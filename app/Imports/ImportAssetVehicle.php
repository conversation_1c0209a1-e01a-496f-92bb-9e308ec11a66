<?php

namespace App\Imports;

use App\Http\Helper;
use DB;
use App\Models\Fleet\Asset;
use App\Models\Fleet\AssetDetail;
use App\Models\Fleet\CategoryAsset;
use App\Models\Fleet\CategoryItem;
use App\Models\Fleet\Company;
use App\Models\Fleet\Department;
use App\Models\Fleet\DetailLocation;
use App\Models\Fleet\MasterLocation;
use App\Models\Fleet\Section;
use App\Models\Fleet\TypeItem;
use App\Models\Fleet\UploadDoc;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithStartRow;

class ImportAssetVehicle implements ToCollection, WithStartRow
{
   public function startRow(): int
   {
        return 2;
   }

   public function collection(Collection $collection)
   {
       DB::transaction(function () use ($collection) {
            foreach ($collection as $key => $item) {
                if(!$item[1]) {
                    continue;
                }

                $company = Company::where('name','ilike','%' . $item[2] . '%')->first();
                if($company) {
                    $asset_detail = AssetDetail::where(['value' => $item[7], 'attribute_code' => 'nomor_polisi'])->first();
                    if($asset_detail) {
                        $asset = new Asset;
                        $asset->id = $asset_detail->asset_id;
                        $asset->category_asset_id = 3;
                        $asset->code = $item[1];
                        $asset->company = $item[2];
                        $asset->company_id = $company->id;
                        $asset->save();
                    }
                }
            }

            return true;
        });
   }
}
