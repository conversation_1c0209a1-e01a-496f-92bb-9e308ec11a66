<?php

namespace App\Http;

use App\Models\Fleet\AnswerQuestionDetail;
use App\Models\Fleet\Company;
use App\Models\Fleet\Department;
use App\Models\Fleet\MasterLocation;
use App\Models\Fleet\RequestForm;

class Helper
{
    
    public static function fresh_aprice($price)
    {
        return strtolower(preg_replace("/[^0-9]/", "", $price));
    }

    public static function status_k3($value = null)
    {
        if (!$value) {
            return null;
        }else {
            if ($value == 1) {
                return 'Tunggu 30 Menit';
            }elseif ($value == 2) {
                return 'Pulangkan';
            }elseif ($value == 3) {
                return 'Boleh jalan dengan keterangan';
            }elseif ($value == 4) {
                return 'Note';
            }
        }
    }

    public static function status_koordinator($value)
    {
        if ($value == 0) {
            return 'Ajukan Ke Bengkel';
        }elseif ($value == 1) {
            return 'Biarkan Jalan';
        }
    }

    public static function getDetailAnswer($answer_question_user_id, $question_id)
    {
        $aqd = AnswerQuestionDetail::where('answer_question_user_id', $answer_question_user_id)->where('question_id', $question_id)->first();
        
        return $aqd;
    }

    public static function vehicle_trip($value)
    {
        if ($value == 0) {
            return 'Dalam Kota';
        }elseif ($value == 1) {
            return 'Luar Kota';
        }
    }

    public static function keputusan_ga_ho($value)
    {
        if ($value == 0) {
            return 'Tolak Pengajuan';
        }elseif ($value == 1) {
            return 'Bengkel Internal';
        }elseif ($value == 2) {
            return 'Bengkel External';
        }
    }

    public static function keputusan_ga_ho_final($value)
    {
        if ($value == 1) {
            return 'Selesai';
        }elseif ($value == 2) {
            return 'Service Ulang';
        }
    }

    public static function saveImage($image, $file_path)
    {
        $image_parts = explode(";base64,", $image);
        if ($image_parts) {
            $image_type_aux = explode("image/", $image_parts[0]);
            $image_type = $image_type_aux[1];
            if ($image_type == 'jpg' || $image_type == 'jpeg' || $image_type == 'png') {
                $image_base64 = base64_decode($image_parts[1]);
                $folderPath = 'storage/'.$file_path.'/';
                $imageName = uniqid();
                $imageFullPath = $folderPath.$imageName.".".$image_type;
                file_put_contents($imageFullPath, $image_base64);
                return $imageFullPath;
            }else{
                return "Format gambar tidak sesuai, Tipe gambar hanya jpg, jpeg, png";
            }
        }
    }

    public static function statusAccident($value)
    {
        if ($value == 0) {
            return 'Menunggu Proses Klaim';
        }elseif ($value == 1) {
            return 'Melengkapi Dokumen';
        }elseif ($value == 2) {
            return 'Menunggu Survei';
        }elseif ($value == 3) {
            return 'Menunggu di Tarik Bengkel';
        }elseif ($value == 4) {
            return 'Menunggu SPK';
        }elseif ($value == 5) {
            return 'Proses Perbaikan';
        }elseif ($value == 6) {
            return 'Finish';
        }
    }

    public static function saveFile($file, $file_path)
    {
        $file_parts = explode(";base64,", $file);
        $file_type = explode('/', mime_content_type($file))[1];

        if ($file_type == 'vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
            $file_type = 'xlsx';
        }
        
        $files = base64_decode($file_parts[1]);
        $folderPath = 'storage/'.$file_path.'/';
        $fileName = uniqid();
        $fileFullPath = $folderPath.$fileName.".".$file_type;
        file_put_contents($fileFullPath, $files);
        return $fileFullPath;
    }

    public static function genCode($company_id, $department_id, $location_id, $prefix = null){
        $company = Company::find($company_id);
        $dept = Department::find($department_id);
        $loc = MasterLocation::find($location_id);
        $code = $prefix.$company->code_number.($dept->code_id ?? 00).($loc->code_id ?? 00).now()->format('my');
        // return $code;
        
        $countSame = null;
        if ($prefix == 'RF') {
            $countSame = RequestForm::where('no_rf', 'like', '%'.$code.'%')->count();
        }elseif ($prefix == 'PR') {
            $countSame = RequestForm::where('no_pr', 'like', '%'.$code.'%')->count();
        }

        $sufix = substr('000'.($countSame + 1), -3);
        $result = $code."-".$sufix;

        return $result;
    }
}
