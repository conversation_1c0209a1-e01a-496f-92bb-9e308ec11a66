<?php

namespace App\Http\Controllers\FleetMaster;

use App\FleetMaster\VehicleEngine;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class EngineController extends Controller
{
    public function index()
    {
        $data = VehicleEngine::orderBy("name", "asc")->get(['id', "name", "code"]);
        return response()->json($data);
    }

    public function store(Request $request, $condition)
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'required',
            ]);

            if ($validator->fails()) {
                return response()->json($validator->errors(), 500);
            }
            $condition == 'create' ? $data = new VehicleEngine() : $data = VehicleEngine::findOrFail($condition);
            $data->name = $request->name;
            $request->code ? $data->code = $request->code : true;
            $data->save();
            return response()->json(['success' => true, 'message' => 'Berhasil input data']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    public function delete($id)
    {
        try {
            VehicleEngine::findOrFail($id)->delete();
            return response()->json(['success' => true, 'message' => 'Berhasil delete data']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $data = VehicleEngine::find($id);
        return response()->json($data);
    }
}
