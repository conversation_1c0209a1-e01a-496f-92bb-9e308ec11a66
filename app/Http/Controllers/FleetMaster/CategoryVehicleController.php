<?php

namespace App\Http\Controllers\FleetMaster;

use App\FleetMaster\VehicleCategory;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class CategoryVehicleController extends Controller
{
    public function index()
    {
        $data = VehicleCategory::orderBy("name", "asc")->get(['id', "name", "code","jumlah_kursi","icon"]);
        return response()->json($data);
    }

    public function store(Request $request, $condition)
    {
        try { 
 
            $validator = Validator::make($request->all(), [
                'name' => 'required',
                'jumlah_kursi' => 'required',  
            ]);

            if ($validator->fails()) {
                return response()->json($validator->errors(), 500);
            }
            
            $condition == 'create' ? $data = new VehicleCategory() : $data = VehicleCategory::findOrFail($condition);
            $data->name = $request->name;
            $data->jumlah_kursi = $request->jumlah_kursi;
            $request->code ? $data->code = $request->code : true;

            if ($request->hasFile('icon')) {
                $request->icon ? $data->icon = $this->uploadImage($request, 'icon', 'vehicle/category') : null;
            }  

            $data->save();
            return response()->json(['success' => true, 'message' => 'Berhasil input data']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    public function delete($id)
    {
        try {
            VehicleCategory::findOrFail($id)->delete();
            return response()->json(['success' => true, 'message' => 'Berhasil delete data']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $data = VehicleCategory::find($id);
        return response()->json($data);
    }
}
