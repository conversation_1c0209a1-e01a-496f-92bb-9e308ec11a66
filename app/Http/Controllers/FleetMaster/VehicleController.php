<?php

namespace App\Http\Controllers\FleetMaster;

use App\FleetMaster\Vehicle;
use App\FleetMaster\VehicleCategory;
use App\FleetMaster\VehicleColor;
use App\FleetMaster\VehicleDriver;
use App\FleetMaster\VehicleEngine;
use App\FleetMaster\VehicleMerk;
use App\FleetMaster\VehicleKmActual;
use App\FleetMaster\VehicleKir;
use App\FleetMaster\VehicleService;
use App\Workshop\Insurance;
use App\Workshop\InsuranceType;
use App\Workshop\WorkshopType;
use App\Http\Controllers\Controller;
use App\Models\Fleet\AnswerQuestionUser;
use App\Models\Fleet\Asset;
use App\Models\Fleet\Branch;
use App\Models\Fleet\Company;
use App\Models\Fleet\Department;
use App\Models\Fleet\Emergency;
use App\Models\Fleet\MasterLocation;
use App\Models\Fleet\Service;
use App\Models\User;
use App\Workshop\Workshop;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;

class VehicleController extends Controller
{

    public function index(Request $request)
    {
        $company = [];
        if ($request->has('company')) {
            $company_get = $request->input('company');
            if (is_array($company_get)) {
                for ($i=0; $i < count($company_get); $i++) { 
                    $company[] = $company_get[$i];
                }
            }
        }

        $departement = [];
        if ($request->has('departement')) {
            $departement_get = $request->input('departement');
            if (is_array($departement_get)) {
                for ($i=0; $i < count($departement_get); $i++) { 
                    $departement[] = $departement_get[$i];
                }
            }
        }

        $location = [];
        if ($request->has('location')) {
            $location_get = $request->input('location');
            if (is_array($location_get)) {
                for ($i=0; $i < count($location_get); $i++) { 
                    $location[] = $location_get[$i];
                }
            }
        }

        $companies = Company::select('name','id')->get();
        $departments = Department::select('name','id')->get();
        $locations = MasterLocation::select('name','id')->get();

        $assets = Asset::with(['categoryAsset', 'typeAsset', 'assetDetail', 'insurance'])
        ->where(function($q) use($company,$location,$departement){
            if (is_array($company) && count($company)) {
                $q->whereIn('company_id',$company);
            }
            if (is_array($location) && count($location)) {
                $q->whereIn('location_id',$location);
            }
            if (is_array($departement) && count($departement)) {
                $q->whereIn('department_id',$departement);
            }
        })->orderBy('id','desc')->get();

        foreach ($assets as $key => $value) {
            if ($request->show_qr == 1){
                $value->show_qr = true;
            }else{
                $value->show_qr = false;
            }
            $value->show_foto_stnk = false;
            $value->show_foto_pajak_stnk = false;
            $value->show_foto_barcode_kir = false;
            $value->show_foto_kartu_kir = false;
            $value->show_foto_kendaraan_depan = false;
            $value->show_foto_kendaraan_samping_kanan = false;
            $value->show_foto_kendaraan_samping_kiri = false;
            $value->show_foto_belakang_kendaraan = false;
            $value->show_foto_dalam_box = false;
            $value->show_foto_kabin_kendaraan = false;
            foreach ($value->assetDetail as $ckey => $cvalue) {
                $value[$cvalue->attribute_code] = $cvalue->value;
            }
        }

        return response()->json(['assets' => $assets, 'companies' => $companies, 'departments' => $departments, 'locations' => $locations]);
    }

    // public function index()
    // {
    //     // $data = Vehicle::with('category', 'merk', 'color')->orderBy("id", "desc")->get();
    //     // $driver = User::where("type_account", "driver")->orderBy("first_name", "asc")->get(['id', 'username']);
    //     // $item = array();
    //     // foreach ($data as $d) {
    //     //     if ($d->service_status == 0) {
    //     //         $status = "Tidak";
    //     //     } else {
    //     //         $status = "Iya";
    //     //     }

    //     //     $list['image']  = asset($d->image);
    //     //     $list['category']   = $d->category->name ?? '';
    //     //     $list['merk']   = $d->merk->name ?? '';
    //     //     $list['color']  = $d->color->name ?? '';
    //     //     $list['id']     = $d->id;
    //     //     $list['license_no'] = $d->license_no;
    //     //     $list['minimum_sim'] = $d->minimum_sim;
    //     //     $list['qr_code'] = $d->qr_code;
    //     //     $list['status'] = $status;
    //     //     $item[]     = $list;
    //     // }

    //     // $user = Auth::user();

    //     // return response()->json([
    //     //     'kendaraan' => $item, 'driver' => $driver, 'user' => $user
    //     // ], 200);

    //     $asset = Asset::orderBy('id', 'desc')->get();
    //     foreach ($asset as $key => $value) {
    //         $value['show_qr'] = false;
    //         $value['jenis_asset'] = 'VEHICLE';
    //         foreach ($value->assetDetail as $ckey => $cvalue) {
    //             $value[$cvalue->attribute_code] = $cvalue->value;
    //         }
    //     }

    //     return $asset;
    // }

    public function create()
    {
        $data = [
            'category'  => VehicleCategory::where("status", 1)->get(['id', 'name']),
            'merk'      => VehicleMerk::get(['id', 'name']),
            'engine'    => VehicleEngine::get(['id', 'name']),
            'color'     => VehicleColor::get(['id', 'name']),
            'branch'    => Branch::get(['id', 'name', 'gst_no']),
            'company'   => Company::get(['id', 'name', 'gst_no']),
            'insurance' => Insurance::get(['id', 'name']),
            'insurance_type' => InsuranceType::get(['id', 'name'])
        ];

        return response()->json($data, 200);
    }


    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'vehicle_category_id' => 'required',
            'vehicle_color_id' => 'required',
            'vehicle_engine_id' => 'required',
            'vehicle_merk_id' => 'required',
            'tenaga_cc' => 'required',
            'tahun_kendaraan' => 'required',
            'license_no' => 'required',
            'license_expire_date' => 'required',
            'reg_expire_date' => 'required',
            'status' => 'required',
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 500);
        }

        try {
            $data = $request->all();
            
            // if ($request->hasFile('image')) {
            //     $data['image'] = $this->uploadImage($request, 'image', 'vehicle/kendaraan');
            // }
            
            $image_parts = explode(";base64,", $request->stnk_file);
            if ($image_parts) {
                $image_type_aux = explode("image/", $image_parts[0]);
                $image_type = $image_type_aux[1];
                if ($image_type == 'jpg' || $image_type == 'jpeg' || $image_type == 'png') {
                    $image_base64 = base64_decode($image_parts[1]);
                    $folderPath = 'storage/stnk_file/';
                    $imageName = uniqid();
                    $imageFullPath = $folderPath.$imageName.".".$image_type;
                    file_put_contents($imageFullPath, $image_base64);
                    $data['stnk_file'] = $imageFullPath;
                }else{
                    return "Format gambar tidak sesuai, Tipe gambar hanya jpg, jpeg, png";
                }
            }

            $image_parts = explode(";base64,", $request->image);
            if ($image_parts) {
                $image_type_aux = explode("image/", $image_parts[0]);
                $image_type = $image_type_aux[1];
                if ($image_type == 'jpg' || $image_type == 'jpeg' || $image_type == 'png') {
                    $image_base64 = base64_decode($image_parts[1]);
                    $folderPath = 'storage/image_mobil/';
                    $imageName = uniqid();
                    $imageFullPath = $folderPath.$imageName.".".$image_type;
                    file_put_contents($imageFullPath, $image_base64);
                    $data['image'] = $imageFullPath;
                }else{
                    return "Format gambar tidak sesuai, Tipe gambar hanya jpg, jpeg, png";
                }
            }
    
            $vehicle = Vehicle::create($data);

            $vhicl['qr_code'] = 'VHCL00'.$vehicle->id;
            $vhcl = Vehicle::findOrFail($vehicle->id)->update($vhicl);
            return response()->json(['success' => true, 'message' => 'Berhasil input data']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    public function update(Request $request, $id)
    {
        // $validator = Validator::make($request->all(), [
        //     'vehicle_category_id' => 'required',
        //     'vehicle_color_id' => 'required',
        //     'vehicle_engine_id' => 'required',
        //     'vehicle_merk_id' => 'required',
        //     'tenaga_cc' => 'required',
        //     'tahun_kendaraan' => 'required',
        //     'license_no' => 'required',
        //     'license_expire_date' => 'required',
        //     'reg_expire_date' => 'required',
        //     'status' => 'required',
        // ]);

        // if ($validator->fails()) {
        //     return response()->json($validator->errors(), 500);
        // }

        $data = $request->all();
        // if () {
        //     $data['image'] = $this->uploadImage($request, 'image', 'vehicle/kendaraan');
        // }
        if ($request->stnk_file) {
            $image_parts = explode(";base64,", $request->stnk_file);
            if ($image_parts) {
                $image_type_aux = explode("image/", $image_parts[0]);
                $image_type = $image_type_aux[1];
                if ($image_type == 'jpg' || $image_type == 'jpeg' || $image_type == 'png') {
                    $image_base64 = base64_decode($image_parts[1]);
                    $folderPath = 'storage/stnk_file/';
                    $imageName = uniqid();
                    $imageFullPath = $folderPath.$imageName.".".$image_type;
                    file_put_contents($imageFullPath, $image_base64);
                    $data['stnk_file'] = $imageFullPath;
                }else{
                    return "Format gambar tidak sesuai, Tipe gambar hanya jpg, jpeg, png";
                }
            }
        }else{
            unset($data['stnk_file']);
            // request()->request->remove('');
        }
        // dd($data);
        $vehicle = Vehicle::findOrFail($id)->update($data);
        return response()->json(['success' => true, 'message' => 'Berhasil update data']);
        try {
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    public function store_driver(Request $request, $condition)
    {
        try {

            $validator = Validator::make($request->all(), [
                'vehicle_id' => 'required',
                'driver_id' => 'required',
                'start_date' => 'required'
            ]);

            if ($validator->fails()) {
                return response()->json($validator->errors(), 500);
            }

            $getData = VehicleDriver::where("vehicle_id",$request->vehicle_id)->where("driver_id",$request->driver_id)->count();

            if($condition == 'create') {
                if($getData > 0) {
                    return response()->json(['success' => false, 'message' => 'Maaf, Driver ini sudah ditambahkan di kendaraan ini']);
                }
            } else {
                if($getData >= 1) {
                    return response()->json(['success' => false, 'message' => 'Maaf, Driver ini sudah ditambahkan di kendaraan ini']);
                }
            }
            $condition == 'create' ? $data = new VehicleDriver() : $data = VehicleDriver::findOrFail($condition);
            $data->vehicle_id = $request->vehicle_id;
            $data->driver_id = $request->driver_id;
            $data->start_date = $request->start_date;

            $request->note ? $data->note = $request->note : true;
            $data->save();
            return response()->json(['success' => 'yes', 'message' => 'Berhasil Menambahkan Driver']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    public function drivers($id)
    {
        $data = Vehicle::findOrFail($id);
        $list = array();
        foreach ($data->driver as $d) {
            $item['first_name']    = $d->driver->first_name ?? '';
            $item['last_name']    = $d->driver->last_name ?? '';
            $item['username']    = $d->driver->username ?? '';
            $item['id']    = $d->id;
            $item['start_date']    = $d->start_date;
            $item['note']    = $d->note;
            $item['vehicle']    = $d->vehicle->license_no ?? '';
            $item['driver_id']  = $d->driver_id;
            $item['vehicle_id'] = $d->vehicle_id;
            $list[] = $item;
        }

        return response()->json($list);
    }

    public function removeDriver($id)
    {
        try {
            VehicleDriver::findOrFail($id)->delete();
            return response()->json(['success' => true, 'message' => 'Berhasil delete data']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    public function edit($id)
    {
        $data = Vehicle::find($id);
        return response()->json($data);
    }

    public function delete($id)
    {
        try {
            Vehicle::findOrFail($id)->delete();
            return response()->json(['success' => true, 'message' => 'Berhasil delete data']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    public function kmActual($id)
    {
        $data_vehicle = Vehicle::with('actual')->find($id);

        return response()->json(['data' => $data_vehicle]);
    }

    public function kmActualStore(Request $request,$id)
    {
        $data = new VehicleKmActual;
        $data->id_vehicle = $id;
        $data->km_actual = $request->input('km_actual');
        $data->tanggal = $request->input('tanggal');
        $data->save();

        return response()->json(['success' => true, 'message' => 'Berhasil input data']);
    }

    public function kmActualDelete($id)
    {
        try {
            VehicleKmActual::findOrFail($id)->delete();
            return response()->json(['success' => true, 'message' => 'Berhasil delete data']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    public function kir($id)
    {
        $data_vehicle = Vehicle::with('kir')->find($id);
        $link = asset('');

        return response()->json(['data' => $data_vehicle, 'link' => $link]);
    }

    public function kirStore(Request $request,$id)
    {
        $data = new VehicleKir;
        $data->id_vehicle = $id;

        $image_parts = explode(";base64,", $request->document);
        if ($image_parts) {
            $image_type_aux = explode("image/", $image_parts[0]);
            $image_type = $image_type_aux[1];
            if ($image_type == 'jpg' || $image_type == 'jpeg' || $image_type == 'png') {
                $image_base64 = base64_decode($image_parts[1]);
                $folderPath = 'storage/kir_file/';
                $imageName = uniqid();
                $imageFullPath = $folderPath.$imageName.".".$image_type;
                file_put_contents($imageFullPath, $image_base64);
                $data->document = $imageFullPath;
            }else{
                return "Format gambar tidak sesuai, Tipe gambar hanya jpg, jpeg, png";
            }
        }

        $data->nomor_kir = $request->input('nomor_kir');
        $data->tahun_kir = $request->input('tahun_kir');
        $data->expired = $request->input('expired');
        $data->note = $request->input('note');
        $data->save();

        return response()->json(['success' => true, 'message' => 'Berhasil input data']);
    }

    public function kirDelete($id)
    {
        try {
            VehicleKir::findOrFail($id)->delete();
            return response()->json(['success' => true, 'message' => 'Berhasil delete data']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    // public function service($id)
    // {
    //     $data_vehicle = Vehicle::with('service.type')->find($id);
    //     $workshop_type = WorkshopType::all();

    //     return response()->json(['data' => $data_vehicle, 'workshop_type' => $workshop_type]);
    // }

    public function serviceStore(Request $request,$id)
    {
        $data = new VehicleService;
        $data->id_vehicle = $id;
        $data->workshop_type_id = $request->input('workshop_type_id');
        $data->kilometer = $request->input('kilometer');
        $data->jumlah_hari = $request->input('jumlah_hari');
        $data->save();

        return response()->json(['success' => true, 'message' => 'Berhasil input data']);
    }

    public function serviceDelete($id)
    {
        try {
            VehicleService::findOrFail($id)->delete();
            return response()->json(['success' => true, 'message' => 'Berhasil delete data']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    public function emergency(Request $request)
    {
        try{
            $data = $request->all();
            $long = $request->long;
            $lat = $request->lat;
            if ($long != null&& $lat!= null) {
                $kendaraan_last = AnswerQuestionUser::with('statusVehicle')->where('user_id', Auth::user()->id)->where('slug','checklist-vehicle')->whereNotNull('status_vehicle_id')->orderBy('id','desc')->first();
                if ($kendaraan_last) {
                    $data['driver_id'] = $kendaraan_last->user_id;
                    $data['asset_id'] = $kendaraan_last->asset_id;
                    $data['status'] = 0;
                    $emergency = Emergency::create($data);
        
                    $dataWorkshop = new Workshop();
                    $dataWorkshop->no_seri = 'LJR-'.now()->format('YmdHis').rand(10, 99);
                    $dataWorkshop->asset_id = $kendaraan_last->asset_id;
                    $dataWorkshop->type = 'Emergency';
                    $dataWorkshop->user_id = Auth::user()->id;
                    $dataWorkshop->driver_id = $kendaraan_last->user_id;
                    $dataWorkshop->status_perbaikan_id = 1;
                    $dataWorkshop->emergency_id = $emergency->id;
                    $dataWorkshop->answer_question_user_id = $kendaraan_last->id;
                    $dataWorkshop->km_service = Asset::findOrFail($kendaraan_last->asset_id)->km_actual;
                    $dataWorkshop->save();

                    $asset = Asset::findOrFail($kendaraan_last->asset_id);
                    $asset->maintenance = 1;
                    $asset->save();
                    
                    return response()->json(['success' => true, 'message' => 'Pengajuan Emergency Sudah Terkirim!']);
                }else{
                    return response()->json(['success' => false, 'message' => 'Data kendaraan '.Auth::user()->full_name." Tidak Ditemukan"]);
                }
            }else{
                return response()->json(['success' => false, 'message' => "Izinkan lokasi Anda untuk mengakses fitur ini"]);
            }
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 200);
        }
    }

    public function service(Request $request)
    {
        try{
            $long = $request->long;
            $lat = $request->lat;
            if ($long != null&& $lat!= null) {
                $data = $request->all();
                $kendaraan_last = AnswerQuestionUser::with('statusVehicle')->where('user_id', Auth::user()->id)->where('slug','checklist-vehicle')->whereNotNull('status_vehicle_id')->orderBy('id','desc')->first();
                if ($kendaraan_last) {
                    $data['driver_id'] = $kendaraan_last->user_id;
                    $data['asset_id'] = $kendaraan_last->asset_id;
                    $data['status'] = 0;
                    $emergency = Service::create($data);
        
                    $dataWorkshop = new Workshop();
                    $dataWorkshop->no_seri = 'LJR-'.now()->format('YmdHis').rand(10, 99);
                    $dataWorkshop->asset_id = $kendaraan_last->asset_id;
                    $dataWorkshop->type = 'Service';
                    $dataWorkshop->user_id = Auth::user()->id;
                    $dataWorkshop->driver_id = $kendaraan_last->user_id;
                    $dataWorkshop->status_perbaikan_id = 1;
                    $dataWorkshop->emergency_id = $emergency->id;
                    $dataWorkshop->answer_question_user_id = $kendaraan_last->id;
                    $dataWorkshop->km_service = Asset::findOrFail($kendaraan_last->asset_id)->km_actual;
                    $dataWorkshop->save();

                    $asset = Asset::findOrFail($kendaraan_last->asset_id);
                    $asset->maintenance = 1;
                    $asset->save();
                    
                    return response()->json(['success' => true, 'message' => 'Pengajuan Service Sudah Terkirim!']);
                }else{
                    return response()->json(['success' => false, 'message' => 'Data kendaraan '.Auth::user()->full_name." Tidak Ditemukan"]);
                }
            }else{
                return response()->json(['success' => false, 'message' => "Izinkan lokasi Anda untuk mengakses fitur ini"]);
            }
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 200);
        }
    }
}
