<?php

namespace App\Http\Controllers\Workshop;

use App\Exports\ExportPerawatanKendaraan;
use App\FleetMaster\Vehicle;
use App\Http\Controllers\Controller;
use App\Http\Helper;
use App\Models\User;
use App\Transaction\PurchaseItem;
use App\Transaction\Transaction;
use App\Workshop\Workshop;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Facades\Excel;

class PengajuanController extends Controller
{
    public function index()
    {
        $list_pengajuan = Workshop::with("vehicle", "driver", "technical")->orderBy("id", "desc")->get();
        $technical =  User::where("type_account", "technical")->orderBy("first_name", "asc")->get(['id', 'username']);

        $list = array();
        foreach ($list_pengajuan as $k) {
            $type = $k->vehicle->category->name ?? '';
            $color = $k->vehicle->color->name ?? '';
            $license_no = $k->vehicle->license_no ?? '';

            $item['id'] = $k->id;
            $item['kendaraan_name']   = $type . ' Warna ' . $color . '- Platno ' . $license_no;
            $item['driver_name']  = $k->driver->first_name.' '. $k->driver->last_name?? '';
            $item['detail'] = $k->detail;
            $item['kategori_kerusakan_id'] = $k->kategoriKerusakan->name ?? '-';
            $item['status'] = $k->status;
            $item['approval_by'] = $k->technical->username ?? '-';
            $list[]     = $item;
        }
        // dd($list);
        $data = [
            'list'  => $list,
            'user'  => $technical
        ];

        return response()->json($data, 200);
    }

    public function create()
    {
        $kendaraan = Vehicle::all();
        $list = array();
        foreach ($kendaraan as $k) {
            $item['id'] = $k->id;
            $item['type']   = $k->category->name ?? '';
            $item['color']  = $k->color->name ?? '';
            $item['platno'] = $k->license_no;
            $list[]     = $item;
        }
        $data = [
            'vehicle'  => $list,
        ];

        return response()->json($data, 200);
    }

    public function elements($id)
    {
        $pengajuan = Workshop::findOrFail($id);
        $technical =  User::where("type_account", "technical")->orderBy("first_name", "asc")->get(['id', 'username']);

        $data = [
            'pengajuan'  => $pengajuan,
            'technical' => $technical
        ];

        return response()->json($data, 200);
    }

    public function approval($id)
    {
        $kendaraan = Vehicle::all();
        $list = array();
        foreach ($kendaraan as $k) {
            $item['id'] = $k->id;
            $item['type']   = $k->category->name ?? '';
            $item['color']  = $k->color->name ?? '';
            $item['platno'] = $k->license_no;
            $list[]     = $item;
        }
        $data = [
            'vehicle'  => $list,
        ];

        return response()->json($data, 200);
    }

    public function edit($id)
    {
        $data = Workshop::find($id);
        return response()->json($data);
    }

    public function getDriver($id)
    {
        $kendaraan = Vehicle::findOrFail($id);
        $list = array();
        foreach ($kendaraan->driver as $k) {
            $item['id'] = $k->driver_id;
            $item['name']   = $k->driver->username ?? '';
            $list[]     = $item;
        }
        return response()->json(['supir' => $list], 200);
    }

    public function store(Request $request, $condition)
    {
        try {

            $validator = Validator::make($request->all(), [
                'driver_id' => 'required',
                'vehicle_id' => 'required',
            ]);

            if ($validator->fails()) {
                return response()->json($validator->errors(), 500);
            }

            $condition == 'create' ? $data = new Workshop() : $data = Workshop::findOrFail($condition);
            $data->vehicle_id = $request->vehicle_id;
            $data->driver_id = $request->driver_id;
            $data->kategori_kerusakan_id = $request->kategori_kerusakan_id;
            $request->detail ? $data->detail = $request->detail : true;
            $data->save();

            $vehilce = Vehicle::findOrFail($request->vehicle_id);
            $vehilce->maintenance = 1;
            $vehilce->save();

            return response()->json(['success' => true, 'message' => 'Berhasil input data', 'id' => $data->id]);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    public function approval_store(Request $request)
    {
        try {

            $validator = Validator::make($request->all(), [
                'workshop_id' => 'required',
                'technical_id' => 'required',
                'type'          => 'required',
                'status_take'       => 'required',
                'status'        => 'required'
            ]);

            if ($validator->fails()) {
                return response()->json($validator->errors(), 500);
            }

            $data = Workshop::findOrFail($request->workshop_id);
            $data->approval_by = $request->technical_id;
            $data->status = 'approval';
            $request->note ? $data->note = $request->note : true;
            $data->save();

            $transaction = new Transaction();
            $transaction->transaction_type = 'workshop';
            $transaction->workshop_type = $request->type;
            $request->estimation_date ? $transaction->estimation_date = $request->estimation_date : true;
            $request->other_change ? $transaction->other_change = Helper::fresh_aprice($request->other_change) : true;
            $transaction->status_take = $request->status_take;
            $transaction->status = $request->status;
            $transaction->workshop_id = $data->id;
            $transaction->save();

            $subtotal = 0;

            dd($request->all());
            if (isset($request->item_name)) {
                $num = count($request->item_name);
                for ($x = 0; $x < $num; $x++) {
                    $detail = new PurchaseItem();
                    $detail->transaction_id = $transaction->id;
                    if($request->item_price) {
                        $subtotal += $request->item_price[$x];
                    }
                    $request->item_name[$x] ? $detail->name = $request->item_name[$x] : true;
                    $request->item_note[$x] ? $detail->note = $request->item_note[$x] : true;
                    $request->item_price[$x] ? $detail->price = Helper::fresh_aprice($request->item_price[$x]) : true;
                    $request->item_qty[$x] ? $detail->qty = $request->item_qty[$x] : true;
                    $detail->save();
                }
            }

            $update = Transaction::findOrFail($transaction->id);
            $update->subtotal = $subtotal;
            $update->final_total = $subtotal + $transaction->other_change;
            $update->save();

            return response()->json(['success' => true, 'message' => 'Berhasil input data']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    public function rejected(Request $request)
    {
        try {

            $validator = Validator::make($request->all(), [
                'id' => 'required',
                'technical_id' => 'required',
            ]);

            if ($validator->fails()) {
                return response()->json($validator->errors(), 500);
            }

            $data = Workshop::findOrFail($request->id);
            $data->approval_by = $request->technical_id;
            $data->status = "rejected";
            $request->note ? $data->note = $request->note : true;
            $data->save();
            return response()->json(['success' => true, 'message' => 'Berhasil input data']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    public function show($id)
    {
        $k = Workshop::findOrFail($id);

        $type = $k->vehicle->category->name ?? '';
        $color = $k->vehicle->color->name ?? '';

        if ($k->status == 'pending') {
            $status = "Menunggu Pengecekan Technical";
        } else if ($k->status == 'approval') {
            $status = "Disetujui Dan Masuk Ke Perbaikan";
        } else {
            $status = "Pengajuan Ditolak";
        }

        $data['driver_name']    = $k->driver->username ?? '';
        $data['vehicle_name']   = $type . ' Warna ' . $color . '- Platno ' . $k->vehicle->license_no;
        $data['status']    = $status;
        $data['detail']    = $k->detail;
        $data['approvalby']    = $k->technical->username ?? '';
        $data['note']    = $k->note;
        return response()->json($data, 200);
    }

    public function delete($id)
    {
        try {
            Workshop::findOrFail($id)->delete();
            return response()->json(['success' => true, 'message' => 'Berhasil delete data']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    public function perawatanKendaraan()
    {
        return Excel::download(new ExportPerawatanKendaraan(), 'perawatan kendaraan.xlsx');
        // try {
            return view('export.formPerawatanKendaraan');
        //     // return response()->json(['success' => true, 'message' => 'Berhasil delete data', 'template' => $template]);
        // } catch (\Exception $e) {
        //     return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        // }
    }
}
