<?php

namespace App\Http\Controllers\Technical;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;

class TechnicalController extends Controller
{
    public function index()
    {
        $data = User::with(['company','department','branch','section','subSection'])->where('role','Technical')->orderBy('id','desc')->get();
        return response()->json($data);
    } 

    public function edit($id)
    {
        $data = User::find($id);

        $detail = [
            'firstname' => $data->first_name,
            'lastname'  => $data->last_name,
            'username'  => $data->username,
            'email'     => $data->email,
            'gender'    => $data->gender,
            'join_date' => $data->join_date,
            'address'   => $data->address,
            'phone'  => $data->phone,
            'photo'  => $data->photo
        ];
        return response()->json($detail);
    }

    public function store(Request $request)
    {
        try {

            $validator = Validator::make($request->all(), [
                'username' => 'required|unique:users,username',
                'firstname'      => 'required',
                'email'     => 'required|email|unique:users,email',
                'password'  => 'required',
                'gender'  => 'required',
                'join_date'   => 'required',
            ]);

            if ($validator->fails()) {
                return response()->json($validator->errors(), 500);
            }

            $data = new User();
            $data->first_name = $request->firstname;
            $data->username = $request->username;
            $data->email = $request->email;
            $data->password = Hash::make($request->password);
            $data->gender = $request->gender;
            $data->join_date = $request->join_date;
            $data->type_account = 'technical';

            $request->lastname ? $data->last_name = $request->lastname : true;
            $request->address ? $data->address = $request->address : true;
            $request->phone ? $data->phone = $request->phone : true;

            if ($request->hasFile('photo')) {
                $request->photo ? $data->photo = $this->uploadImage($request, 'photo', 'users/driver') : null;
            }

            $data->save();
            return response()->json(['success' => true, 'message' => 'Berhasil input data']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    public function update(Request $request, $id)
    {
        try {

            $validator = Validator::make($request->all(), [
                'username'     => 'required|unique:users,username,' . $request->id,
                'firstname'      => 'required',
                'email'     => 'required|email|unique:users,email,' . $request->id,
                'gender'  => 'required',
                'join_date'   => 'required',
            ]);

            if ($validator->fails()) {
                return response()->json($validator->errors(), 500);
            }

            $data = User::findOrFail($id);
            $data->first_name = $request->firstname;
            $data->username = $request->username;
            $data->email = $request->email;
            $data->gender = $request->gender;
            $data->join_date = $request->join_date;
            $data->type_account = 'technical';

            $request->password ? $data->password = Hash::make($request->password) : true;
            $request->lastname ? $data->last_name = $request->lastname : true;
            $request->address ? $data->address = $request->address : true;
            $request->phone ? $data->phone = $request->phone : true;

            if ($request->hasFile('photo')) {
                $request->photo ? $data->photo = $this->uploadImage($request, 'photo', 'users/driver') : null;
            }

            $data->save();
            return response()->json(['success' => true, 'message' => 'Berhasil edit data']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    public function delete($id)
    {
        try {
            User::findOrFail($id)->delete();
            return response()->json(['success' => true, 'message' => 'Berhasil delete data']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }
}
