<?php

namespace App\Http\Controllers\Fleet;

use App\FleetMaster\Vehicle;
use App\Http\Controllers\Controller;
use App\Models\Fleet\MasterVehicle;
use Illuminate\Http\Request;

class MasterVehicleController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $data = MasterVehicle::with('category','color','engine','merk', 'driver')->orderBy('id','desc')->get();
        return response()->json($data);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        // $vendor_types = VendorType::all();
        // $currencies = Currency::all();
        // $payment_terms = PaymentTerm::all();
        // $tax_groups = TaxGroup::all();
        // $factor_companies = FactorCompany::all();

        // return response()->json(['vendor_types' => $vendor_types, 'currencies' => $currencies, 'payment_terms' => $payment_terms, 'tax_groups' => $tax_groups, 'factor_companies' => $factor_companies]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        try{
            $data = $request->all();
            $vendor = MasterVehicle::create($data);
            
            return response()->json(['success' => true, 'message' => 'Berhasil input data']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $data = MasterVehicle::find($id);
        return response()->json($data);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        try{
            $data = $request->all();
            $vendor = MasterVehicle::find($id)->update($data);
            
            return response()->json(['success' => true, 'message' => 'Berhasil update data']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        try{
            $data = MasterVehicle::destroy($id);

            return response()->json(['success' => true, 'message' => 'Berhasil deleted data']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }
}
