<?php

namespace App\Http\Controllers\Fleet;

use App\Http\Controllers\Controller;
use App\Models\Fleet\Location;
use App\Models\Fleet\Nation;
use Illuminate\Http\Request;

class LocationMaintenanceController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $locations = Location::with('departement')->orderBy('id','desc')->get();

        return $locations;
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        // dd($request->all());
        $location = new Location();
        $location->code = $request->get('code');
        $location->name = $request->get('name');
        $location->del_add1 = $request->get('del_add1');
        $location->del_add2 = $request->get('del_add2');
        $location->del_add3 = $request->get('del_add3');
        $location->del_add4 = $request->get('del_add4');
        $location->del_add5 = $request->get('del_add5');
        $location->del_add6 = $request->get('del_add6');
        $location->tel = $request->get('tel');
        $location->fax = $request->get('fax');
        $location->email = $request->get('email');
        $location->contact = $request->get('contact');
        $location->tax_province_id = $request->get('tax_province_id');
        $location->cash_sale_customer = $request->get('cash_sale_customer');
        $location->managed = $request->get('managed');
        $location->cash_sale_branch = $request->get('cash_sale_branch');
        $location->internal_request = $request->get('internal_request');
        $location->departement_id = $request->get('departement_id');
        $location->save();

        return "Create Location Maintenance Successfully";
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $location = Location::with(['departement.division.branch.company','departement.division.branch.company.branch','departement.division.branch.divisi','departement.division.departement'])->findOrFail($id);

        return $location;
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $location = Location::findOrFail($id);
        $location->code = $request->get('code');
        $location->name = $request->get('name');
        $location->del_add1 = $request->get('del_add1');
        $location->del_add2 = $request->get('del_add2');
        $location->del_add3 = $request->get('del_add3');
        $location->del_add4 = $request->get('del_add4');
        $location->del_add5 = $request->get('del_add5');
        $location->del_add6 = $request->get('del_add6');
        $location->tel = $request->get('tel');
        $location->fax = $request->get('fax');
        $location->email = $request->get('email');
        $location->contact = $request->get('contact');
        $location->tax_province_id = $request->get('tax_province_id');
        $location->cash_sale_customer = $request->get('cash_sale_customer');
        $location->managed = $request->get('managed');
        $location->cash_sale_branch = $request->get('cash_sale_branch');
        $location->internal_request = $request->get('internal_request');
        $location->departement_id = $request->get('departement_id');
        $location->save();

        return "Update Location Maintenance Successfully";
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $location = Location::findOrFail($id);
        $location->delete();

        return "Delete Location Maintenance Successfully";
    }

    public function nation()
    {
        $nation = Nation::get();

        return $nation;
    }
}
