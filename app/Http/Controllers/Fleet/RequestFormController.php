<?php

namespace App\Http\Controllers\Fleet;

use App\Exports\ExportRequestForm;
use App\Http\Controllers\Controller;
use App\Http\Helper;
use App\Models\Fleet\Asset;
use App\Models\Fleet\AssetDetail;
use App\Models\Fleet\CategoryItem;
use App\Models\Fleet\Company;
use App\Models\Fleet\Department;
use App\Models\Fleet\LogApproval;
use App\Models\Fleet\MasterLocation;
use App\Models\Fleet\RequestForm;
use App\Models\Fleet\RequestFormDetail;
use App\Models\Fleet\TypeItem;
use App\Models\User;
use App\Workshop\Workshop;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\ExportPurchase;
use App\Models\Fleet\MasterTypeAsset;
use PDF;

use function PHPUnit\Framework\isEmpty;
use function PHPUnit\Framework\isNull;

class RequestFormController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $permission_name = 'list-request-form';
        if($request['approval-rf'] == 1)
            $permission_name = 'approval-rf';
        if($request['date-range'] && $request['rf'])
            $permission_name = 'list-request-form';
        if($request['verivikasi-rf'] == 1)
            $permission_name = 'verifikasi-rf';
        if($request['pr-ga'])
            $permission_name = 'approval-pr-ga';
        if($request['pr-direktur'])
            $permission_name = 'approval-pr-direktur';
        if($request['date-range'] && $request['pr'])
            $permission_name = 'list-pr';
        $permission = Helper::getPermissionUser($permission_name);

        $prNonAset_direktur = Helper::systemSetting('prNonAset_direktur');
        $prJasa_direktur = Helper::systemSetting('prJasa_direktur');
        if ($request->get('date-range') == 1 || $request->has('start_date')) {
            $data = RequestForm::with(['company:id,name', 'department:id,name', 'section:id,name', 'location:id,name', 'workshop', 'user:id,full_name', 'requestFormDetail','logApproval' => function($query) {
                                    return $query->whereIn('slug', ['verified-ga', 'status-approval-pr-ga']);
                                }])
                                ->where(function($query) use($request, $prJasa_direktur, $prNonAset_direktur){
                                    if ($request->get('approval-rf') || $request->get('status') == 1) {
                                        $query->whereNull('status_approval')->whereNull('verified')->whereNull('status_approval_pr_ga');

                                    }else if ($request->get('verivikasi-rf') || $request->get('status') == 3) {
                                        $query->where('status_approval', 1)->whereNull('verified')->whereNull('status_approval_pr_ga');
                                    }else if ($request->get('pr-ga') || $request->get('status') == 4) {
                                        $query->where('status_approval', 1)->where('verified', 1)->whereNull('status_approval_pr_ga');
                                    }else if ($request->get('pr-direktur') || $request->get('status') == 5) {
                                        $query->where('status_approval', 1)
                                                ->where('verified', 1)->where('status_approval_pr_ga', 1)
                                                ->whereNull('status_approval_pr_direktur')
                                                ->whereHas('requestFormDetail', function($query) use ($prNonAset_direktur, $prJasa_direktur) {
                                                    $query
                                                        ->where(function($subQuery) {
                                                            $subQuery->where('type_submission_code', 1);
                                                        })
                                                        ->orWhere(function($subQuery) use ($prNonAset_direktur) {
                                                            $subQuery->where('type_submission_code', 2)
                                                                    ->whereRaw('(estimate_price * qty_request_buy) - budget >= ' . $prNonAset_direktur)
                                                                    ->orWhereRaw('(estimate_price * qty_request_buy) - budget < 0');
                                                        })->orWhere(function($subQuery) use ($prJasa_direktur) {
                                                            $subQuery->where('type_submission_code', 3)
                                                                    ->whereRaw('(estimate_price * qty_request_buy) - budget >= ' . $prJasa_direktur)
                                                                    ->orWhereRaw('(estimate_price * qty_request_buy) - budget < 0');
                                                        });
                                                });
                                    }else if ($request->get('pending')) {
                                        $query->where('status_approval', '<', 6);
                                    }

                                    if ($request->get('pr')) {
                                        $query->where('status_approval', 1)->where('verified', 1)
                                              ->where('status_approval_pr_ga', 1)
                                              ->whereHas('requestFormDetail', function($query) use ($prNonAset_direktur, $prJasa_direktur) {
                                                    $query
                                                    ->where(function($subQuery) {
                                                        $subQuery->where('type_submission_code', 1);
                                                    })
                                                    ->orWhere(function($subQuery) use ($prNonAset_direktur) {
                                                        $subQuery->where('type_submission_code', 2)
                                                                ->whereRaw('(estimate_price * qty_request_buy) - budget >= ' . $prNonAset_direktur)
                                                                ->where('status_approval_pr_direktur', 1)
                                                                ->orWhereRaw('(estimate_price * qty_request_buy) - budget < 0');
                                                    })->orWhere(function($subQuery) {
                                                        $subQuery->where('type_submission_code', 2);
                                                    })->orWhere(function($subQuery) use ($prJasa_direktur) {
                                                        $subQuery->where('type_submission_code', 3)
                                                                ->whereRaw('(estimate_price * qty_request_buy) - budget >= ' . $prJasa_direktur)
                                                                ->where('status_approval_pr_direktur', 1)
                                                                ->orWhereRaw('(estimate_price * qty_request_buy) - budget < 0');
                                                    })->orWhere(function($subQuery) {
                                                        $subQuery->where('type_submission_code', 3);
                                                    });
                                                });

                                    }elseif ($request->get('rf')) {
                                        if ($request->has('status')) {

                                            // PROSES APPROVAL
                                            if ($request->status == 1) {
                                                $query->whereNull('status_approval')->whereNull('verified')->whereNull('status_approval_pr_ga')->whereNull('status_approval_pr_direktur');
                                            }elseif ($request->status == 2) {
                                                $query->where('status_approval', 2)->whereNull('verified')->whereNull('status_approval_pr_ga')->whereNull('status_approval_pr_direktur');
                                            }

                                            // VERIFIED
                                            elseif ($request->status == 3) {
                                                $query->where('status_approval', 1)->whereNull('verified')->whereNull('status_approval_pr_ga')->whereNull('status_approval_pr_direktur');
                                            }

                                            // PROSES APPROVAL PR GA
                                            elseif ($request->status == 4) {
                                                $query->where('verified', 1)->whereNull('status_approval_pr_ga')->whereNull('status_approval_pr_direktur');
                                            }elseif ($request->status == 5) {
                                                $query->where('status_approval_pr_ga', 2)->whereNull('status_approval_pr_direktur');
                                            }

                                            // PROSES APPROVAL PR DIREKTUR
                                            elseif ($request->status == 6) {
                                                $query->where('status_approval_pr_ga', 1)->whereNull('status_approval_pr_direktur');
                                            }elseif ($request->status == 7) {
                                                $query->where('status_approval_pr_direktur', 2);
                                            }

                                            elseif ($request->status == 8) {
                                                $query->where('status_approval_pr_direktur', 1);
                                            }
                                        }
                                    }

                                })
                                ->when(($request->start_date ?? false) || ($request->to_date ?? false) , function($query) use ($request) {
                                    $query->whereDate('request_forms.created_at', '>=', $request->start_date)
                                          ->whereDate('request_forms.created_at', '<=', $request->to_date);
                                })
                                ->orderBy('request_forms.id', 'desc')
                                ->filter($request);


            if(count($permission['company']) > 0 && !in_array('all', $permission['company'])) {
                $data = $data->whereIn('company_id', $permission['company']);
            }else if(count($permission['company']) == 0)
                $data = $data->where('company_id', Auth::user()->company->id ?? 0);

            if(count($permission['department']) > 0 && !in_array('all', $permission['department']))
                $data = $data->whereHas('department', function($query) use ($permission) {
                    $query->whereIn('name', $permission['department']);
                });
            else if(count($permission['department']) == 0) {
                $department_user = Auth::user()->department;
                if(strpos(strtolower($department_user->name ?? ''), '(corporate)') || strpos(strtolower($department_user->name  ?? ''), '(corp)')) {
                    $data = $data->whereHas('department', function($query) use ($department_user) {
                        $query->where('name', 'ilike', $department_user->name ?? '');
                    });
                } else
                    $data = $data->where('department_id', Auth::user()->department->id ?? 0);
            }

            if(count($permission['location']) > 0 && !in_array('all', $permission['location']))
                $data = $data->whereHas('location', function($query) use ($permission) {
                    $query->whereIn('name', $permission['location']);
                });
            else if(count($permission['location']) == 0)
                $data = $data->where('location_id', Auth::user()->location->id ?? 0);

            $data = $data->get();
        }else{
            $data = RequestForm::with(['company:id,name', 'department:id,name', 'section:id,name', 'location:id,name', 'workshop', 'user:id,full_name', 'requestFormDetail', 'logApproval' => function($query) {
                return $query->whereIn('slug', ['verified-ga', 'status-approval-pr-ga']);
            }])
            ->where(function($query) use($request, $permission, $prJasa_direktur, $prNonAset_direktur){
                if ($request->get('approval-rf') || $request->get('status') == 1) {
                    $query->whereNull('status_approval')->whereNull('verified');

                }else if ($request->get('verivikasi-rf') || $request->get('status') == 3) {
                    $query->where('status_approval', 1)->whereNull('verified')->whereNull('status_approval_pr_ga');
                }else if ($request->get('pr-ga') || $request->get('status') == 4) {
                    $query->where('status_approval', 1)->where('verified', 1)->whereNull('status_approval_pr_ga');

                }else if ($request->get('pr-direktur') || $request->get('status') == 5) {
                    $query->where('status_approval', 1)
                            ->where('verified', 1)
                            ->where('status_approval_pr_ga', 1)
                            ->whereNull('status_approval_pr_direktur')
                            ->whereHas('requestFormDetail', function($query) use ($prNonAset_direktur, $prJasa_direktur) {
                                $query
                                    ->where(function($subQuery) {
                                        $subQuery->where('type_submission_code', 1);
                                    })
                                    ->orWhere(function($subQuery) use ($prNonAset_direktur) {
                                        $subQuery->where('type_submission_code', 2)
                                                ->whereRaw('(estimate_price * qty_request_buy) - budget >= ' . $prNonAset_direktur)
                                                ->orWhereRaw('(estimate_price * qty_request_buy) - budget < 0');
                                    })->orWhere(function($subQuery) use ($prJasa_direktur) {
                                        $subQuery->where('type_submission_code', 3)
                                                ->whereRaw('(estimate_price * qty_request_buy) - budget >= ' . $prJasa_direktur)
                                                ->orWhereRaw('(estimate_price * qty_request_buy) - budget < 0');
                                    });
                            });
                }else if ($request->get('pending')) {
                    $query->where('status_approval', '<', 6);
                }

                if ($request->get('pr')) {
                    $query->where('status_approval', 1)->where('verified', 1);
                }elseif ($request->get('rf')) {
                    if ($request->has('status')) {
                        // PROSES APPROVAL
                        if ($request->status == 1) {
                            $query->whereNull('status_approval')->whereNull('verified')->whereNull('status_approval_pr_ga')->whereNull('status_approval_pr_direktur');
                        }elseif ($request->status == 2) {
                            $query->where('status_approval', 2)->whereNull('verified')->whereNull('status_approval_pr_ga')->whereNull('status_approval_pr_direktur');
                        }

                        // VERIFIED
                        elseif ($request->status == 3) {
                            $query->where('status_approval', 1)->whereNull('verified')->whereNull('status_approval_pr_ga')->whereNull('status_approval_pr_direktur');
                        }

                        // PROSES APPROVAL PR GA
                        elseif ($request->status == 4) {
                            $query->where('verified', 1)->whereNull('status_approval_pr_ga')->whereNull('status_approval_pr_direktur');
                        }elseif ($request->status == 5) {
                            $query->where('status_approval_pr_ga', 2)->whereNull('status_approval_pr_direktur');
                        }

                        // PROSES APPROVAL PR DIREKTUR
                        elseif ($request->status == 6) {
                            $query->where('status_approval_pr_ga', 1)->whereNull('status_approval_pr_direktur');
                        }elseif ($request->status == 7) {
                            $query->where('status_approval_pr_direktur', 2);
                        }

                        elseif ($request->status == 8) {
                            $query->where('status_approval_pr_direktur', 1);
                        }
                    }
                }

                if ($request->get('po')) {
                    $query->where('status_approval', 1)->where('verified', 1)->where('status_approval_pr_ga', 1)->where('status_approval_pr_direktur', 1);
                }
            })
            ->orderBy('id', 'desc')
            ->filter($request);

            if(count($permission['company']) > 0 && !in_array('all', $permission['company'])) {
                $data = $data->whereIn('company_id', $permission['company']);
            }else if(count($permission['company']) == 0)
                $data = $data->where('company_id', Auth::user()->company->id ?? 0);

            if(count($permission['department']) > 0 && !in_array('all', $permission['department'])) {
                $data = $data->whereHas('department', function($query) use ($permission) {
                    $query->whereIn('name', $permission['department']);
                });
            } else if(count($permission['department']) == 0) {
                $department_user = Auth::user()->department;
                if(strpos(strtolower($department_user->name ?? ''), '(corporate)') || strpos(strtolower($department_user->name  ?? ''), '(corp)')) {
                    $data = $data->whereHas('department', function($query) use ($department_user) {
                        $query->where('name', 'ilike', $department_user->name ?? '');
                    });
                } else
                    $data = $data->where('department_id', Auth::user()->department->id ?? 0);
            }

            if(count($permission['location']) > 0 && !in_array('all', $permission['location'])) {
                $data = $data->whereHas('location', function($query) use ($permission) {
                    $query->whereIn('name', $permission['location']);
                });
            }else if(count($permission['location']) == 0)
                $data = $data->whereHas('location', function($query) use ($permission) {
                    $query->where('name', Auth::user()->location->name ?? []);
                });

            $data = $data->get();
            foreach($data as $item) {
                $item->date_pr_ga = ($item->logApproval->where('slug', 'status-approval-pr-ga')->first()->created_at ?? null) ? date('d-m-Y H:i:s', strtotime($item->logApproval->where('slug', 'status-approval-pr-ga')->first()->created_at)) : null;
            }
            return $data;
        }

        foreach ($data as $key => $value) {
            $date_approval_rf = LogApproval::where('ref_id', $value->id)->where('slug', 'status-approval-rf')->orderBy('id', 'desc')->first()->created_at ?? null;
            $date_verivikasi_rf = LogApproval::where('ref_id', $value->id)->where('slug', 'verified-ga')->orderBy('id', 'desc')->first()->created_at ?? null;
            $date_pr_ga = LogApproval::where('ref_id', $value->id)->where('slug', 'status-approval-pr-ga')->orderBy('id', 'desc')->first()->created_at ?? null;
            $date_pr_direktur = LogApproval::where('ref_id', $value->id)->where('slug', 'status-approval-pr-direktur')->orderBy('id', 'desc')->first()->created_at ?? null;

            $value['date_approval_rf'] = $date_approval_rf;
            $value['date_verivikasi_rf'] = ($date_verivikasi_rf != null) ? $date_verivikasi_rf->format('d-m-Y H:i:s') : null;
            $value['date_pr_ga'] = ($date_pr_ga != null) ? $date_pr_ga->format('d-m-Y H:i:s') : null;
            $value['date_pr_direktur'] = $date_pr_direktur;

            $value->no_seri = $value->workshop->no_seri ?? '-';

            // if ($value->status_approval == 1) {
            //     $value->workshop->no_seri = 'Approved Manager Dept';
            // }

            $value->uid = Auth::user()->id;

            if ($value->status_approval) {
                if ($value->status_approval == 1) {
                    $value->status_process = 'Proses Verifikasi GA';
                    $value->status_approval = 'Approved Manager Dept';
                }elseif($value->status_approval == 2){
                    $value->status_process = 'Reject Manager Dept';
                    $value->status_approval = 'Reject Manager Dept';
                }
            }else{
                $value->status_process = 'Proses Approval RF Manager Dept';
            }

            if ($value->verified == 1) {
                $value->status_process = 'Proses Approval PR GA';
                $value->status_approval = 'Verified GA';
            }
            if ($value->status_approval_pr_ga == 1) {
                if($value->type_submission_code == 1)
                    $value->status_process = 'Proses Approval PR DIREKTUR';

                foreach($value->requestFormDetail ?? [] as $itemDetail) {
                    $estimate_price = ($itemDetail->estimate_price * $itemDetail->qty_request_buy) - $itemDetail->budget;
                    switch($value->type_submission_code) {
                        case "2":
                            if($estimate_price >= $prNonAset_direktur)
                                $value->status_process = 'Proses Approval PR DIREKTUR';
                            else
                                $value->status_process = 'Proses PO';
                            break;
                        case "3":
                            if($estimate_price >= $prJasa_direktur)
                                $value->status_process = 'Proses Approval PR DIREKTUR';
                            else
                                $value->status_process = 'Proses PO';
                            break;
                    }
                }

                $value->status_approval_pr = 'Approved GA';
                $value->status_approval = 'Approved PR GA';
            }elseif ($value->status_approval_pr_ga == 2) {
                $value->status_approval_pr = 'Reject GA';
                $value->status_approval = 'Reject PR GA';
            }
            if ($value->status_approval_pr_direktur == 1) {
                $value->status_process = 'Proses PO';

                $approveByDirektur = LogApproval::where('ref_id', $value->id)->where('slug', 'status-approval-pr-direktur')->orderBy('id', 'desc')->first();
                if ($approveByDirektur) {
                    $value->status_approval_pr = 'Approved DIREKTUR';
                }else{
                    $value->status_approval_pr = 'Approved GA';
                }

            }elseif ($value->status_approval_pr_direktur == 2) {
                $value->status_process = 'Reject PR DIREKTUR';

                $approveByDirektur = LogApproval::where('ref_id', $value->id)->where('slug', 'status-approval-pr-direktur')->orderBy('id', 'desc')->first();
                if ($approveByDirektur) {
                    $value->status_approval_pr = 'Reject DIREKTUR';
                }else{
                    $value->status_approval_pr = 'Reject GA';
                }
            }

        }

        $data = array_values($data->toArray());

        return response()->json($data);
    }

    // public function exportPurchase(Request $request)
    // {
    //     $permission_name = 'list-request-form';
    //     if($request['approval-rf'] == 1)
    //         $permission_name = 'approval-request-form';
    //     if($request['date-range'] && $request['rf'])
    //         $permission_name = 'list-request-form';
    //     if($request['verivikasi-rf'] == 1)
    //         $permission_name = 'verivikasi-request-form';
    //     if($request['pr-ga'])
    //         $permission_name = 'approval-pr-ga';
    //     if($request['pr-direktur'])
    //         $permission_name = 'approval-pr-direktur';
    //     if($request['date-range'] && $request['pr'])
    //         $permission_name = 'list-purchase-request';
    //     $permission = Helper::getPermissionUser($permission_name);

    //     $start_date = null;
    //     $to_date = null;
    //     if ($request->has('start_date')) {
    //         $start_date = Carbon::parse($request->start_date)->format('Y-m-d').' 00:00:00';
    //     }
    //     if ($request->has('to_date')) {
    //         $to_date = Carbon::parse($request->to_date)->format('Y-m-d').' 23:59:00';
    //     }
    //     $data['start_date'] = $start_date ?? null;
    //     $data['to_date'] = $to_date ?? null;


    //     $data = RequestForm::with(['company', 'department', 'section', 'location', 'workshop', 'user'])
    //         ->where('status_approval', 1)->where('verified', 1)
    //         ->where(function($query) use($request){
    //             if ($request->get('approval-rf') || $request->get('status') == 1) {
    //                 $query->whereNull('status_approval')->whereNull('verified')->whereNull('status_approval_pr_ga');
    //             }else if ($request->get('verivikasi-rf') || $request->get('status') == 3) {
    //                 $query->where('status_approval', 1)->whereNull('verified')->whereNull('status_approval_pr_ga');
    //             }else if ($request->get('pr-ga') || $request->get('status') == 4) {
    //                 $query->where('status_approval', 1)->where('verified', 1)->whereNull('status_approval_pr_ga');
    //             }else if ($request->get('pr-direktur') || $request->get('status') == 5) {
    //                 $query->where('status_approval', 1)->where('verified', 1)->where('status_approval_pr_ga', 1)->whereNull('status_approval_pr_direktur');
    //             }else if ($request->get('pending')) {
    //                 $query->where('status_approval', '<', 6);
    //             }
    //         })
    //         ->when(($request->start_date ?? false) || ($request->to_date ?? false) , function($query) use ($request) {
    //             $query->whereDate('request_forms.created_at', '>=', $request->start_date)
    //                     ->whereDate('request_forms.created_at', '<=', $request->to_date);
    //         })
    //         ->orderBy('id', 'desc')
    //         ->get();


    //     foreach ($data as $key => $value) {
    //         $date_approval_rf = LogApproval::where('ref_id', $value->id)->where('slug', 'status-approval-rf')->orderBy('id', 'desc')->first()->created_at ?? null;
    //         $date_verivikasi_rf = LogApproval::where('ref_id', $value->id)->where('slug', 'verified-ga')->orderBy('id', 'desc')->first()->created_at ?? null;
    //         $date_pr_ga = LogApproval::where('ref_id', $value->id)->where('slug', 'status-approval-pr-ga')->orderBy('id', 'desc')->first()->created_at ?? null;
    //         $date_pr_direktur = LogApproval::where('ref_id', $value->id)->where('slug', 'status-approval-pr-direktur')->orderBy('id', 'desc')->first()->created_at ?? null;

    //         $value['date_approval_rf'] = $date_approval_rf;
    //         $value['date_verivikasi_rf'] = ($date_verivikasi_rf != null) ? $date_verivikasi_rf->format('d-m-Y H:i:s') : null;
    //         $value['date_pr_ga'] = ($date_pr_ga != null) ? $date_pr_ga->format('d-m-Y H:i:s') : null;
    //         $value['date_pr_direktur'] = $date_pr_direktur;

    //         $value->no_seri = $value->workshop->no_seri ?? '-';
    //         // $value->uid = Auth::user()->id;

    //         if ($value->status_approval) {
    //             if ($value->status_approval == 1) {
    //                 $value->status_process = 'Proses Verivikasi GA';
    //                 $value->status_approval = 'Approved Manager Dept';
    //             }elseif($value->status_approval == 2){
    //                 $value->status_process = 'Reject Manager Dept';
    //                 $value->status_approval = 'Reject Manager Dept';
    //             }
    //         }else{
    //             $value->status_process = 'Proses Approval RF Manager Dept';
    //         }

    //         if ($value->verified == 1) {
    //             $value->status_process = 'Proses Approval PR GA';
    //             $value->status_approval = 'Verified GA';
    //         }
    //         if ($value->status_approval_pr_ga == 1) {
    //             $value->status_process = 'Proses Approval PR DIREKTUR';
    //             $value->status_approval_pr = 'Approved GA';
    //             $value->status_approval = 'Approved PR GA';
    //         }elseif ($value->status_approval_pr_ga == 2) {
    //             $value->status_approval_pr = 'Reject GA';
    //             $value->status_approval = 'Reject PR GA';
    //         }
    //         if ($value->status_approval_pr_direktur == 1) {
    //             $value->status_process = 'Proses PO';

    //             $approveByDirektur = LogApproval::where('ref_id', $value->id)->where('slug', 'status-approval-pr-direktur')->orderBy('id', 'desc')->first();
    //             if ($approveByDirektur) {
    //                 $value->status_approval_pr = 'Approved DIREKTUR';
    //             }else{
    //                 $value->status_approval_pr = 'Approved GA';
    //             }

    //         }elseif ($value->status_approval_pr_direktur == 2) {
    //             $value->status_process = 'Reject PR DIREKTUR';

    //             $approveByDirektur = LogApproval::where('ref_id', $value->id)->where('slug', 'status-approval-pr-direktur')->orderBy('id', 'desc')->first();
    //             if ($approveByDirektur) {
    //                 $value->status_approval_pr = 'Reject DIREKTUR';
    //             }else{
    //                 $value->status_approval_pr = 'Reject GA';
    //             }
    //         }

    //     }

    //     if(count($permission['company']) > 0 && !in_array('all', $permission['company'])) {
    //         $data = $data->whereIn('company_id', $permission['company']);
    //     }else if(count($permission['company']) == 0)
    //         $data = $data->where('company_id', Auth::user()->company->id ?? 0);

    //     if(count($permission['department']) > 0 && !in_array('all', $permission['department'])) {
    //         $data = $data->whereHas('department', function($query) use ($permission) {
    //             $query->whereIn('name', $permission['department']);
    //         });
    //     }else if(count($permission['department']) == 0)
    //         $data = $data->whereHas('department', function($query) use ($permission) {
    //             $query->whereIn('name', Auth::user()->department->name ?? []);
    //         });

    //     if(count($permission['location']) > 0 && !in_array('all', $permission['location'])) {
    //         $data = $data->whereHas('location', function($query) use ($permission) {
    //             $query->whereIn('name', $permission['location']);
    //         });
    //     }else if(count($permission['location']) == 0)
    //         $data = $data->whereHas('location', function($query) use ($permission) {
    //             $query->whereIn('name', Auth::user()->department->name ?? []);
    //         });

    //     $data = $data->get();

    //     return Excel::download(new ExportPurchase($data), "List Purchase Request.xlsx");
    // }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $validasi = Validator::make($request->all(), [
            'request_form_detail.*.category_item_id' => 'required',
            'request_form_detail.*.type_item_id' => 'required',
            'request_form_detail.*.type_item_name' => 'required',
            'request_form_detail.*.qty' => 'required',
            'request_form_detail.*.estimate_price' => 'required',
            'request_form_detail.*.budget' => 'required',
            'request_form_detail.*.reminder_budget' => 'required',
        ]);

        if($validasi->fails())
            return response()->json([
                'status' => false,
                'message' => $validasi->errors()->all(),
            ]);

        try{
            $data = $request->all();
            $data['user_id'] = Auth::user()->id;

            if($data['nopol'] == null)
            {
                $data['type_code'] = $data['type_submission'];
                $getSubmission = MasterTypeAsset::select('name')->where('code',$data['type_submission'])->first();
                $data['type_submission'] = $getSubmission->name;
                $data['type_submission_code'] = $data['type_code'];
            }

            $workshop = Workshop::find($data['workshop_id']);
            if (isset($workshop->asset)) {
                $data['plat_number'] = AssetDetail::where('asset_id', $workshop->asset_id)->where('attribute_code', 'nomor_polisi')->first()->value ?? null;
                $asset = Asset::find($workshop->asset_id);
                $data['type_submission'] = 'Aset';
                $data['company_id'] = $asset->company_id;
                $data['department_id'] = $asset->department_id;
                $data['section_id'] = $asset->section_id;
                $data['location_id'] = $asset->location_id;

                $data['no_rf'] = Helper::genCode($data['company_id'], $data['department_id'], $data['location_id'],01, 'RF');
                $data['no_out'] = Helper::genCode($data['company_id'], $data['department_id'], $data['location_id'],01, 'OB');
            }

            if($data['nopol'] == null)
            {
                $data['no_rf'] = Helper::genCode($data['company_id'], $data['department_id'], $data['location_id'],$data['type_code'], 'RF');
                $data['no_out'] = Helper::genCode($data['company_id'], $data['department_id'], $data['location_id'],$data['type_code'], 'OB');
            }

            // $limit_approval_directur = (int)Helper::systemSetting('approval_direktur');
            $data['total_price'] = $data['total_price'] ?? 0;

            $create = RequestForm::create($data);

            if (isset($data['request_form_detail'])) {
                foreach ($data['request_form_detail'] as $key => $value) {
                    if($value['type_item_id'] && $value['category_item_id']){
                        $type_item = TypeItem::find($value['type_item_id']);


                        $value['estimate_price'] = str_replace('.', '', $value['estimate_price']);
                        $value['budget'] = str_replace('.', '', $value['budget']);
                        $value['reminder_budget'] = str_replace('.', '', $value['reminder_budget']);

                        $value['uom'] = $type_item->uom ?? null;
                        $value['budget'] = $type_item->budget ?? 0;
                        $value['qty_item_approve'] = $value['qty'];
                        $value['noted_manager'] = null;
                        $value['request_form_id'] = $create->id;
                        $value['qty_rf'] = $value['qty'];

                        $createDetail = RequestFormDetail::create($value);

                        $type_item->estimate_price = $value['estimate_price'];
                        if (isset($value['spesifikasi'])) {
                            $type_item->spesifikasi = $value['spesifikasi'];
                        }
                        $type_item->save();
                    }
                }
            }

            return response()->json(['success' => true, 'message' => 'Pengajuan Anda Sudah tersimpan di sistem dengan No', 'no_rf' =>  $create->no_rf, 'request_form_id' => $create->id]);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $data = RequestForm::with(['company', 'department', 'location', 'workshop', 'user'])
                            ->find($id);

        $data['request_form_id'] = $data->id ?? null;
        $data['company_name'] = $data->company->name ?? null;
        $data['department_name'] = $data->department->name ?? null;
        $data['section_name'] = $data->section->name ?? null;
        $data['location_name'] = $data->location->name ?? null;
        $data['no_seri'] = $data->workshop->no_seri ?? null;

        $data['request_form_detail'] = RequestFormDetail::with(['typeItem', 'user'])
                                                            ->where('request_form_id', $data->id)
                                                            ->whereNotNull('category_item_id')
                                                            ->where('qty', '>', 0)
                                                            ->whereNotNull('type_item_id')
                                                            ->get();
        $get_booking_rf = Helper::getBookingRF($data['request_form_detail']->pluck('type_item_id')->toArray());
        foreach ($data['request_form_detail'] as $key => $value) {
            $value['category_item_name'] = CategoryItem::find($value->category_item_id)->name ?? null;
            $value['type_item_name'] = TypeItem::find($value->type_item_id)->name ?? null;
            $value['qty_in_stock'] = (TypeItem::find($value->type_item_id)->qty_stock ?? 0) - ($get_booking_rf[$value->type_item_id] ?? 0);
        }

        return response()->json($data);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $data = RequestForm::findOrFail($id);
        return response()->json($data);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        try{
            $data = $request->all();

            $rf = RequestForm::findOrFail($id);
            $TypeAsset = MasterTypeAsset::where('name', $rf->type_submission )->first();

            if (isset($data['request_form_detail'])) {
                $validate = $this->validateVerivikasi($rf, $data);

                if ($validate) {
                    return response()->json(['success' => false, 'message' => $validate], 500);
                }

               if(empty($request->status_approval_pr_ga)) {
                   $data_reject = array_column($data['request_form_detail'], 'qty_reject');
                   foreach ($data_reject as $key => $value) {
                       if($value > 0) return response()->json(['success' => false, 'message' => 'Qty reject tidak boleh melebihi 0'], 405);
                   }
               }

                foreach ($data['request_form_detail'] as $key => $value) {
                    $value['estimate_price'] = str_replace('.', '', $value['estimate_price']);
                    $value['budget'] = str_replace('.', '', $value['budget']);
                    $value['reminder_budget'] = str_replace('.', '', $value['reminder_budget']);
                    if($value['type_item_id'] && $value['category_item_id']){
                        unset($value['category_item_name']);
                        unset($value['type_item_name']);
                        unset($value['qty_in_stock']);

                        $type_item = TypeItem::find($value['type_item_id']);

                        $value['uom'] = $type_item->uom ?? null;
                        $value['budget'] = $type_item->budget ?? 0;
                        $value['reminder_budget'] = $value['budget'] - ($value['qty_request_buy'] * $value['estimate_price']);
                        $value['qty_request_buy'] = (int)$value['qty_request_buy'];
                        $value['qty_reject'] = (int)$value['qty_reject'];

                        if ($rf->status_approval == null && isset($data['status_approval'])) {
                            $value['qty_rf'] = (int)$value['qty_item_approve'];
                            $value['qty'] = (int)$value['qty_item_approve'];
                        }

                        if ($rf->verified == null && isset($data['verified'])) {
                            $value['qty_rf'] = (int)$value['qty_item_approve'] + $value['qty_request_buy'];
                            $value['qty'] = $value['qty_rf'];
                            $data['create_pr'] = now()->format('Y-m-d H:i:s');
                        }

                        $rfd = RequestFormDetail::find($value['id']);

                        if($value['qty_item_approve'] != $rfd['qty_item_approve']){
                            $count_booking_item = $type_item->booking_rf + $value['qty_item_approve'];
                            if($count_booking_item < $type_item->booking_rf){
                                $type_item->booking_rf = $count_booking_item;
                                $type_item->save();
                            }
                        }

                        $rfd->update($value);
                        // $createDetail = RequestFormDetail::find($value['id'])->update($value);
                    }
                }
            }

            if ($rf->status_approval == null && isset($data['status_approval'])) {
                LogApproval::createLogApproval(1, 'status-approval-rf', 'Manager Dept', $id);
            }elseif ($rf->verified == null && isset($data['verified'])) {
                LogApproval::createLogApproval(1, 'verified-ga', 'GA', $id);

                $data['no_pr'] = Helper::genCode($rf->company_id, $rf->department_id, $rf->location_id,$TypeAsset->code, 'PR');
            }elseif ($rf->status_approval_pr_ga == null && isset($data['status_approval_pr_ga'])) {
                LogApproval::createLogApproval(1, 'status-approval-pr-ga', 'GA', $id);
            }elseif ($rf->status_approval_pr_direktur == null && isset($data['status_approval_pr_direktur'])) {
                LogApproval::createLogApproval(1, 'status-approval-pr-direktur', 'Direktur', $id);
                $data['status_approval_pr_direktur'] = 1;
            }

            if(isset($data['status_approval_pr_direktur']) && $data['status_approval_pr_direktur'] == null){
                $data['status_approval_pr_direktur'] = null;
            }

            // $limit_approval_directur = (int)Helper::systemSetting('approval_direktur');
            // if (isset($data['total_price']) && $data['total_price'] < $limit_approval_directur && $data['type_submission'] != 'Asset') {
            //     $data['status_approval_pr_direktur'] = 1;
            // }elseif(isset($data['status_approval_pr_direktur']) && $data['status_approval_pr_direktur'] == null){
            //     $data['status_approval_pr_direktur'] = null;
            // }



            $edit = $rf->update($data);

            return response()->json(['success' => true, 'message' => 'Berhasil edit data']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    public function validateVerivikasi($rf, $data)
    {
        foreach ($data['request_form_detail'] as $key => $value) {
            // VALIDASI VERIVIKASI
            if ($rf->verified == null && isset($data['verified'])) {
                if($value['qty_item_approve'] > $value['qty_in_stock']){
                    return 'Qty Item Approve tidak boleh lebih dari Qty Stock';
                }

                if($value['qty_reject'] < 0){
                    return 'Qty Reject tidak boleh kurang dari 0';
                }
            }

            // VALIDASI APPROVAL PR GA
            if ($rf->status_approval_pr_ga == null && isset($data['status_approval_pr_ga'])) {
                if($value['qty_item_approve'] > $value['qty_in_stock']){
                    return 'Qty Item Approve tidak boleh lebih dari Qty Stock';
                }

                if($value['qty_reject'] < 0){
                    return 'Qty Reject tidak boleh kurang dari 0';
                }
            }

            // VALIDASI APPROVAL PR GA
            if ($rf->status_approval_pr_direktur == null && isset($data['status_approval_pr_direktur'])) {
                if($value['qty_item_approve'] > $value['qty_in_stock']){
                    return 'Qty Item Approve tidak boleh lebih dari Qty Stock';
                }

                if($value['qty_reject'] < 0){
                    return 'Qty Reject tidak boleh kurang dari 0';
                }
            }
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $data = RequestForm::findOrFail($id);
        $data->delete();
        $response = array(
            'success' => true,
            'message' => 'Berhasil hapus data'
        );

        return response()->json($response);
    }

    public function resetDetail($id)
    {
        $rf = RequestForm::find($id);
        $rf->company_id = null;
        $rf->department_id = null;
        $rf->location_id = null;
        $rf->type_submission = null;
        $rf->save();

        $data = RequestFormDetail::where('request_form_id', $id)->get();
        foreach ($data as $key => $value) {
            $value->delete();
        }
        $response = array(
            'success' => true,
            'message' => 'Berhasil reset data'
        );

        return response()->json($response);
    }

    public function reportRf(Request $request)
    {
        $data = RequestForm::with('requestFormDetail')->findOrFail($request->id);
        $user = User::find($request->uid);
        $data['approve_rf'] = LogApproval::where('ref_id', $data->id)->where('slug', 'status-approval-rf')->orderBy('id', 'desc')->first();
        $cetak = "REQUEST FORM $data->type_submission ('.date('d F Y').').pdf";

        // return view('pdf.request_form', ['data' => $data, 'user' => $user]);

        $pdf = PDF::loadview('pdf.request_form', compact('data', 'user'))
                    ->setPaper('A4', 'portrait')
                    ->setOptions(['isPhpEnabled' => true, 'enable_remote' => true]);
        return $pdf->stream($cetak);
    }

    public function reportPr(Request $request)
    {
        $data = RequestForm::with(['requestFormDetail' => function($q){
                                    $q->where('qty_request_buy', '>', 0);
                                }])
                                ->findOrFail($request->id);
        $user = User::find($request->uid);

        $approveManDept = LogApproval::where('ref_id', $data->id)->where('slug', 'status-approval-rf')->orderBy('id', 'desc')->first();
        $data['approved_manager_dept'] = $approveManDept->user->full_name ?? null;
        $data['date_approved_manager_dept'] = $approveManDept->created_at ?? null;
        $data['role_approved_manager_dept'] = $approveManDept->user->jabatan_name ?? null;

        $approveByDirektur = LogApproval::where('ref_id', $data->id)->where('slug', 'status-approval-pr-ga')->orderBy('id', 'desc')->first();
        $data['approved_ga_manager'] = $approveByDirektur->user->full_name ?? null;
        $data['date_approved_ga_manager'] = $approveByDirektur->created_at ?? null;
        $data['role_approved_ga_manager'] = $approveByDirektur->user->jabatan_name ?? null;

        $approveByDirektur = LogApproval::where('ref_id', $data->id)->where('slug', 'status-approval-pr-direktur')->orderBy('id', 'desc')->first();
        $data['approved_direktur'] = $approveByDirektur->user->full_name ?? null;
        $data['date_approved_direktur'] = $approveByDirektur->created_at ?? null;
        $data['role_approved_direktur'] = $approveByDirektur->user->jabatan_name ?? null;

        $cetak = "PURCHASE REQUEST FORM $data->type_submission ('.date('d F Y').').pdf";

        // return view('pdf.purchase_request_form', ['data' => $data, 'user' => $user]);

        $pdf = PDF::loadview('pdf.purchase_request_form', compact('data', 'user'))
                    ->setPaper('A4', 'portrait')
                    ->setOptions(['isPhpEnabled' => true, 'enable_remote' => true]);
        return $pdf->stream($cetak);
    }

    public function riwayatApproval($id)
    {
        $data = LogApproval::where('ref_id', $id)->get();

        return response()->json($data);
    }

    public function getRefNo(Request $request)
    {
        $data = $request->all();

        $ref_no = Helper::genCode($data['company_id'], $data['department_id'], $data['location_id'], 'RF');

        return response()->json($ref_no);
    }

    public function exportRequestForm(Request $request)
    {
        $permission_name = 'list-request-form';
        if($request['approval-rf'] == 1)
            $permission_name = 'approval-request-form';
        if($request['date-range'] && $request['rf'])
            $permission_name = 'list-request-form';
        if($request['verivikasi-rf'] == 1)
            $permission_name = 'verivikasi-request-form';
        if($request['pr-ga'])
            $permission_name = 'approval-pr-ga';
        if($request['pr-direktur'])
            $permission_name = 'approval-pr-direktur';
        if($request['date-range'] && $request['pr'])
            $permission_name = 'list-pr';
        $permission = Helper::getPermissionUser($permission_name);

        if(!Auth::user() && $request->userId) {
            $user = User::where('code', $request->userId)->first();
            if(!$user) {
                return response()->json(['error' => 'Token tidak valid'], 401);
            }
            Auth::login($user);
        }

        $prNonAset_direktur = Helper::systemSetting('prNonAset_direktur');
        $prJasa_direktur = Helper::systemSetting('prJasa_direktur');
        if ($request->get('date-range') == 1 || $request->has('start_date')) {
            $data = RequestForm::with(['company:id,name', 'department:id,name', 'section:id,name', 'location:id,name', 'workshop', 'user:id,full_name', 'requestFormDetail','logApproval' => function($query) {
                                    return $query->whereIn('slug', ['verified-ga', 'status-approval-pr-ga']);
                                }])
                                ->where(function($query) use($request, $prJasa_direktur, $prNonAset_direktur){
                                    if ($request->get('approval-rf') || $request->get('status') == 1) {
                                        $query->whereNull('status_approval')->whereNull('verified')->whereNull('status_approval_pr_ga');

                                    }else if ($request->get('verivikasi-rf') || $request->get('status') == 3) {
                                        $query->where('status_approval', 1)->whereNull('verified')->whereNull('status_approval_pr_ga');
                                    }else if ($request->get('pr-ga') || $request->get('status') == 4) {
                                        $query->where('status_approval', 1)->where('verified', 1)->whereNull('status_approval_pr_ga');
                                    }else if ($request->get('pr-direktur') || $request->get('status') == 5) {
                                        $query->where('status_approval', 1)->where('verified', 1)->where('status_approval_pr_ga', 1)->whereNull('status_approval_pr_direktur');
                                    }else if ($request->get('pending')) {
                                        $query->where('status_approval', '<', 6);
                                    }

                                    if ($request->get('pr')) {
                                        $query->where('status_approval', 1)->where('verified', 1)
                                              ->where('status_approval_pr_ga', 1)
                                              ->whereHas('requestFormDetail', function($query) use ($prNonAset_direktur, $prJasa_direktur) {
                                                    $query
                                                    ->where(function($subQuery) {
                                                        $subQuery->whereIn('type_submission_code', [1]);
                                                    })
                                                    ->orWhere(function($subQuery) use ($prNonAset_direktur) {
                                                        $subQuery->where('type_submission_code', 3)
                                                                ->whereRaw('(estimate_price * qty_request_buy) - budget > ' . $prNonAset_direktur)
                                                                ->where('status_approval_pr_direktur', 1)
                                                                ->orWhereRaw('(estimate_price * qty_request_buy) - budget < 0');
                                                    })->orWhere(function($subQuery) {
                                                        $subQuery->where('type_submission_code', 3);
                                                    })->orWhere(function($subQuery) use ($prJasa_direktur) {
                                                        $subQuery->where('type_submission_code', 4)
                                                                ->whereRaw('(estimate_price * qty_request_buy) - budget > ' . $prJasa_direktur)
                                                                ->where('status_approval_pr_direktur', 1)
                                                                ->orWhereRaw('(estimate_price * qty_request_buy) - budget < 0');
                                                    });
                                                });
                                    }elseif ($request->get('rf')) {
                                        if ($request->has('status')) {

                                            // PROSES APPROVAL
                                            if ($request->status == 1) {
                                                $query->whereNull('status_approval')->whereNull('verified')->whereNull('status_approval_pr_ga')->whereNull('status_approval_pr_direktur');
                                            }elseif ($request->status == 2) {
                                                $query->where('status_approval', 2)->whereNull('verified')->whereNull('status_approval_pr_ga')->whereNull('status_approval_pr_direktur');
                                            }

                                            // VERIFIED
                                            elseif ($request->status == 3) {
                                                $query->where('status_approval', 1)->whereNull('verified')->whereNull('status_approval_pr_ga')->whereNull('status_approval_pr_direktur');
                                            }

                                            // PROSES APPROVAL PR GA
                                            elseif ($request->status == 4) {
                                                $query->where('verified', 1)->whereNull('status_approval_pr_ga')->whereNull('status_approval_pr_direktur');
                                            }elseif ($request->status == 5) {
                                                $query->where('status_approval_pr_ga', 2)->whereNull('status_approval_pr_direktur');
                                            }

                                            // PROSES APPROVAL PR DIREKTUR
                                            elseif ($request->status == 6) {
                                                $query->where('status_approval_pr_ga', 1)->whereNull('status_approval_pr_direktur');
                                            }elseif ($request->status == 7) {
                                                $query->where('status_approval_pr_direktur', 2);
                                            }

                                            elseif ($request->status == 8) {
                                                $query->where('status_approval_pr_direktur', 1);
                                            }
                                        }
                                    }

                                })
                                ->when(($request->start_date ?? false) || ($request->to_date ?? false) , function($query) use ($request) {
                                    $query->whereDate('request_forms.created_at', '>=', $request->start_date)
                                          ->whereDate('request_forms.created_at', '<=', $request->to_date);
                                })
                                ->orderBy('request_forms.id', 'desc')
                                ->filter($request);


            if(count($permission['company']) > 0 && !in_array('all', $permission['company'])) {
                $data = $data->whereIn('company_id', $permission['company']);
            }else if(count($permission['company']) == 0)
                $data = $data->where('company_id', Auth::user()->company->id ?? 0);

            if(count($permission['department']) > 0 && !in_array('all', $permission['department']))
                $data = $data->whereHas('department', function($query) use ($permission) {
                    $query->whereIn('name', $permission['department']);
                });
            else if(count($permission['department']) == 0) {
                $department_user = Auth::user()->department;
                if(strpos(strtolower($department_user->name ?? ''), '(corporate)') || strpos(strtolower($department_user->name  ?? ''), '(corp)')) {
                    $data = $data->whereHas('department', function($query) use ($department_user) {
                        $query->where('name', 'ilike', $department_user->name ?? '');
                    });
                } else
                    $data = $data->where('department_id', Auth::user()->department->id ?? 0);
            }

            if(count($permission['location']) > 0 && !in_array('all', $permission['location']))
                $data = $data->whereHas('location', function($query) use ($permission) {
                    $query->whereIn('name', $permission['location']);
                });
            else if(count($permission['location']) == 0)
                $data = $data->where('location_id', Auth::user()->location->id ?? 0);

            $data = $data->get();
        }else{
            $data = RequestForm::with(['company:id,name', 'department:id,name', 'section:id,name', 'location:id,name', 'workshop', 'user:id,full_name', 'requestFormDetail', 'logApproval' => function($query) {
                return $query->whereIn('slug', ['verified-ga', 'status-approval-pr-ga']);
            }])
            ->where(function($query) use($request, $permission, $prJasa_direktur, $prNonAset_direktur){
                if ($request->get('approval-rf') || $request->get('status') == 1) {
                    $query->whereNull('status_approval')->whereNull('verified');

                }else if ($request->get('verivikasi-rf') || $request->get('status') == 3) {
                    $query->where('status_approval', 1)->whereNull('verified')->whereNull('status_approval_pr_ga');
                }else if ($request->get('pr-ga') || $request->get('status') == 4) {
                    $query->where('status_approval', 1)->where('verified', 1)->whereNull('status_approval_pr_ga');

                }else if ($request->get('pr-direktur') || $request->get('status') == 5) {
                    $query->where('status_approval', 1)
                            ->where('verified', 1)
                            ->where('status_approval_pr_ga', 1)
                            ->whereNull('status_approval_pr_direktur')
                            ->whereHas('requestFormDetail', function($query) use ($prNonAset_direktur, $prJasa_direktur) {
                                $query
                                ->where(function($subQuery) {
                                    $subQuery->where('type_submission', 1);
                                })
                                ->orWhere(function($subQuery) use ($prNonAset_direktur) {
                                    $subQuery->where('type_submission', 2)
                                            ->whereRaw('(estimate_price * qty_request_buy) - budget > ' . $prNonAset_direktur)
                                            ->orWhereRaw('(estimate_price * qty_request_buy) - budget < 0');
                                })->orWhere(function($subQuery) use ($prJasa_direktur) {
                                    $subQuery->where('type_submission', 3)
                                            ->whereRaw('(estimate_price * qty_request_buy) - budget > ' . $prJasa_direktur)
                                            ->orWhereRaw('(estimate_price * qty_request_buy) - budget < 0');
                                });
                            });
                }else if ($request->get('pending')) {
                    $query->where('status_approval', '<', 6);
                }

                if ($request->get('pr')) {
                    $query->where('status_approval', 1)->where('verified', 1);
                }elseif ($request->get('rf')) {
                    if ($request->has('status')) {
                        // PROSES APPROVAL
                        if ($request->status == 1) {
                            $query->whereNull('status_approval')->whereNull('verified')->whereNull('status_approval_pr_ga')->whereNull('status_approval_pr_direktur');
                        }elseif ($request->status == 2) {
                            $query->where('status_approval', 2)->whereNull('verified')->whereNull('status_approval_pr_ga')->whereNull('status_approval_pr_direktur');
                        }

                        // VERIFIED
                        elseif ($request->status == 3) {
                            $query->where('status_approval', 1)->whereNull('verified')->whereNull('status_approval_pr_ga')->whereNull('status_approval_pr_direktur');
                        }

                        // PROSES APPROVAL PR GA
                        elseif ($request->status == 4) {
                            $query->where('verified', 1)->whereNull('status_approval_pr_ga')->whereNull('status_approval_pr_direktur');
                        }elseif ($request->status == 5) {
                            $query->where('status_approval_pr_ga', 2)->whereNull('status_approval_pr_direktur');
                        }

                        // PROSES APPROVAL PR DIREKTUR
                        elseif ($request->status == 6) {
                            $query->where('status_approval_pr_ga', 1)->whereNull('status_approval_pr_direktur');
                        }elseif ($request->status == 7) {
                            $query->where('status_approval_pr_direktur', 2);
                        }

                        elseif ($request->status == 8) {
                            $query->where('status_approval_pr_direktur', 1);
                        }
                    }
                }

                if ($request->get('po')) {
                    $query->where('status_approval', 1)->where('verified', 1)->where('status_approval_pr_ga', 1)->where('status_approval_pr_direktur', 1);
                }
            })
            ->orderBy('id', 'desc')
            ->filter($request);

            if(count($permission['company']) > 0 && !in_array('all', $permission['company'])) {
                $data = $data->whereIn('company_id', $permission['company']);
            }else if(count($permission['company']) == 0)
                $data = $data->where('company_id', Auth::user()->company->id ?? 0);

            if(count($permission['department']) > 0 && !in_array('all', $permission['department'])) {
                $data = $data->whereHas('department', function($query) use ($permission) {
                    $query->whereIn('name', $permission['department']);
                });
            } else if(count($permission['department']) == 0) {
                $department_user = Auth::user()->department;
                if(strpos(strtolower($department_user->name ?? ''), '(corporate)') || strpos(strtolower($department_user->name  ?? ''), '(corp)')) {
                    $data = $data->whereHas('department', function($query) use ($department_user) {
                        $query->where('name', 'ilike', $department_user->name ?? '');
                    });
                } else
                    $data = $data->where('department_id', Auth::user()->department->id ?? 0);
            }

            if(count($permission['location']) > 0 && !in_array('all', $permission['location'])) {
                $data = $data->whereHas('location', function($query) use ($permission) {
                    $query->whereIn('name', $permission['location']);
                });
            }else if(count($permission['location']) == 0)
                $data = $data->whereHas('location', function($query) use ($permission) {
                    $query->where('name', Auth::user()->location->name ?? []);
                });

            $data = $data->get();
        }

        foreach ($data as $key => $value) {
            $date_approval_rf = LogApproval::where('ref_id', $value->id)->where('slug', 'status-approval-rf')->orderBy('id', 'desc')->first()->created_at ?? null;
            $date_verivikasi_rf = LogApproval::where('ref_id', $value->id)->where('slug', 'verified-ga')->orderBy('id', 'desc')->first()->created_at ?? null;
            $date_pr_ga = LogApproval::where('ref_id', $value->id)->where('slug', 'status-approval-pr-ga')->orderBy('id', 'desc')->first()->created_at ?? null;
            $date_pr_direktur = LogApproval::where('ref_id', $value->id)->where('slug', 'status-approval-pr-direktur')->orderBy('id', 'desc')->first()->created_at ?? null;

            $value['date_approval_rf'] = $date_approval_rf;
            $value['date_verivikasi_rf'] = ($date_verivikasi_rf != null) ? $date_verivikasi_rf->format('d-m-Y H:i:s') : null;
            $value['date_pr_ga'] = ($date_pr_ga != null) ? $date_pr_ga->format('d-m-Y H:i:s') : null;
            $value['date_pr_direktur'] = $date_pr_direktur;

            $value->no_seri = $value->workshop->no_seri ?? '-';

            // if ($value->status_approval == 1) {
            //     $value->workshop->no_seri = 'Approved Manager Dept';
            // }

            $value->uid = Auth::user()->id;

            if ($value->status_approval) {
                if ($value->status_approval == 1) {
                    $value->status_process = 'Proses Verivikasi GA';
                    $value->status_approval = 'Approved Manager Dept';
                }elseif($value->status_approval == 2){
                    $value->status_process = 'Reject Manager Dept';
                    $value->status_approval = 'Reject Manager Dept';
                }
            }else{
                $value->status_process = 'Proses Approval RF Manager Dept';
            }

            if ($value->verified == 1) {
                $value->status_process = 'Proses Approval PR GA';
                $value->status_approval = 'Verified GA';
            }
            if ($value->status_approval_pr_ga == 1) {
                $value->status_process = 'Proses Approval PR DIREKTUR';
                $value->status_approval_pr = 'Approved GA';
                $value->status_approval = 'Approved PR GA';
            }elseif ($value->status_approval_pr_ga == 2) {
                $value->status_approval_pr = 'Reject GA';
                $value->status_approval = 'Reject PR GA';
            }
            if ($value->status_approval_pr_direktur == 1) {
                $value->status_process = 'Proses PO';

                $approveByDirektur = LogApproval::where('ref_id', $value->id)->where('slug', 'status-approval-pr-direktur')->orderBy('id', 'desc')->first();
                if ($approveByDirektur) {
                    $value->status_approval_pr = 'Approved DIREKTUR';
                }else{
                    $value->status_approval_pr = 'Approved GA';
                }

            }elseif ($value->status_approval_pr_direktur == 2) {
                $value->status_process = 'Reject PR DIREKTUR';

                $approveByDirektur = LogApproval::where('ref_id', $value->id)->where('slug', 'status-approval-pr-direktur')->orderBy('id', 'desc')->first();
                if ($approveByDirektur) {
                    $value->status_approval_pr = 'Reject DIREKTUR';
                }else{
                    $value->status_approval_pr = 'Reject GA';
                }
            }

        }

        if($permission_name == 'list-pr')
            // return view('export.ExportPurchase', compact('data'));
            return Excel::download(new ExportPurchase($data), "List Purchase Request.xlsx");
        else
            return Excel::download(new ExportRequestForm($data), 'request_form.xlsx');
    }
}
