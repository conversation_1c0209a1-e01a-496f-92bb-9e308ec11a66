<?php

namespace App\Http\Controllers\Fleet;

use App\Http\Controllers\Controller;
use App\Models\Fleet\AnswerQuestion;
use App\Models\Fleet\AnswerQuestionDetail;
use App\Models\Fleet\AnswerQuestionUser;
use App\Models\Fleet\CategoryQuestion;
use App\Models\Fleet\Question;
use App\Models\Fleet\QuestionMark;
use App\Models\Fleet\StatusVehicle;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class ChecklistVehicleController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $auth = Auth::user()->role->name;
        // $data = AnswerQuestionUser::with('answerQuestionDetail')->where('vehicle_id')->with('statusVehicle')->orderBy('id', 'desc')->get();
        $data = AnswerQuestionUser::with('answerQuestionDetail', 'statusVehicle')->where('vehicle_id', '!=', null)->orderBy('id', 'desc')->get();
        foreach ($data as $key => $res) {
            if ($res['total_point'] <= $res->statusVehicle->start) {
                $data[$key]['ready'] = 0;
            }else{
                $data[$key]['ready'] = 1;
            }
        }

        return response()->json(['data' => $data, 'user' => $auth]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create(Request $request)
    {
        $route = $request->slug;
        if ($route) {
            $data = CategoryQuestion::where('slug', 'checklist-vehicle')->orderBy('id', 'asc')->with('question.answer')->get();
            return response()->json($data);
        }else{
            return response()->json(['messages' => 'not found!']);
        }
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        try{
            $data = $request->all();
            // dd($data);
            $answerQuestionUser['vehicle_id'] = $data[0]['kendaraan_id'];
            $answerQuestionUser['status'] = 0;
            $answerQuestionUser['user_id'] = Auth::user()->id;
            $answerQuestionUser['slug'] = 'checklist-vehicle';
            $answerUser = AnswerQuestionUser::create($answerQuestionUser);

            // $mark = new QuestionMark;
            // $mark['kendaraan_id'] = $data[0]['kendaraan_id'];
            // $mark['user_id'] = Auth::user()->id;
            // $mark['slug'] = "checklist-vehicle";
            // $mark->save();
            // $idM = $mark->id;
            
            $CategoryQuestion = array();
            foreach ($data as $key => $res) {
                $question = Question::findOrFail($res['question_id']);
                $answer = AnswerQuestion::findOrFail($res['answer_id']);
                $res['question'] = $question->question;
                $res['answer'] = $answer->answer;
                $res['point'] = $answer->point;
                $res['answer_question_user_id'] = $answerUser->id;
                $res['answer_question_id'] = $res['answer_id'];
                unset($res['kendaraan_id']);
                // dd($res);
                // $CategoryQuestion[$key] = AnswerQuestionUser::create($res);

                $CategoryQuestion[$key] = AnswerQuestionDetail::create($res);
            }

            $point = 0;
            foreach ($CategoryQuestion as $answer) {
                // dd($answer->point);
                $point += (int)$answer->point;
            }

            // dd($point);
            $color = StatusVehicle::where('start', '<=', $point)->where('to', '>=', $point)->first()->id;
            $qPoint['status_vehicle_id'] = $color;
            $qPoint['total_point'] = $point;
            $qPoint['input_date'] = now();
            $qPoint['status'] = 1;
            AnswerQuestionUser::findOrFail($answerUser->id)->update($qPoint);
            // $questionPoint = QuestionMark::findOrFail($idM)->update($qPoint);

            return response()->json(['success' => true, 'message' => 'Selamat anda mendapatkan '.$point.' point']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        // 
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $data = AnswerQuestionUser::findOrFail($id);
        if ($data['total_point'] <= $data->statusVehicle->start) {
            $data['ready'] = 0;
        }else{
            $data['ready'] = 1;
        }

        return response()->json($data);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        try{
            $data = $request->all();
            $CategoryQuestion = AnswerQuestionUser::findOrFail($id)->update($data);
            
            return response()->json(['success' => true, 'message' => 'Berhasil edit data']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $data = AnswerQuestionUser::findOrFail($id);
        $data->delete();
        $response = array(
            'success' => true,
            'message' => 'Berhasil hapus data'
        );

        return response()->json($response);
    }

    public function groupByColor()
    {
        try {
            $data = AnswerQuestionUser::where('vehicle_id')->orderBy('id', 'desc')->with('statusVehicle', 'vehicle')->get()->groupBy('status_vehicle_id');
    
            return response()->json(['success' => true, 'data' => $data]);
        } catch (\Throwable $th) {
            return response()->json(['success' => false, 'message' => $th->getMessage()]);
        }
    }
}
