<?php

namespace App\Http\Controllers\Fleet;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use DB;
use Auth;


class RoleUserController extends Controller
{
    function __construct()
    {
        //  $this->middleware('permission:role-list|role-create|role-edit|role-delete', ['only' => ['index','store']]);
        //  $this->middleware('permission:role-create', ['only' => ['create','store']]);
        //  $this->middleware('permission:role-edit', ['only' => ['edit','update']]);
        //  $this->middleware('permission:role-delete', ['only' => ['destroy']]);
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        // $permissions = Permission::pluck('id','id')->all();
        
        // $role = Role::first();
        // $role->syncPermissions($permissions);
        // $user = Auth::user();
        // $user->assignRole([$role->id]);

        $roles = Role::orderBy('id','DESC')->get();

        return response()->json(['roles' => $roles]);
    }
    
    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $permission = Permission::orderBy('menu_name','asc')->get();
        foreach ($permission as $key => $data) {
            $data->selected = false;
        }
        $group = Permission::select(['menu_name'])->groupBy('menu_name')->get();
        foreach ($group as $key => $data) {
            $data->selected = false;
        }

        return response()->json(['permission' => $permission, 'group' => $group]);
    }
    
    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $this->validate($request, [
            'name' => 'required|unique:roles,name',
            'permission' => 'required',
        ]);
    
        $role = Role::create(['name' => $request->input('name')]);
        $permission = $request->input('permission');
        $selected = [];
        if (is_array($permission)) {
            for ($i=0; $i < count($permission); $i++) { 
                if ($permission[$i]['selected']) {
                    $id = $permission[$i]['id'];
                    $selected[$id] = $id;
                }
            }
        }

        $role->syncPermissions($selected);
    
        return response()->json(['success' => true, 'message' => 'Role created successfully']);
    }
    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $role = Role::find($id);
        $rolePermissions = Permission::join("role_has_permissions","role_has_permissions.permission_id","=","permissions.id")
            ->where("role_has_permissions.role_id",$id)
            ->get();
    
        return response()->json(['role' => $role, 'rolePermissions' => $rolePermissions]);
    }
    
    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $role = Role::find($id);
        $permission = Permission::orderBy('menu_name','asc')->get();
        $group = Permission::select(['menu_name'])->groupBy('menu_name')->get();
        $rolePermissions = DB::table("role_has_permissions")->where("role_has_permissions.role_id",$id)
            ->pluck('role_has_permissions.permission_id','role_has_permissions.permission_id')
            ->all();
        
        $group_status = [];
        foreach ($permission as $key => $data) {
            if (isset($rolePermissions[$data->id])) {
                $data->selected = true;

                if (!isset($group_status[$data->menu_name]['jumlah_true'])) {
                    $group_status[$data->menu_name]['jumlah_true'] = 0;
                }else{
                    $group_status[$data->menu_name]['jumlah_true'] = $group_status[$data->menu_name]['jumlah_true'] + 1;
                }
            }else{
                $data->selected = false;
            }

            if (!isset($group_status[$data->menu_name]['jumlah'])) {
                $group_status[$data->menu_name]['jumlah'] = 0;
            }else{
                $group_status[$data->menu_name]['jumlah'] = $group_status[$data->menu_name]['jumlah'] + 1;
            }
        }

        foreach ($group as $key => $data) {
            if (isset($group_status[$data->menu_name]['jumlah']) && isset($group_status[$data->menu_name]['jumlah_true'])) {
                if ($group_status[$data->menu_name]['jumlah'] == $group_status[$data->menu_name]['jumlah_true']) {
                    $data->selected = true;
                }else{
                    $data->selected = false;
                }
            }else{
                $data->selected = false;
            }
        }
    
        return response()->json(['role' => $role, 'rolePermissions' => $rolePermissions, 'permission' => $permission, 'group' => $group]);

    }
    
    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $this->validate($request, [
            'name' => 'required',
            'permission' => 'required',
        ]);
    
        $role = Role::find($id);
        $role->name = $request->input('name');
        $role->save();

        $permission = $request->input('permission');
        $selected = [];
        if (is_array($permission)) {
            for ($i=0; $i < count($permission); $i++) { 
                if ($permission[$i]['selected']) {
                    $id = $permission[$i]['id'];
                    $selected[$id] = $id;
                }
            }
        }
    
        $role->syncPermissions($selected);
    
        return response()->json(['success' => true, 'message' => 'Role updated successfully']);
        
    }
    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        DB::table("roles")->where('id',$id)->delete();

        return response()->json(['success' => true, 'message' => 'Role deleted successfully']);
    }

    public function listPermission()
    {
        $permissions = [];
        foreach (Permission::all() as $permission) {
            if (Auth::user()->can($permission->name)) {
                $permissions[] = $permission->name;
            }
        }
        return $permissions;
    }
}
