<?php

namespace App\Http\Controllers\Fleet;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Fleet\StockOpname;
use App\Models\Fleet\Location;
use App\Models\Fleet\LocStock;
use App\Models\Fleet\StockOpnameDetail;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;
use Auth;

class StockOpnameController extends Controller
{
    public function index()
    {
        $opnames = StockOpname::with(['location','user'])->orderBy('id','desc')->get();

        return response()->json(['opnames' => $opnames]);
    }

    public function create(Request $request)
    {
        $location = Location::all();
        
        $items = [];
        if ($request->has('location_id')) {
            $items = LocStock::with('stock')
            ->where('location_id',$request->get('location_id'))
            ->get();
            foreach ($items as $item) {
                $item->actual = "";
                $item->note = "";
            }
        }

        return response()->json(['location' => $location, 'items' => $items]);
    }

    public function store(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'location_id' => 'required',
                'items' => 'required',
            ]);
    
            if ($validator->fails()) {
                return response()->json($validator->errors(), 500);
            }

            $data = new StockOpname;
            $data->code = 'LJR-SO-'.Carbon::now()->format('ymdHis').rand(10,99);
            $data->location_id = $request->input('location_id');
            $data->note = $request->input('note');
            $data->user_id = Auth::user()->id;
            $data->date = Carbon::now();
            $data->save();

            if (is_array($request->input('items'))) {
                $items = $request->input('items');
                for ($i=0; $i < count($items); $i++) { 
                    $detail = new StockOpnameDetail;
                    $detail->stock_opname_id = $data->id;
                    $detail->locstock_id = $items[$i]['id'];
                    $detail->qty_available = $items[$i]['quantity'];
                    $detail->qty_actual = $items[$i]['actual'];
                    $detail->note = $items[$i]['note'];
                    $detail->save();
                }
            }

            return response()->json(['success' => true, 'message' => 'Berhasil create data']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    public function edit($id){
        $data = StockOpname::find($id);
        $items = StockOpnameDetail::with('locStock.stock')->where('stock_opname_id',$data->id)->get();

        $location = Location::all();

        return response()->json(['location' => $location, 'items' => $items, 'data' => $data]);
    }

    public function update(Request $request,$id)
    {
        try {
            $validator = Validator::make($request->all(), [
                'location_id' => 'required',
                'items' => 'required',
            ]);
    
            if ($validator->fails()) {
                return response()->json($validator->errors(), 500);
            }

            $data = StockOpname::find($id);
            $data->note = $request->input('note');
            $data->save();

            if (is_array($request->input('items'))) {
                $items = $request->input('items');
                for ($i=0; $i < count($items); $i++) { 
                    $detail = StockOpnameDetail::find($items[$i]['id']);
                    $detail->qty_actual = $items[$i]['qty_actual'];
                    $detail->note = $items[$i]['note'];
                    $detail->save();
                }
            }

            return response()->json(['success' => true, 'message' => 'Berhasil update data']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    public function destroy($id)
    {
        try{
            $data = StockOpname::find($id);
            if ($data->delete()) {
                $detail = StockOpnameDetail::where('stock_opname_id',$id)->delete();
            }

            return response()->json(['success' => true, 'message' => 'Berhasil deleted data']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }
}
