<?php

namespace App\Http\Controllers\Fleet;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Fleet\Divisi;
use App\Models\Fleet\Branch;
use Illuminate\Support\Facades\Validator;

class DivisiController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $data = Divisi::with('branch')->orderBy('id','desc')->get();
        return response()->json($data);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $branch = Branch::all();

        return response()->json(['branch' => $branch]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'branch_id' => 'required',
            'code' => 'required',
        ]);
 
        if ($validator->fails()) {
            return response()->json($validator->errors(), 500);
        }
        
        $divisi = Divisi::create($request->all());

        return response()->json(['success' => true, 'message' => 'Berhasil input data']);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $data = Divisi::with('departement')->find($id);
        return response()->json($data);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $branch = Branch::all();
        $data = Divisi::find($id);
        return response()->json(['data'=>$data, 'branch'=>$branch]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'branch_id' => 'required',
            'code' => 'required',
        ]);
 
        if ($validator->fails()) {
            return response()->json($validator->errors(), 500);
        }
        
        $divisi = Divisi::find($id)->update($request->all());

        return response()->json(['success' => true, 'message' => 'Berhasil update data']);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $divisi = Divisi::destroy($id);

        return response()->json(['success' => true, 'message' => 'Berhasil delete data']);
    }
}
