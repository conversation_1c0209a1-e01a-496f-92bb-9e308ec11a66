<?php

namespace App\Http\Controllers\Fleet;

use App\Http\Controllers\Controller;
use App\Models\Fleet\AccountGroups;
use App\Models\Fleet\ChartMaster;
use App\Models\Fleet\StockCategory;
use App\Models\Fleet\StockType;
use App\Models\Fleet\TaxCategory;
use Illuminate\Http\Request;

class StockCategoryController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $stockCategory = StockCategory::with('stockType', 'taxCategory')->get();

        return $stockCategory;
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        // 
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $stockCategory = new StockCategory();
        $stockCategory->code = $request->get('code');
        $stockCategory->category_description = $request->get('category_description');
        $stockCategory->stock_type = $request->get('stock_type');
        $stockCategory->stock_act = 1;
        $stockCategory->adj_glact = 1;
        $stockCategory->issue_glact = 1;
        $stockCategory->purch_price_varact = 1;
        $stockCategory->material_useage_varac = 1;
        $stockCategory->wipatch = 1;
        $stockCategory->default_tax_id = $request->get('default_tax_id');
        $stockCategory->save();

        return "Create Stock Category Successfully";
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $stockCategory = StockCategory::findOrFail($id);

        return $stockCategory;
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $stockCategory = StockCategory::findOrFail($id);
        $stockCategory->code = $request->get('code');
        $stockCategory->category_description = $request->get('category_description');
        $stockCategory->stock_type = $request->get('stock_type');
        $stockCategory->stock_act = 1;
        $stockCategory->adj_glact = 1;
        $stockCategory->issue_glact = 1;
        $stockCategory->purch_price_varact = 1;
        $stockCategory->material_useage_varac = 1;
        $stockCategory->wipatch = 1;
        $stockCategory->default_tax_id = $request->get('default_tax_id');
        $stockCategory->save();

        return "Update Stock Category Successfully";
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $stockCategory = StockCategory::findOrFail($id);
        $stockCategory->delete();

        return "Delete Stock Category Successfully";
    }

    public function taxCategory()
    {
        $taxCategory = TaxCategory::get();

        return $taxCategory;
    }

    public function stockType()
    {
        $stockType = StockType::get();

        return $stockType;
    }

    public function chartMasterPandl0()
    {
        $pandl1 = ChartMaster::with('account')->whereHas('account', function ($query){
            $query->where('pandl', 0);
        })->orderBy('accountcode', 'asc')->get();

        return $pandl1;
    }

    public function chartMasterPandl1()
    {
        $pandl1 = ChartMaster::with('account')->whereHas('account', function ($query){
                    $query->where('pandl', 1);
                })->orderBy('accountcode', 'asc')->get();

        return $pandl1;
    }
}