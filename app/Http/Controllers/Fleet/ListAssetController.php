<?php

namespace App\Http\Controllers\Fleet;

use App\Http\Helper;
use App\Models\Fleet\Asset;
use Illuminate\Http\Request;
use App\Models\Fleet\Company;
use App\Models\Fleet\Section;
use App\Models\Fleet\Location;
use App\Models\Fleet\TypeItem;
use App\Models\Fleet\TypeAsset;
use App\Models\Fleet\UploadDoc;
use App\Exports\ExportAssetItem;
use App\Imports\ImportAssetLand;
use App\Models\Fleet\Department;
use App\Models\Fleet\AssetDetail;
use App\Models\Fleet\CategoryItem;
use Illuminate\Support\Facades\DB;
use App\Imports\ImportAssetVehicle;
use App\Models\Fleet\CategoryAsset;
use App\Http\Controllers\Controller;
use App\Imports\ImportAssetBuilding;
use App\Imports\ImportAssetComputer;
use App\Models\Fleet\DetailLocation;
use App\Models\Fleet\MasterLocation;
use Illuminate\Support\Facades\File;
use Maatwebsite\Excel\Facades\Excel;
use App\Imports\ImportAssetEquipment;
use App\Imports\ImportAssetFurniture;
use App\Imports\ImportAssetMechinary;
use App\Models\Fleet\RequestFormDetail;
use App\Exports\ExportAsuransiKendaraan;
use App\Imports\ImportAsuransiKendaraan;
use Illuminate\Support\Facades\Validator;

class ListAssetController extends Controller
{
    public function detailLocation()
    {
        $detail_location = DetailLocation::select('id', 'name')
            ->orderBy('name', 'asc')
            ->get();

        $detail_location = $detail_location->map(function ($item) {
            return [
                'value' => $item->id,
                'label' => $item->name
            ];
        });

        return response()->json([
            'data' => $detail_location
        ]);
    }

    public function code_aset(Request $request)
    {
        $data = Asset::whereNull('waste_disposal_id')
                        ->whereNotNull('code');

        if (!$request->all_aset && !$request->waste_disposal) {
            $data = $data->whereNull('status_disposal')
                         ->where('request_extermination_id');
        }
        $data = $data->whereNull('issued_asset_code')
            ->latest()
            ->filter($request)
            ->get();

        return response()->json([
            'data' => $data
        ]);
    }

    public function index(Request $request, $type = false)
    {
        $select = explode(',', $request->select);
        $data = Asset::whereNotNull('code')
            ->whereNull('issued_asset_code');

        if (!$request->all_aset && !$request->waste_disposal) {
            $data = $data->whereNull('status_disposal')
                         ->where('request_extermination_id');
        }

        if ($request->select) {
            // $result = [];
            $data = $data->select($select)
                ->with('typeItem', 'categoryItem', 'companies:id,name', 'department:id,name', 'picUsedUser:id,first_name,last_name,nip,full_name')
                ->latest()
                ->filter($request)
                ->paginate(10)
                ->toArray();

            $results['data'] = $data['data'];
            unset($data['data']);
            $results['meta'] = $data;

            return response()->json($results);
        } else {
            $data = $data->with([
                'companies:id,name',
                'department:id,name',
                'location:id,name',
                'categoryAsset:id,name,code',
                'typeAsset:id,name',
                'assetDetail',
                'section:id,name',
                'detailAsset',
                'detailLocation:id,name',
                'handoverDetail:id,asset_id,created_at',
                'typeItem:id,category_item_id,uom,name',
                'uploadDoc',
                'categoryItem:id,name,code',
                'picUsedUser:id,first_name,last_name,nip,full_name',
                'typeItem.simVehicle',
                'lentDetail' => function ($query) {
                    $query->orderBy('id', 'desc')
                        ->first();
                },
                'lentDetail.lent'
            ]);
            $data = $data->latest()
                ->filter($request)
                ->get();
        }


        if (!$request->select)
            foreach ($data as $key => $item) {
                $item->doc_asset = $item->uploadDoc->name ?? '';
                $item->status_asset_id = $item->status_asset;
                $item->status_asset = Helper::statusAsset($item->status_asset);
                $color_expired = '';
                $expired_in = '';
                if ($item->status_asset === 'Lent') {
                    $lent_detail = $item->lentDetail[0] ?? false;
                    if ($lent_detail) {
                        $end_period_lent = $lent_detail->lent->end_period;
                        $now = date('Y-m-d');
                        $diffinWeek = date_diff(date_create($now), date_create($end_period_lent))->format('%R%a');
                        $expired_in = date('d-m-Y', strtotime($end_period_lent));

                        // Convert difference to days
                        $diffinDays = abs($diffinWeek);

                        // Define thresholds
                        $twoWeeks = 14;
                        $oneWeek = 7;

                        // Check where the difference falls
                        if ($diffinDays > $twoWeeks) {
                            // Lebih dari 2 minggu
                            $color_expired = '#00FF00';
                        } elseif ($diffinDays <= $twoWeeks && $diffinDays > $oneWeek) {
                            // Antara 2 minggu dan 1 minggu
                            $color_expired = '#FFA500';
                        } else {
                            // Kurang dari 1 minggu
                            $color_expired = '#FF0000';
                        }
                    }
                    // $item->lentDetail = $item->lentDetail;
                }
                $item->expired_in = $expired_in;
                $item->color_expired = $color_expired;
                // dump(Helper::statusAsset($item->status_asset), $item->status_asset);
                foreach (($item->detailAsset ?? []) as $detail) {
                    if ($detail->attribute_code == 'serial_number')
                        $item['serial_number'] = $detail->value;
                    if ($detail->attribute_code == 'upload_foto_mechinary')
                        $item['detail_foto'] = asset($detail->value);
                    if ($detail->attribute_code != 'status_asset')
                        $item[$detail->attribute_code] = $detail->value;
                }

                $simVehicle = $item->typeItem->simVehicle ?? [];
                foreach ($simVehicle as $sim) {
                    if (strtolower($item['warna_plat']) == strtolower($sim->color_plat_vehicle))
                        $item->sim_yang_digunakan = json_decode($sim->type_sim);
                }

                // $item->serial_number = $detail['serial_number'];
                // $item->foto = $detail['detail_foto'];

            }

        if ($type === 'export') {
            return $data;
        }

        return response()->json([
            'data' => $data
        ]);
    }

    public function create(Request $request)
    {
        $company = Company::orderBy('name', 'asc')->get();
        $department = Department::orderBy('name', 'asc')->get();
        $section = Section::orderBy('name', 'asc')->get();
        $name = '';
        if ($request->type == "1") {
            $name = "LAND";
        } elseif ($request->type == "2") {
            $name = "BUILDING";
        } elseif ($request->type == "6") {
            $name = "VEHICLE";
        } elseif ($request->type == "4") {
            $name = "FURNITURE";
        } elseif ($request->type == "3") {
            $name = "OFFICE EQUIPMENT";
        } elseif ($request->type == "5") {
            $name = "MECHINERY";
        } elseif ($request->type == "7") {
            $name = "COMPUTER HW/SW";
        }

        $categoriItems = [];
        $location = [];
        $type_item = [];
        if (isset($request->type)) {
            $categoriItems = CategoryItem::select('id', 'name')
                ->where('name', $name)
                ->where('type_asset', 1)
                ->orderBy('name', 'asc')
                ->first();

            $location = Location::orderBy('name', 'asc')->get();
            $type_item = TypeItem::select('id', 'name')
                ->where('category_item_id', $categoriItems->id ?? 0)
                ->orderBy('name', 'asc')
                ->get();
        }

        $type_items = TypeItem::select('id', 'name')
            ->orderBy('name', 'asc')
            ->get();

        $category_asset = CategoryAsset::select('id', 'name')
            ->orderBy('name', 'asc')
            ->get();

        $detail_location = DetailLocation::select('id', 'name')
            ->orderBy('name', 'asc')
            ->get();
        $detail_location = $detail_location->map(function ($item) {
            return [
                'value' => $item->id,
                'label' => $item->name
            ];
        });

        $upload_doc = UploadDoc::select('id', 'name')
            ->orderBy('name', 'asc')
            ->get();

        $upload_doc = $upload_doc->map(function ($item) {
            return [
                'value' => $item->id,
                'label' => $item->name
            ];
        });

        return response()->json([
            'company' => $company,
            'department' => $department,
            'section' => $section,
            'location' => $location,
            'type_item' => $type_item,
            'type_items' => $type_items,
            'detail_location' => $detail_location,
            'category_asset' => $category_asset,
            'upload_doc' => $upload_doc
        ]);
    }

    public function store(Request $request, $name)
    {
        $input = $request->all();
        $count_key_asset = 11;
        if ($request->used)
            $count_key_asset = 12;
        $validasi = null;
        $code_asset = '';
        switch ($name) {
            case 'LAND':
            case 'land':
                $validasi = $this->landValidation($input);
                $code_asset = '01';
                break;
            case 'BUILDING':
            case 'building':
                $validasi = $this->buildingValidation($input);
                $code_asset = '02';
                break;
            case 'VEHICLE':
            case 'vehicle':
                $validasi = $this->vehicleValidation($input);
                $code_asset = '03';
                break;
            case 'COMPUTER SH/SW':
            case 'computer':
                $validasi = $this->computerValidation($input);
                $code_asset = '04';
                break;
            case 'OFFICE EQUIPMENT':
            case 'office-equipment':
                $validasi = $this->officeEquipmentValidation($input);
                $code_asset = '05';
                break;
            case 'MECHINARY':
            case 'mechinary':
                $validasi = $this->mechinaryValidation($input);
                $code_asset = '06';
                break;
            case 'FURNITURE':
            case 'furniture':
                $validasi = $this->furnitureValidation($input);
                $code_asset = '07';
                break;
            default:
                return response()->json([
                    'status' => false,
                    'message' => 'Data tidak ditemukan'
                ]);
        }

        if (isset($validasi) && $validasi->fails())
            return response()->json([
                'status' => false,
                'message' => $validasi->errors()->all()
            ]);

        if ($request->used == 1)
            $validasi = Validator::make($input, [
                'pic_user_id' => 'required',
            ]);

        $validasi = Validator::make($input, [
            'status' => 'required',
            'company_id' => 'required',
            'department_id' => 'required',
            'section_id' => 'required',
            'location_id' => 'required',
            'detail_location' => 'required',
            'tanggal_penerimaan_barang' => 'required',
            'tahun_bulan_aset' => 'required',
            'type_item_id' => 'required',
            'used' => 'required',
            'harga_beli' => 'required',
            'upload_doc' => 'required'
        ]);

        if ($validasi->fails())
            return response()->json([
                'status' => false,
                'message' => $validasi->errors()->all()
            ]);

        $category_asset = CategoryItem::firstWhere(['code' => $code_asset, 'type_asset' => 1]);
        $company = Company::firstWhere('id', $input['company_id']);
        $type_item = TypeItem::firstWhere('id', $input['type_item_id']);
        $tahunaset = date('my', strtotime($input['tahun_bulan_aset']));
        $code_aset = Helper::getCodeAset([
            'company_code' => $company->code_number ?? '',
            'code_type_aset' => $category_asset->code ?? '',
            'category_aset_code' => $category_asset->code ?? '',
            'type_item_code' => $type_item->code ?? '00',
            'tahun_bulan_asset' => date('my', strtotime($request->tahun_bulan_asset)),
        ]);

        $input['code'] = $code_aset;
        // $input['code'] = $input['no_asset'] ?? '';
        $input['category_asset_id'] = $category_asset->id;
        $input['category_item_id'] = $category_asset->id;
        $input['company'] = $company->name;
        $input['tahun_bulan_aset'] = $input['tahun_bulan_aset'] ?? $tahunaset;
        $input['status_asset'] = $input['status'] ?? '';
        $input['good_receive_date'] = date('Y-m-d', strtotime($input['tanggal_penerimaan_barang']));
        $input['spesification'] = $input['spesifikasi'] ?? '';
        $input['purchase_price_no_ppn'] = $input['harga_beli'] ?? '';
        $input['harga_beli'] = str_replace([',', ' '], '', $input['harga_beli']) ?? '';
        $input['doc_aset'] = Helper::uploadFile($request->file('upload_file_doc'), 'doc_asset', 'doc_asset') ?? '';
        $input['detail_location'] = Helper::detailLocationAdd(($input['detail_location'] == 'lainnya') ? $input['detail_location_other'] : $input['detail_location']);
        $input['upload_doc'] = Helper::uploadDocAdd(($input['upload_doc'] == 'lainnya') ? $input['upload_doc_other'] : $input['upload_doc']);
        $input['used'] = Helper::statusUsed($input['used']);
        if ($request->used == 1)
            $input['pic_used_user_id'] = $input['pic_user_id'] ?? '';
        $asset = Asset::create($input);

        $list_asset = ["status", "pic_user_id", "company_id", "section_id", "location_id", "tanggal_penerimaan_barang", "type_item_id", "spesifikasi", "used", "harga_beli", "upload_doc", "department_id", "detail_location", 'tahun_bulan_aset'];

        foreach ($input as $key => $item) {
            if (!in_array($key, $list_asset)) {
                $asset_detail[$key] = $item;
                if ($key !== 'upload_file_doc' && $request->hasFile($key)) {
                    $asset_detail[$key] = Helper::uploadFile($request->file($key), 'asset', $key);
                }

                $id = AssetDetail::max('id') + 1;
                AssetDetail::create([
                    'id' => $id,
                    'asset_id' => $asset->id,
                    'attribute_code' => $key,
                    'value' => $asset_detail[$key]
                ]);
            }
        }

        // foreach($input as $key => $item) {
        //    if(!in_array($key, $list_asset)) {
        //         $asset_detail[$key] = $item;
        //         if($request->hasFile($key))
        //             $asset_detail[$key] = Helper::uploadFile($request->file($key), 'asset', $key);

        //         $id = AssetDetail::max('id') + 1;
        //         AssetDetail::create([
        //             'id' => $id,
        //             'asset_id' => $asset->id,
        //             'attribute_code' => $key,
        //             'value' => $asset_detail[$key]
        //         ]);
        //    }

        // }

        return response()->json([
            'status' => true,
            'message' => 'Data berhasil disimpan'
        ]);
    }

    // public function edit($id)
    // {
    //     $data = Asset::where('id',$id)->with('companies:id,name', 'department:id,name', 'location:id,name', 'categoryAsset:id,name,code', 'typeAsset:id,name',
    //     'assetDetail', 'section:id,name', 'detailAsset', 'detailLocation:id,name', 'handoverDetail:id,asset_id,created_at','typeItem:id,category_item_id,uom,name')->first();
    //     $data->doc_aset = Helper::statusUploadDoc($data->upload_doc);
    //     foreach(($data->detailAsset ?? []) as $detail) {
    //         if($detail->attribute_code == 'serial_number')
    //             $data['serial_number'] = $detail->value;
    //         if($detail->attribute_code == 'upload_foto_mechinary')
    //             $data['detail_foto'] = asset($detail->value);
    //         if($detail->attribute_code != 'status_asset')
    //             $data[$detail->attribute_code] = $detail->value;
    //         if($detail->attribute_code != 'merk')
    //             $data[$detail->attribute_code] = $detail->value;
    //         if($detail->attribute_code != 'type')
    //             $data[$detail->attribute_code] = $detail->value;
    //     }

    //     // $item->serial_number = $detail['serial_number'];
    //     // $item->foto = $detail['detail_foto'];

    //     return response()->json([
    //         'data' => $data
    //     ]);
    // }

    public function update(Request $request, $id, $name)
    {
        $input = $request->all();
        $validasi = null;
        $code_asset = '';
        switch ($name) {
            case 'LAND':
            case 'land':
                $validasi = $this->landValidation($input);
                $code_asset = '01';
                break;
            case 'BUILDING':
            case 'building':
                $validasi = $this->buildingValidation($input);
                $code_asset = '02';
                break;
            case 'VEHICLE':
            case 'vehicle':
                $validasi = $this->vehicleValidation($input);
                $code_asset = '03';
                break;
            case 'COMPUTER SH/SW':
            case 'computer':
                $validasi = $this->computerValidation($input);
                $code_asset = '04';
                break;
            case 'OFFICE EQUIPMENT':
            case 'office-equipment':
                $validasi = $this->officeEquipmentValidation($input);
                $code_asset = '05';
                break;
            case 'MECHINARY':
            case 'mechinary':
                $validasi = $this->mechinaryValidation($input);
                $code_asset = '06';
                break;
            case 'FURNITURE':
            case 'furniture':
                $validasi = $this->furnitureValidation($input);
                $code_asset = '07';
                break;
            default:
                return response()->json([
                    'status' => false,
                    'message' => 'Data tidak ditemukan'
                ]);
        }
        if ($request->used == 1)
            $validasi = Validator::make($input, [
                'pic_user_id' => 'required',
            ]);

        $validasi = Validator::make($input, [
            'status' => 'required',
            'tanggal_penerimaan_barang' => 'required',
            'used' => 'required',
            'upload_doc' => 'required'
        ]);

        if ($validasi->fails())
            return response()->json([
                'status' => false,
                'message' => $validasi->errors()->all()
            ]);

        $data = Asset::find($id);
        DB::transaction(function() use ($data, $input, $request, $id, $code_asset) {
            // $purchase_price = str_replace([',', ' '], '', $input['harga_beli']) ?? '';

            // $category_asset = $data->categoryItem;
            // $company_new = Company::firstWhere('id', $input['company_id']);
            // $type_item_new = TypeItem::firstWhere('id', $input['type_item_id']);
            // $tahunaset_new = date('my', strtotime($input['tanggal_penerimaan_barang']));
            // $code_type_aset_new = (Helper::codeTypeAset($purchase_price) ?? '');

            // $code_first_new = ($company_new->code_number ?? '') . "." . $code_type_aset_new . "." . ($category_asset->code ?? '00') . "." . ($type_item_new->code ?? '00');
            // $code_center_new =  $tahunaset_new;
            // $code_new = $code_first_new . "." . $code_center_new;
            // $code_type_aset_last = (Helper::codeTypeAset($data->purchase_price_no_ppn) ?? '');

            // $company_last = $data->companies;
            // $type_item_last = $data->typeItem;
            // $code_first_last = ($company_last->code_number ?? '') . "." . $code_type_aset_last . "." . ($category_asset->code ?? '00') . "." . ($type_item_last->code ?? '00');
            // $code_center_last =  date('my', strtotime($data->tahun_bulan_aset));
            // $code_last = $code_first_last . "." . $code_center_last;

            // if($code_new != $code_last) {
            //     $code_aset = Helper::getCodeAset([
            //         'company_code' => $company_new->code_number ?? '',
            //         'code_type_aset' => $code_type_aset_new ?? '',
            //         'category_aset_code' => $category_asset->code ?? '',
            //         'type_item_code' => $type_item_new->code ?? '00',
            //         'tahun_bulan_asset' => $tahunaset_new,
            //     ]);
            //     $codes = $code_aset;
            // } else {
            //     $codes = $data->code;
            // }

            $data->update([
                // 'type_item_id' => $input['type_item_id'],
                // 'category_item_id' => $type_item_new->category_item_id,
                // 'company_id' => $input['company_id'] ?? 0,
                // 'department_id' => $input['department_id'] ?? 0,
                // 'section_id' => $input['section_id'] ?? 0,
                // 'location_id' => $input['location_id'] ?? 0,
                // 'code_type_aset' => $code_type_aset_new ?? 1,
                // 'category_asset_id' => $type_item_new->category_item_id ?? 0,
                // 'type_asset_id' => $type_item_new->id ?? 0,
                // 'detail_location' => Helper::detailLocationAdd(($input['detail_location'] == 'lainnya') ? $input['detail_location_other'] : $input['detail_location']),
                'spesification' => $input['spesifikasi'] ?? '',
                // 'good_receive_date' => $input['tanggal_penerimaan_barang'] ?? '',
                // 'tahun_bulan_aset' => $tahunaset_new,
                // 'pic_used_user_id' => intval($request->used) === 1 ? $request->pic_user_id ?? 0 : 0,
                // 'used' => $request->used,
                // 'uom' => $type_item_new->uom ?? '',
                // 'purchase_price_no_ppn' => $purchase_price,
            ]);

            $list_asset = ["status", "pic_user_id", "company_id", "section_id", "location_id", "tanggal_penerimaan_barang", "type_item_id", "spesifikasi", "used", "harga_beli", "upload_doc", "department_id", "detail_location",];

            foreach ($input as $key => $item) {
                if (!in_array($key, $list_asset)) {
                    $asset_detail[$key] = $item;
                    $detail = AssetDetail::where('asset_id', $id)->where('attribute_code', $key)->first();
                    if (!empty($detail)) {
                        if ($key !== 'upload_file_doc' && $request->hasFile($key)) {
                            $asset_detail[$key] = Helper::uploadFile($request->file($key), 'asset', $key);
                        }
                        AssetDetail::where('asset_id', $id)->where('attribute_code', $key)->update([
                            'value' => $asset_detail[$key]
                        ]);
                    } else {
                        if ($key !== 'upload_file_doc' && $request->hasFile($key)) {
                            $asset_detail[$key] = Helper::uploadFile($request->file($key), 'asset', $key);
                        }
                        $idDetails = AssetDetail::max('id') + 1;
                        AssetDetail::create([
                            'id' => $idDetails,
                            'asset_id' => $id,
                            'attribute_code' => $key,
                            'value' => $asset_detail[$key]
                        ]);
                    }
                }
            }
        });

        return response()->json([
            'status' => true,
            'message' => 'Data berhasil disimpan'
        ]);
    }

    public function edit(Request $request, $id)
    {
        $data = Asset::with(
            'companies:id,name',
            'department:id,name',
            'location:id,name',
            'categoryAsset:id,name,code',
            'typeAsset:id,name',
            'assetDetail',
            'section:id,name',
            'detailAsset',
            'detailLocation:id,name',
            'handoverDetail:id,asset_id,created_at',
            'typeItem:id,category_item_id,uom,name',
            'uploadDoc',
            'categoryItem:id,name,code',
            'picUsedUser:id,first_name,last_name,nip,full_name',
            'typeItem.simVehicle',
        )
            ->where('id', $id)
            ->first();

        foreach ($data->assetDetail as $item) {
            $data[$item->attribute_code] = $item->value;
            if ($data[$item->attribute_code] == 'type') {
                $item->merk = $item->value;
            } elseif ($data[$item->attribute_code] == 'upload_foto_furniture') {
                $item->upload_foto_furniture = $item->value;
            }

            if(strpos($item, "upload_foto") !== false) {
                $data[$item->attribute_code.'_preview'] = "/$item->value";
            }
        }

        $data['used'] = Helper::usedStatus($data['used']);
        $data['pic_user_id'] = $data->pic_user_used_id;

        $company = Company::orderBy('name', 'asc')->get();

        $type_item = TypeItem::select('id', 'name')
            ->where('category_item_id', $data->category_item_id)
            ->orderBy('name', 'asc')
            ->get();

        $detail_location = DetailLocation::select('id', 'name')
            ->orderBy('name', 'asc')
            ->get();
        $detail_location = $detail_location->map(function ($item) {
            return [
                'value' => $item->id,
                'label' => $item->name
            ];
        });

        if (!$data)
            return response()->json([
                'status' => false,
                'message' => 'Data tidak ditemukan'
            ]);

        return response()->json([
            'type_item' => $type_item,
            'company' => $company,
            'detail_location' => $detail_location,
            'status' => true,
            'data' => $data,
            'edit' => true
        ]);
    }

    public function getAsset(Request $request, $id)
    {
        $inId = explode(',', $id);
        $data = Asset::with(
            'companies:id,name',
            'department:id,name',
            'location:id,name',
            'categoryAsset:id,name,code',
            'typeAsset:id,name',
            'assetDetail',
            'section:id,name',
            'typeItem:id,uom,name',
            'detailLocation:id,name'
        )
            ->whereNotNull('code')
            ->latest()
            ->whereIn('id', $inId)
            ->filter($request)
            ->get();

        $company_first = $data->first()->companies ?? '';
        $department_first = $data->first()->department ?? '';
        $section_first = $data->first()->section ?? '';
        $location_first = $data->first()->location ?? '';
        $detail = new \stdClass();
        $detail->company = $company_first->name ?? '';
        $detail->company_id = $company_first->id ?? '';
        $detail->department = $department_first->name ?? '';
        $detail->department_id = $department_first->id ?? '';
        $detail->section = $section_first->name ?? '';
        $detail->section_id = $section_first->id ?? '';
        $detail->location = $location_first->name ?? '';
        $detail->location_id = $location_first->id ?? '';
        $detail->detail_location = $data->first()->detailLocation->name ?? '';

        foreach ($data as $item) {
            $item->type_asset = $item->typeAsset->name ?? '';
        }

        $company = Company::select('id', 'name')
            ->orderBy('name', 'ASC')
            ->get();
        $detail_location = DetailLocation::select('id', 'name')
            ->orderBy('name', 'asc')
            ->get();
        $detail_location = $detail_location->map(function ($item) {
            return [
                'value' => $item->id,
                'label' => $item->name
            ];
        });

        return response()->json([
            'data' => $data,
            'detail' => $detail,
            'company' => $company,
            'detail_location' => $detail_location
        ]);
    }

    public function processAsset(Request $request, $id)
    {
        $inId = explode(',', $id);
        $data = Asset::select('company_id', 'department_id', 'section_id', 'location_id')
            ->whereIn('id', $inId)
            ->get()
            ->toArray();

        $company_id = array_column($data, 'company_id');
        $departement_id = array_column($data, 'department_id');
        $section_id = array_column($data, 'section_id');
        $location_id = array_column($data, 'location_id');

        $company_id = Helper::checkSameObjectInArray($company_id);
        $departement_id = Helper::checkSameObjectInArray($departement_id);
        $section_id = Helper::checkSameObjectInArray($section_id);
        $location_id = Helper::checkSameObjectInArray($location_id);

        if ($company_id && $departement_id && $section_id && $location_id)
            return response()->json([
                'status' => true,
                'message' => 'Data berhasil diproses'
            ]);
        else
            return response()->json([
                'status' => false,
                'message' => 'Data Asset tidak dapat diproses dikarenakan ada data yang tidak sama'
            ]);
    }

    public function uploadMedia(Request $request)
    {
        $image = Helper::uploadFile($request->file('file'), 'serah-terima-asset', 'serah-terima-asset');

        return response()->json([
            'status' => true,
            'message' => 'Data berhasil disimpan',
            'url' => $image
        ]);
    }

    public function exportAsset(Request $request, $type_asset)
    {
        $jenis_aset = explode('-', $type_asset)[1] ?? '';
        $jenis_aset = strtoupper($jenis_aset);
        if ($jenis_aset == 'COMPUTER')
            $jenis_aset = 'COMPUTER HW/SW';
        if ($jenis_aset == 'OFFICE')
            $jenis_aset = 'OFFICE EQUIPMENT';
        $request->merge(['category' => $jenis_aset]);

        if ($jenis_aset)
            switch ($jenis_aset) {
                case 'LAND':
                case 'land':
                    $name_asset = 'LAND';
                    break;
                case 'BUILDING':
                case 'building':
                    $name_asset = 'BUILDING';
                    break;
                case 'OFFICE':
                case 'office equipment':
                    $name_asset = 'OFFICE EQUIPMENT';
                    break;
                case 'COMPUTER HW/SW':
                case 'computer':
                case 'COMPUTER':
                    $name_asset = 'COMPUTER SW-HW';
                    break;
                case 'OFFICE EQUIPMENT':
                case 'office':
                    $name_asset = 'OFFICE EQUIPMENT';
                    break;
                case 'MECHINERY':
                case 'mechinery':
                    $name_asset = 'MECHINERY';
                    break;
                case 'FURNITURE':
                case 'furniture':
                    $name_asset = 'FURNITURE';
                    break;
                case 'VEHICLE':
                case 'vehicle':
                    $name_asset = 'VEHICLE';
                    break;
                default:
                    return back()->with([
                        'status' => false,
                        'message' => 'Data tidak ditemukan'
                    ]);
            }


        $in_id = [];
        foreach($request->in_id ?? [] as $item) {
            $in_id[] = str_replace([',', '?', ':', '.'], '', $item);
        }
        $request['in_id'] = $in_id;
        $data = $this->index($request, 'export');
        // return view('export.listAssetItem', compact('data','jenis_aset'));
        return Excel::download(new ExportAssetItem($jenis_aset, $data), "List Data Asset {$name_asset} .xlsx");

        // return view('export.listAssetItem', compact('data'));
    }

    public function no_list_asuransi(Request $request)
    {
        $data =  Asset::with('assetDetail:id,asset_id,attribute_code,value', 'insurance', 'typeItem:id,category_item_id,uom,name')
            ->whereNull('waste_disposal_id')
            ->whereNotNull('code')
            ->whereHas('categoryItem', function ($query) {
                return $query->where('name', 'ilike', "%VEHICLE%");
            })
            ->latest()
            ->get();

        foreach ($data as $key => $item) {
            foreach (($item->detailAsset ?? []) as $detail) {
                $item[$detail->attribute_code] = $detail->value;
            }
            // $item->status_asuransi = '';
            // if(date('Y-m-d', strtotime($item['mulai_berakhir_polis'])) >= date('Y-m-d'))
            //     $item->status_asuransi = 'Aktif';
            // elseif(date('Y-m-d', strtotime($item['mulai_berakhir_polis'])) <= date('Y-m-d'))
            //     $item->status_asuransi = 'Expired';
        }

        return response()->json([
            'no_aset' => $data->pluck('code'),
            'nopol' => $data->groupBy('nomor_polisi')->keys(),
            'type_kendaraan' => $data->groupBy('typeItem.name')->keys(), // 'no_jenis' => $data->pluck('no_jenis'),
            'merk' => $data->groupBy('merk')->keys(),
        ]);
    }

    public function asuransiKendaraan(Request $request)
    {
        $select = explode(',', $request->select);
        $data =  Asset::with('assetDetail:id,asset_id,attribute_code,value', 'insurance', 'categoryItem', 'typeItem');
        $data = $data->whereNull('waste_disposal_id')
            ->whereNotNull('code')
            ->whereHas('categoryItem', function ($query) {
                return $query->where('name', 'ilike', "%VEHICLE%");
            })
            ->latest();

        if ($request->select) {
            $data = $data->select($select)
                ->latest()
                ->filter($request)
                ->whereNull('insurance_name')
                ->paginate(10)
                ->toArray();
            foreach ($data['data'] as $key => $item) {
                foreach (($item['asset_detail'] ?? []) as $detail) {
                    $data['data'][$key][$detail['attribute_code']] = $detail['value'];
                }
            }
            $results['data'] = $data['data'];
            unset($data['data']);
            $results['meta'] = $data;

            return response()->json($results);
        } else
            $data = $data->whereNotNull('insurance_name')->filter($request)->get();

        if (!$request->select)
            foreach ($data as $key => $item) {
                foreach (($item->detailAsset ?? []) as $detail) {
                    $item[$detail->attribute_code] = $detail->value;
                }
                $item->status_asuransi = '';
                if (date('Y-m-d', strtotime($item['mulai_berakhir_polis'])) >= date('Y-m-d'))
                    $item->status_asuransi = 'Aktif';
                elseif (date('Y-m-d', strtotime($item['mulai_berakhir_polis'])) <= date('Y-m-d'))
                    $item->status_asuransi = 'Expired';
            }

        return response()->json([
            'data' => $data
        ]);
    }

    public function asuransiKendaraanStore(Request $request)
    {
        $input = $request->all();
        $validasi = Validator::make($input, [
            'no_asset' => 'required',
            'insurance_name' => 'required',
            'mulai_berlaku_polis' => 'required',
            'mulai_berakhir_polis' => 'required',
            'jenis_pertanggungan' => 'required',
            'limit_klaim' => 'required'
        ]);
        if ($validasi->fails())
            return response()->json([
                'status' => false,
                'message' => $validasi->errors()->all()
            ]);

        $asset = Asset::find($request->no_asset);
        if (empty($asset))
            return response()->json([
                'status' => false,
                'message' => 'Data tidak ditemukan'
            ]);

        try {

            // $asset->insurance_id = $request->insurance_id;
            $asset->insurance_name = $request->insurance_name;
            $asset->save();
            $requestDetail = [
                'no_polis', 'mulai_berlaku_polis', 'mulai_berakhir_polis',
                'jenis_pertanggungan', 'limit_klaim', 'keterangan_asuransi'
            ];
            $assetDetails = [];
            foreach ($requestDetail as $rd) {
                $assetDetails['id'] = AssetDetail::max('id') + 1;
                $assetDetails['attribute_code'] = $rd;
                $assetDetails['asset_id'] = $asset->id;
                $assetDetails['value'] = $input[$rd] ?? null;
                AssetDetail::create($assetDetails);
            }

            return response()->json([
                'status' => true,
                'message' => 'Data berhasil disimpan'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    public function exportAsuransiKendaraan(Request $request)
    {
        $no_asset = $request->no_asset;
        $nopol = $request->nopol;
        $merk = $request->merk;
        $no_jenis = $request->no_jenis;
        $start_date_asc = $request->start_date_asc;
        $to_date_asc = $request->to_date_asc;

        return Excel::download(new ExportAsuransiKendaraan($nopol, $merk, $no_jenis, $start_date_asc, $to_date_asc, $no_asset), "List Asuransi Kendaraan.xlsx");
    }

    public function reset(Request $request)
    {
        $data = Asset::find($request->asset_id);

        $data->km_actual = 0;

        if ($data->save()) {
            return response()->json([
                'success' => TRUE,
                'message' => "Berhasil Di Reset"
            ]);
        } else {
            return response()->json([
                'success' => FALSE,
                'message' => "Gagal Di reset"
            ]);
        }
    }

    public function cek_nopol(Request $request)
    {
        $nopol = $request->nopol;

        $getNopol = AssetDetail::where(function ($query) use ($nopol) {
            $query->where('attribute_code', 'nomor_polisi')
                ->where('value', 'like', "%$nopol%");
        })
            ->first();

        if ($getNopol != null) {
            $data = Asset::find($getNopol->asset_id);

            if (!empty($data)) {
                return response()->json([
                    'status' => TRUE,
                    'nomor_polisi' => $nopol,
                    'asset_id' => $getNopol->asset_id,
                    'message' => "Nomor Polisi" . " " . $nopol . " " . "Tersedia"
                ]);
            } else {
                return response()->json([
                    'status' => FALSE,
                    'message' => "Nomor Polisi" . " " . $nopol . " " . "Tidak Ditemukan"
                ]);
            }
        } else {
            return response()->json([
                'status' => FALSE,
                'message' => "Nomor Polisi" . " " . $nopol . " " . "Tidak Ditemukan"
            ]);
        }
    }

    public function list_nopol()
    {
        $data = AssetDetail::where(function ($query) {
            $query->where('attribute_code', 'nomor_polisi');
        })
            ->get();

        return response()->json([
            'data' => $data,
            'status' => TRUE,
            'total_data' => count($data),
            'message' => "SUCCESS"
        ]);
    }

    public function importVehicle(Request $request)
    {
        try {
            Excel::import(new ImportAssetVehicle, $request->file('file'));
            return response()->json(['success' => true, 'message' => 'Berhasil import data vehicle']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }
    public function importEquipment(Request $request)
    {
        try {
            Excel::import(new ImportAssetEquipment, $request->file('file'));
            return response()->json(['success' => true, 'message' => 'Berhasil import data aset equipment']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }
    public function importLand(Request $request)
    {
        try {
            Excel::import(new ImportAssetLand, $request->file('file'));
            return response()->json(['success' => true, 'message' => 'Berhasil import data asset tanah']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }
    public function importBuilding(Request $request)
    {
        try {
            Excel::import(new ImportAssetBuilding, $request->file('file'));
            return response()->json(['success' => true, 'message' => 'Berhasil import data aset building']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }
    public function importComputer(Request $request)
    {
        try {
            Excel::import(new ImportAssetComputer, $request->file('file'));
            return response()->json(['success' => true, 'message' => 'Berhasil import data aset computer']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }
    public function importMechinary(Request $request)
    {
        try {
            Excel::import(new ImportAssetMechinary, $request->file('file'));
            return response()->json(['success' => true, 'message' => 'Berhasil import data aset mechinary']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }
    public function importFurniture(Request $request)
    {
        try {
            Excel::import(new ImportAssetFurniture, $request->file('file'));
            return response()->json(['success' => true, 'message' => 'Berhasil import data aset furniture']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    public function importAsuransiKendaraan(Request $request)
    {
        try {
            Excel::import(new ImportAsuransiKendaraan, $request->file('file'));
            return response()->json(['success' => true, 'message' => 'Berhasil import data asuransi kendaraan']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }
}
