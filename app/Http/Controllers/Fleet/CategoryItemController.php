<?php

namespace App\Http\Controllers\Fleet;

use App\Http\Controllers\Controller;
use App\Models\Fleet\CategoryItem;
use Illuminate\Http\Request;

class CategoryItemController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $columns = ['name', 'code'];
        $keyword = $request->get('filter');

        $data = CategoryItem::where(function($result) use ($keyword,$columns){
            foreach($columns as $column)
            {
                if($keyword != ''){
                    $result->orWhere($column,'ILIKE','%'.$keyword.'%');
                }
            }
        })->orderBy('id', 'desc')->get();

        foreach ($data as $key => $value) {
            if ($value->type_asset == 1) {
                $value->type = 'Asset';
            }elseif ($value->type_asset == 2) {
                $value->type = 'Non Asset';
            }elseif ($value->type_asset == 3) {
                $value->type = 'Jasa';
            }
        }

        if ($request->type_submission) {
            $data = CategoryItem::where('type_asset', $request->type_submission)
            ->where(function($result) use ($keyword,$columns){
                foreach($columns as $column)
                {
                    if($keyword != ''){
                        $result->orWhere($column,'ILIKE','%'.$keyword.'%');
                    }
                }
            })->orderBy('code', 'asc')->get();
        }

        return response()->json($data);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        try{
            $data = $request->all();
            $create = CategoryItem::create($data);
            
            return response()->json(['success' => true, 'message' => 'Berhasil input data']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $data = CategoryItem::findOrFail($id);
        return response()->json($data);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        try{
            $data = $request->all();
            $edit = CategoryItem::findOrFail($id)->update($data);
            
            return response()->json(['success' => true, 'message' => 'Berhasil edit data']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $data = CategoryItem::findOrFail($id);
        $data->delete();
        $response = array(
            'success' => true,
            'message' => 'Berhasil hapus data'
        );

        return response()->json($response);
    }
}
