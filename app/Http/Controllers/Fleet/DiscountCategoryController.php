<?php

namespace App\Http\Controllers\Fleet;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Fleet\DiscountCategory;
use Illuminate\Support\Facades\Validator;

class DiscountCategoryController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $data = DiscountCategory::orderBy('id','desc')->get();
        return response()->json($data);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'code' => 'required',
                'name' => 'required',
                'discount_percent' => 'required|numeric'
            ]);
    
            if ($validator->fails()) {
                return response()->json($validator->errors(), 500);
            }

            $data = $request->all();
            $supplier = DiscountCategory::create($data);

            return response()->json(['success' => true, 'message' => 'Berhasil input data']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $data = DiscountCategory::find($id);
        return response()->json($data);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        try {
            $validator = Validator::make($request->all(), [
                'code' => 'required',
                'name' => 'required',
                'discount_percent' => 'required|numeric'
            ]);
    
            if ($validator->fails()) {
                return response()->json($validator->errors(), 500);
            }

            $data = $request->all();
            $supplier = DiscountCategory::find($id)->update($data);

            return response()->json(['success' => true, 'message' => 'Berhasil update data']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $divisi = DiscountCategory::destroy($id);

        return response()->json(['success' => true, 'message' => 'Berhasil delete data']);
    }
}
