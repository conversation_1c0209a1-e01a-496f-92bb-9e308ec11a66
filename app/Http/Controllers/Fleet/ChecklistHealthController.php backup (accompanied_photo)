<?php

namespace App\Http\Controllers\Fleet;

use App\Http\Controllers\Controller;
use App\Models\Fleet\AnswerQuestion;
use App\Models\Fleet\AnswerQuestionDetail;
use App\Models\Fleet\AnswerQuestionUser;
use App\Models\Fleet\CategoryQuestion;
use App\Models\Fleet\Question;
use App\Models\Fleet\QuestionMark;
use App\Models\Fleet\StatusVehicle;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ChecklistHealthController extends Controller
{
    public function index()
    {
        $auth = Auth::user()->role;
        $data = AnswerQuestionUser::with('answerQuestionDetail', 'statusVehicle')->where('slug', 'checklist-health')->orderBy('id', 'desc')->get();
        foreach ($data as $key => $res) {
            $sv = $res->statusVehicle->start ?? 0;
            if ($res['total_point'] <= $sv) {
                $data[$key]['ready'] = 0;
            }else{
                $data[$key]['ready'] = 1;
            }
        }

        return response()->json(['data' => $data, 'user' => $auth]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create(Request $request)
    {
        $route = $request->slug;
        if ($route) {
            $data = CategoryQuestion::where('slug', 'checklist-health')->orderBy('id', 'asc')->with('question.answer')->get();
            // $data['success'] = true;
            return response()->json($data);
        }else{
            return response()->json(['success' => false, 'messages' => 'not found!']);
        }
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        try{
            $data = $request->all();
            // return $data;
            $answerQuestionUser['status'] = 0;
            $answerQuestionUser['user_id'] = Auth::user()->id;
            $answerQuestionUser['slug'] = 'checklist-health';
            $answerQuestionUser['location_checklist_id'] = $data[0]['location_checklist_id'];
            $answerUser = AnswerQuestionUser::create($answerQuestionUser);
            
            $CategoryQuestion = array();
            foreach ($data as $key => $res) {
                $question = Question::findOrFail($res['question_id']);
                $answer = AnswerQuestion::findOrFail($res['answer_id']);
                $res['question'] = $question->question;
                $res['answer'] = $answer->answer;
                $res['point'] = $answer->point;
                $image_parts = explode(";base64,", $res['image']);
                if ($image_parts) {
                    $image_type_aux = explode("image/", $image_parts[0]);
                    $image_type = $image_type_aux[1];
                    if ($image_type == 'jpg' || $image_type == 'jpeg' || $image_type == 'png') {
                        $image_base64 = base64_decode($image_parts[1]);
                        $folderPath = 'storage/cheklist/';
                        $imageName = uniqid();
                        $imageFullPath = $folderPath.$imageName.".".$image_type;
                        file_put_contents($imageFullPath, $image_base64);
                        $res['image'] = $imageFullPath;
                    }else{
                        return "Format gambar tidak sesuai, Tipe gambar hanya jpg, jpeg, png";
                    }
                }
                $res['answer_question_user_id'] = $answerUser->id;
                $res['answer_question_id'] = $res['answer_id'];
                // dd($res);
                // $CategoryQuestion[$key] = AnswerQuestionUser::create($res);

                $CategoryQuestion[$key] = AnswerQuestionDetail::create($res);
            }

            $point = 0;
            foreach ($CategoryQuestion as $answer) {
                // dd($answer->point);
                $point += (int)$answer->point;
            }

            try {
                $color = StatusVehicle::where('start', '<=', $point)->where('to', '>=', $point)->first()->id;
            } catch (\Throwable $th) {
                $color = StatusVehicle::orderBy('to', 'desc')->first()->id;
            }

            // $color = StatusVehicle::where('start', '<=', $point)->where('to', '>=', $point)->first()->id;
            $qPoint['status_vehicle_id'] = $color;
            $qPoint['total_point'] = $point;
            $qPoint['input_date'] = now();
            $qPoint['status'] = 1;
            AnswerQuestionUser::findOrFail($answerUser->id)->update($qPoint);

            return response()->json(['success' => true, 'message' => 'Selamat anda mendapatkan '.$point.' point']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        // 
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $data = AnswerQuestionUser::findOrFail($id);
        if ($data['total_point'] <= $data->statusVehicle->start) {
            $data['ready'] = 0;
        }else{
            $data['ready'] = 1;
        }

        return response()->json($data);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        try{
            $data = $request->all();
            $CategoryQuestion = AnswerQuestionUser::findOrFail($id)->update($data);
            
            return response()->json(['success' => true, 'message' => 'Berhasil edit data']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $data = QuestionMark::findOrFail($id);
        $data->delete();
        $response = array(
            'success' => true,
            'message' => 'Berhasil hapus data'
        );

        return response()->json($response);
    }
}
