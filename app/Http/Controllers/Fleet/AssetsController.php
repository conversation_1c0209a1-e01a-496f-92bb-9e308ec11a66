<?php

namespace App\Http\Controllers\Fleet;

use App\Exports\ExportAsset;
use App\Http\Controllers\Controller;
use App\Http\Helper;
use App\Imports\ImportAsset;
use Illuminate\Http\Request;
use App\Models\Fleet\Asset;
use App\Models\Fleet\AssetDetail;
use App\Models\Fleet\AssetLog;
use App\Models\Fleet\AssetMasterAttribute;
use App\Models\Fleet\LocStock;
use App\Models\Fleet\AssetMasterClass;
use App\Models\Fleet\AssetMasterGroup;
use App\Models\Fleet\CategoryAsset;
use App\Models\Fleet\Company;
use App\Models\Fleet\Department;
use App\Models\Fleet\MasterLocation;
use App\Models\Fleet\TypeAsset;
use App\Models\User;
use App\Workshop\Insurance;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;
use Auth;
use Maatwebsite\Excel\Facades\Excel;

class AssetsController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $assets = Asset::with(['stock','createdBy','requester','onHand', 'categoryAsset', 'typeAsset'])->orderBy('id','desc')->get();
        foreach ($assets as $key => $asset) {
            $asset->show_qr = false;
        }

        return response()->json(['assets' => $assets]);
    }

    public function index2(Request $request)
    {
        $company = [];
        if ($request->has('company')) {
            $company_get = $request->input('company');
            if (is_array($company_get)) {
                for ($i=0; $i < count($company_get); $i++) { 
                    $company[] = $company_get[$i];
                }
            }
        }

        $departement = [];
        if ($request->has('departement')) {
            $departement_get = $request->input('departement');
            if (is_array($departement_get)) {
                for ($i=0; $i < count($departement_get); $i++) { 
                    $departement[] = $departement_get[$i];
                }
            }
        }

        $location = [];
        if ($request->has('location')) {
            $location_get = $request->input('location');
            if (is_array($location_get)) {
                for ($i=0; $i < count($location_get); $i++) { 
                    $location[] = $location_get[$i];
                }
            }
        }

        $companies = Company::select('name','id')->get();
        $departments = Department::select('name','id')->get();
        $locations = MasterLocation::select('name','id')->get();

        $assets = Asset::with(['categoryAsset', 'typeAsset', 'assetDetail', 'insurance'])
        ->where(function($q) use($company,$location,$departement){
            if (is_array($company) && count($company)) {
                $q->whereIn('company_id',$company);
            }
            if (is_array($location) && count($location)) {
                $q->whereIn('location_id',$location);
            }
            if (is_array($departement) && count($departement)) {
                $q->whereIn('department_id',$departement);
            }
        })->orderBy('id','desc')->get();

        foreach ($assets as $key => $value) {
            $value->show_qr = false;
            $value->show_foto_stnk = false;
            $value->show_foto_pajak_stnk = false;
            $value->show_foto_barcode_kir = false;
            $value->show_foto_kartu_kir = false;
            $value->show_foto_kendaraan_depan = false;
            $value->show_foto_kendaraan_samping_kanan = false;
            $value->show_foto_kendaraan_samping_kiri = false;
            $value->show_foto_belakang_kendaraan = false;
            $value->show_foto_dalam_box = false;
            $value->show_foto_kabin_kendaraan = false;
            foreach ($value->assetDetail as $ckey => $cvalue) {
                $value[$cvalue->attribute_code] = $cvalue->value;
            }
        }

        return response()->json(['assets' => $assets, 'companies' => $companies, 'departments' => $departments, 'locations' => $locations]);
    }

    public function pickupUpdate(Request $request)
    {
        try {
            $id = $request->input('id');

            $update = Asset::find($id);
            if ($update) {
                $update->status = 1;
                $update->pickup_date = Carbon::now();
                $update->save();
            }
            return response()->json(['success' => true, 'message' => 'berhasil update data'], 200);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $category_asset = CategoryAsset::orderBy('name', 'asc')->get();
        $company = Company::orderBy('name', 'asc')->get();
        $category_asset = CategoryAsset::orderBy('name', 'asc')->get();
        $type_asset = TypeAsset::orderBy('name', 'asc')->get();
        $departement = Department::orderBy('name', 'asc')->get();
        $location = MasterLocation::orderBy('name', 'asc')->get();

        return response()->json(['category_asset' => $category_asset,
                                    'company' => $company,
                                    'category_asset' => $category_asset,
                                    'type_asset' => $type_asset,
                                    'departement' => $departement,
                                    'location' => $location]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        try {
            $dataAsset = $request->asset;
            $code = $dataAsset['company'].".".$dataAsset['category_asset'].".".$dataAsset['type_asset'].".".now()->format('d').".".now()->format('Y').".";
            $assetCode = Asset::where('code', 'LIKE', '%'.$code.'%')->count();
            $randInt = '00001';
            if ($assetCode >= 1) {
                $count = $assetCode+1;
                $randInt = '0000'.(string)$count;
            }
            $randInt = substr($randInt, -5);
			$dataAsset['id'] = Asset::max('id') + 1;
            $dataAsset['code'] = $code.$randInt;
            $dataAsset['created_asset'] = now();
            $dataAsset['pickup_date'] = now();
            $dataAsset['location_id'] = $dataAsset['location'];
            $dataAsset['category_asset_id'] = CategoryAsset::where('code', $dataAsset['category_asset'])->first()->id ?? null;
            $dataAsset['company_id'] = Company::where('code_number', $dataAsset['company'])->first()->id ?? null;
            $dataAsset['company'] = Company::where('code_number', $dataAsset['company'])->first()->name ?? null;
            $dataAsset['department_id'] = $dataAsset['departement'];
            $dataAsset['type_asset_id'] = TypeAsset::where('code', $dataAsset['type_asset'])->first()->id ?? null;
            $createAsset = Asset::create($dataAsset);
            // return $createAsset;
            foreach ($request->assetDetail as $key => $assetDetail) {
                if ($key == 'foto_stnk' && $assetDetail != null) {
                    $assetDetail['foto_stnk'] = Helper::saveImage($assetDetail, 'foto_stnk');
                }
        
                if ($key == 'foto_pajak_stnk' && $assetDetail != null) {
                    $assetDetail = Helper::saveImage($assetDetail, 'foto_pajak_stnk');
                }
        
                if ($key == 'foto_barcode_kir' && $assetDetail != null) {
                    $assetDetail = Helper::saveImage($assetDetail, 'foto_barcode_kir');
                }
        
                if ($key == 'foto_kartu_kir' && $assetDetail != null) {
                    $assetDetail = Helper::saveImage($assetDetail, 'foto_kartu_kir');
                }
        
                if ($key == 'foto_kendaraan_depan' && $assetDetail != null) {
                    $assetDetail = Helper::saveImage($assetDetail, 'foto_kendaraan_depan');
                }
        
                if ($key == 'foto_kendaraan_samping_kanan' && $assetDetail != null) {
                    $assetDetail = Helper::saveImage($assetDetail, 'foto_kendaraan_samping_kanan');
                }
        
                if ($key == 'foto_kendaraan_samping_kiri' && $assetDetail != null) {
                    $assetDetail = Helper::saveImage($assetDetail, 'foto_kendaraan_samping_kiri');
                }
        
                if ($key == 'foto_belakang_kendaraan' && $assetDetail != null) {
                    $assetDetail = Helper::saveImage($assetDetail, 'foto_belakang_kendaraan');
                }
        
                if ($key == 'foto_dalam_box' && $assetDetail != null) {
                    $assetDetail = Helper::saveImage($assetDetail, 'foto_dalam_box');
                }
        
                if ($key == 'foto_kabin_kendaraan' && $assetDetail != null) {
                    $assetDetail = Helper::saveImage($assetDetail, 'foto_kabin_kendaraan');
                }
                
                $dataAssetDetail['id'] = AssetDetail::max('id') + 1;
                $dataAssetDetail['asset_id'] = $createAsset->id;
                $dataAssetDetail['attribute_code'] = $key;
                $dataAssetDetail['value'] = $assetDetail;
                $assetDetail = AssetDetail::create($dataAssetDetail);
            }

            return response()->json(['success' => true, 'message' => 'Berhasil Create Data']);
        } catch (\Throwable $th) {
            return response()->json(['success' => false, 'message' => $th->getMessage()], 500);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $data = AssetLog::with('user')->where('asset_id',$id)->get();

        return response()->json(['data' => $data]);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $data = Asset::with('categoryAsset', 'typeAsset')->findOrFail($id);
        foreach ($data->assetDetail as $key => $value) {
            $data[$value->attribute_code] = $value->value;
        }
        $companies = Company::select('name','id')->get();
        $departments = Department::select('name','id')->get();
        $locations = MasterLocation::select('name','id')->get();
        $category_asset = CategoryAsset::select('name','id')->get();

        return response()->json(['data' => $data, 'category_asset' => $category_asset, 'companies' => $companies, 'departments' => $departments, 'locations' => $locations]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */

    public function update(Request $request, $id)
    {
        try {
            $validator = Validator::make($request->all(), [
                'code' => 'required',
            ]);
                
            if ($validator->fails()) {
                return response()->json($validator->errors(), 500);
            }

            $row = $request->all();
            $dataAsset = Asset::findOrFail($id) ?? null;
            $data['code'] = $row['code'];
            // $data['company_id'] = Company::where('name', 'ilike', "%".$row['company']."%")->first()->id ?? null;
            // $data['department_id'] = Department::where('name', 'ilike', "%".$row['departement']."%")->first()->id ?? null;
            // $data['location_id'] = MasterLocation::where('name', 'ilike', "%".$row['location']."%")->first()->id ?? null;
            // if ($data['location_id'] == null) {
            //     $dataLokasi['name'] = $row['location'];
            //     $dataLokasi['code'] = 'custom';
            //     $location = MasterLocation::create($dataLokasi);
            //     $data['location_id'] = $location->id;
            // }
            // $data['company'] = $row['company'];
            $data['created_asset'] = now()->format('Y-m-d');
            $data['pickup_date'] = now()->format('Y-m-d');
            $data['status'] = 1;
            $data['in_stock'] = 1;
            // $category_asset = CategoryAsset::findOrFail($row['category_asset_id']);
            // $data['category_asset_id'] = $category_asset->id ?? null;
            // $data['type_asset_id'] = TypeAsset::where('name', $row['type_asset'])->first()->id ?? null;
            $dataAsset->update($data);
            
            // FOTO
            //if ($row['foto_stnk']) {
             //   $row['foto_stnk'] = Helper::saveImage($row['foto_stnk'], 'foto_stnk');
            //}

            //if ($row['foto_pajak_stnk']) {
             //   $row['foto_pajak_stnk'] = Helper::saveImage($row['foto_pajak_stnk'], 'foto_pajak_stnk');
            //}

            //if ($row['foto_barcode_kir']) {
             //   $row['foto_barcode_kir'] = Helper::saveImage($row['foto_barcode_kir'], 'foto_barcode_kir');
            //}

            //if ($row['foto_kartu_kir']) {
            //    $row['foto_kartu_kir'] = Helper::saveImage($row['foto_kartu_kir'], 'foto_kartu_kir');
            //}

            //if ($row['foto_kendaraan_depan']) {
            //    $row['foto_kendaraan_depan'] = Helper::saveImage($row['foto_kendaraan_depan'], 'foto_kendaraan_depan');
            //}

            //if ($row['foto_kendaraan_samping_kanan']) {
             //   $row['foto_kendaraan_samping_kanan'] = Helper::saveImage($row['foto_kendaraan_samping_kanan'], 'foto_kendaraan_samping_kanan');
            //}

            //if ($row['foto_kendaraan_samping_kiri']) {
            //    $row['foto_kendaraan_samping_kiri'] = Helper::saveImage($row['foto_kendaraan_samping_kiri'], 'foto_kendaraan_samping_kiri');
            //}

            //if ($row['foto_belakang_kendaraan']) {
            //    $row['foto_belakang_kendaraan'] = Helper::saveImage($row['foto_belakang_kendaraan'], 'foto_belakang_kendaraan');
            //}

            //if ($row['foto_dalam_box']) {
            //    $row['foto_dalam_box'] = Helper::saveImage($row['foto_dalam_box'], 'foto_dalam_box');
            //}

            //if ($row['foto_kabin_kendaraan']) {
            //    $row['foto_kabin_kendaraan'] = Helper::saveImage($row['foto_kabin_kendaraan'], 'foto_kabin_kendaraan');
            //}

            $assetMasterAttribute = AssetMasterAttribute::where('category_id', $dataAsset->category_asset_id)->get();

            foreach ($assetMasterAttribute as $key => $value) {
                $assetDetails['attribute_code'] = $value->code;
                $assetDetails['asset_id'] = $dataAsset->id;
                $assetDetails['value'] = $row[$value->code] ?? null;
				$asd = AssetDetail::where('asset_id', $dataAsset->id)->where('attribute_code', $value->code)->first();
				if ($asd) {
					$asd->update($assetDetails);
				}
                //$assetDetail = AssetDetail::where('asset_id', $dataAsset->id)->where('attribute_code', $value->code)->first()->update($assetDetails);
            }
            
            return response()->json(['success' => true, 'message' => 'Berhasil update data']);
        } catch (\Throwable $th) {
            return response()->json(['success' => false, 'message' => $th->getMessage()], 500);
        }
    }
    // public function update(Request $request, $id)
    // {
    //     try {
    //         $validator = Validator::make($request->all(), [
    //             'code' => 'required',
    //         ]);
    
    //         if ($validator->fails()) {
    //             return response()->json($validator->errors(), 500);
    //         }

    //         //check update
    //         $log = "";
    //         $status = [0 => 'Ready Pickup', 1 => 'Already Pickup'];
    //         $asset = Asset::find($id);
    //         if ($asset->code != $request->code) {
    //             $log = 'Label di update dari '.$asset->code.' menjadi '.$request->code;
    //         }
    //         if ($asset->status != $request->status) {
    //             $log = 'Status di update ke '.$status[$request->status];
    //         }
    //         if ($request->status == 1 && $asset->on_hand_id != $request->on_hand_id) {
    //             $user_from = User::find($asset->on_hand_id);
    //             $user_to = User::find($request->on_hand_id);
    //             if ($user_from) {
    //                 $log = 'Asset di transfer dari '.$user_from->first_name.' '.$user_from->lastname.' Ke '.$user_to->first_name.' '.$user_to->lastname;
    //             }else{
    //                 $log = 'Asset di transfer Ke '.$user_to->first_name.' '.$user_to->lastname;
    //             }
    //         }
    //         if ($request->status == 1 && $asset->code != $request->code && $asset->on_hand_id != $request->on_hand_id) {
    //             $user_from = User::find($asset->on_hand_id);
    //             $user_to = User::find($request->on_hand_id);
    //             if ($user_from) {
    //                 $log = 'Label di update dari '.$asset->code.' menjadi '.$request->code.' dan di transfer dari '.$user_from->first_name.' '.$user_from->lastname.' Ke '.$user_to->first_name.' '.$user_to->lastname;
    //             }else{
    //                 $log = 'Label di update Ke '.$user_to->first_name.' '.$user_to->lastname;
    //             }
    //         }
    //         if ($request->status == 1 && $asset->code != $request->code && $asset->on_hand_id != $request->on_hand_id && $asset->status != $request->status) {
    //             $user_from = User::find($asset->on_hand_id);
    //             $user_to = User::find($request->on_hand_id);
    //             if ($user_from) {
    //                 $log = 'Label di update dari '.$asset->code.' menjadi '.$request->code.' status di update ke '.$status[$request->status].' dan di transfer dari '.$user_from->first_name.' '.$user_from->lastname.' Ke '.$user_to->first_name.' '.$user_to->lastname;
    //             }else{
    //                 $log = 'Label di update Ke '.$user_to->first_name.' '.$user_to->lastname.' Status di update ke '.$status[$request->status];
    //             }
    //         }

    //         $data = $request->all();
    //         if ($request->status == 0) {
    //            $data['on_hand_id'] = NULL;
    //            $data['in_stock'] = 0;
    //         }else{
    //            $data['in_stock'] = 1;
    //         }
    //         $data['class_id'] = $request->class_id;
    //         $data['group_id'] = $request->group_id;
    //         $update = Asset::find($id)->update($data);

    //         //tambah stock di locstock
    //         if ($update) {
    //             $asset_get = Asset::find($id);
    //             if ($request->status == 0) {
    //                 //update qty in table lockstock
    //                 $cek_lockstock = LocStock::where('location_id',$asset_get->po->into_stock_location)
    //                 ->where('stock_id',$asset_get->stock_id)
    //                 ->first();

    //                 if ($cek_lockstock) {
    //                     $lockstock = LocStock::find($cek_lockstock->id);

    //                     $lockstock->quantity = $lockstock->quantity + 1;
    //                     $lockstock->qty_pickup = $lockstock->qty_pickup - 1;
    //                     $lockstock->save();
    //                 }
    //             }else{
    //                 //update qty in table lockstock
    //                 $cek_lockstock = LocStock::where('location_id',$asset_get->po->into_stock_location)
    //                 ->where('stock_id',$asset_get->stock_id)
    //                 ->first();

    //                 if ($cek_lockstock) {
    //                     $lockstock = LocStock::find($cek_lockstock->id);

    //                     $lockstock->quantity = $lockstock->quantity - 1;
    //                     $lockstock->qty_pickup = $lockstock->qty_pickup + 1;
    //                     $lockstock->save();
    //                 }
    //             }
    //         }

    //         if ($log != "") {
    //             $insert_log = new AssetLog;
    //             $insert_log->date_log = Carbon::now();
    //             $insert_log->asset_id = $id;
    //             $insert_log->user_update = Auth::user()->id;
    //             $insert_log->log_description = $log;
    //             $insert_log->save();
    //         }

    //         return response()->json(['success' => true, 'message' => 'Berhasil update data']);
    //     } catch (\Exception $e) {
    //         return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
    //     }
    // }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }

    public function setLokasi()
    {
        $data = Asset::all();
        foreach ($data as $key => $datas) {
            $lokasi = $datas->po->into_stock_location;
            $update = Asset::where('id',$datas->id)->update(['location_id' => $lokasi]);
        }
    }

    public function importAsset(Request $request)
    {
        try {
            $file_parts = explode(";base64,", $request->file);
            if ($file_parts) {
                $file_type_aux = explode("application/", $file_parts[0]);
                $file_type = 'xlsx';
                $file_base64 = base64_decode($file_parts[1]);
                $folderPath = 'storage/assets/';
                $fileName = uniqid();
                $fileFullPath = $folderPath.$fileName.".".$file_type;
                file_put_contents($fileFullPath, $file_base64);
                $assets = $fileFullPath;
                // return public_path($assets);
                Excel::import(new ImportAsset, $assets);
                return response()->json(['status' =>'Berhasil Import Data']);
            }else {
                return response()->json(['status' =>'excel tidak tersedia']);
            }
    
        } catch (\Throwable $th) {
            return response()->json(['status' => $th->getMessage()]);
        }
    }

    public function export()
    {
        // $data = Asset::get();
        // foreach ($data as $key => $value) {
        //     foreach ($value->assetDetail as $ckey => $cvalue) {
        //         $value[$cvalue->attribute_code] = $cvalue->value;
        //     }
        // }

        // return view('export.asset', ['data' => $data]);
        return Excel::download(new ExportAsset(), 'List Data Asset.xlsx');
    }
}
