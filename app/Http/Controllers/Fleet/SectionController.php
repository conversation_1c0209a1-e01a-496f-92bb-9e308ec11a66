<?php

namespace App\Http\Controllers\Fleet;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Fleet\Section;
use Illuminate\Support\Facades\Validator;
use App\Models\Fleet\Department;
use App\Models\User;

class SectionController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $data = Section::with('department')->orderBy('id','desc')->get();
        return response()->json($data);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $department = Department::orderBy('id','desc')->get();
        return response()->json(['department' => $department]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $section = User::get()->groupBy('section_id');
        $code = [];
        foreach ($section as $key => $value) {
            $com = Section::where('code', $value->first()->section_id)->first();
            if ($com) {
                $com->name = $value->first()->section_name;
                $com->save();
                array_push($code, $value->first()->section_id);
            }else {
                $data['code'] = $value->first()->section_id;
                $data['name'] = $value->first()->section_name;
                $create = Section::create($data);
                array_push($code, $value->first()->section_id);
            }
        }
        $delete = Section::whereNotIn('code', $code)->delete();
        // $validator = Validator::make($request->all(), [
        //     'name' => 'required',
        //     'department_id' => 'required'
        // ]);
 
        // if ($validator->fails()) {
        //     return response()->json($validator->errors(), 500);
        // }
        
        // $section = Section::create($request->all());

        return response()->json(['success' => true, 'message' => 'Berhasil Sinkronkan data']);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $data = Section::with('SubSection')->find($id);
        return response()->json($data);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $data = Section::find($id);
        $department = Department::orderBy('id','desc')->get();
        return response()->json(['data' => $data, 'department' => $department]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required',
            'department_id' => 'required'
        ]);
 
        if ($validator->fails()) {
            return response()->json($validator->errors(), 500);
        }
        
        $section = Section::find($id)->update($request->all());

        return response()->json(['success' => true, 'message' => 'Berhasil update data']);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $section = Section::destroy($id);

        return response()->json(['success' => true, 'message' => 'Berhasil delete data']);
    }
}
