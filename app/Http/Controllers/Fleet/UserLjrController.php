<?php

namespace App\Http\Controllers\Fleet;

use App\Http\Controllers\Controller;
use App\Models\Fleet\UserLjr;
use App\Models\Fleet\Driver;
use App\Models\Fleet\TypeSim;
use App\Models\Fleet\MasterLocation;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Http;

class UserLjrController extends Controller
{
    public function index()
    {
        $data = User::get();

        return response()->json(['status' => true, 'data' => $data]);
    }

    public function employee()
    {
        $data = Http::get('http://lestarijayaraya.co.id:8009/api/karyawan/api_tampil?api_key=Y4BoXmabMlK7BQx&employee_status=1&employe_status=1')->json();

        return $data;
    }


    public function login(Request $request)
    {
        $input = $request->all();
        if (Auth::attempt(array('username' => $input['username'], 'password' => $input['password']))) {
            $user = Auth::user();
            if($user->status_user == 2)
                return response(['message' => 'Akun anda tidak aktif, silahkan hubungi admin!'], 403);

            $token = $user->createToken('API Token')->accessToken;

            $user_las_login = DB::table('oauth_access_tokens')
                ->where('user_id', Auth::user()->id)
                ->orderBy('created_at', 'desc')->first();

            // DB::table('oauth_access_tokens')
            // ->where('id', $user_las_login->id)
            // ->where('user_id', Auth::user()->id)
            // ->update([
            //     'via' => $request->via,
            // ]);
            // $user_las_login->save();

            // DB::table('oauth_access_tokens')
            // ->where('user_id', Auth::user()->id)
            // ->where('id', '!=', $user_las_login->id)
            // ->where('via', $request->via)
            // ->update([
            //     'revoked' => true,
            // ]);

            return response([
                'message' => 'success',
                'token' => $token,
                'user' => $user
            ]);
        }else{
            return response(['message' => 'email & password salah, silahkan coba lagi!'], 200);
        }
    }

    public function checkUserHrm()
    {
        $data = Http::get('http://lestarijayaraya.co.id:8009/api/karyawan/api_tampil?api_key=Y4BoXmabMlK7BQx&employee_status=1&employe_status=1')->json();
        $data = collect($data)->sortBy('email');
        $user_login_code = array();
        // return $data;
        // try {
            foreach ($data as $key => $user_login) {
                if ($user_login['user_id']) {
                    array_push($user_login_code, $user_login['user_id']);

                    $userLjr = User::where('code', $user_login['user_id'])->first() ?? null;
                    $formBody['code'] = $user_login['user_id'];
                    $formBody['nip'] = $user_login['nip'];
                    $formBody['nik'] = $user_login['nik'];
                    $formBody['full_name'] = $user_login['full_name'];
                    $parts = explode(" ", $user_login['full_name']);
                    if(is_array($parts) && count($parts) > 1) {
                        $get_firstname = "";
                        $get_lastname = "";
                        for ($i=0; $i < count($parts); $i++) {
                            if ($i == 0) {
                                $get_firstname = $parts[$i];
                            }else{
                                $get_lastname .= $parts[$i].' ';
                            }
                        }
                        $firstname = $get_firstname;
                        $lastname = $get_lastname;
                    }else{
                        $firstname = $user_login['full_name'];
                        $lastname = " ";
                    }

                    $formBody['first_name'] = $firstname;
                    $formBody['last_name'] = $lastname;
                    $formBody['email'] = $user_login['email'];
                    $formBody['company_id'] = $user_login['company_id'];
                    $formBody['company_name'] = $user_login['company_name'];
                    $formBody['department_id'] = $user_login['department_id'];
                    $formBody['department_name'] = $user_login['department_name'];
                    $formBody['location_type_id'] = $user_login['location_type_id'];
                    $formBody['location_type_name'] = $user_login['location_type_name'];
                    $formBody['location_id'] = $user_login['location_id'];
                    $formBody['location_name'] = $user_login['location_name'];
                    $formBody['job_id'] = $user_login['job_id'];
                    $formBody['jabatan_name'] = $user_login['jabatan_name'];
                    $formBody['section_id'] = $user_login['section_id'];
                    $formBody['section_name'] = $user_login['section_name'];
                    $formBody['sub_section_id'] = $user_login['sub_section_id'];
                    $formBody['sub_section_name'] = $user_login['sub_section_name'];
                    $formBody['blood_type'] = $user_login['blood_type'];
                    $formBody['emergency_number'] = $user_login['emergency_number'];
                    $formBody['birth'] = $user_login['born_date'];
                    $formBody['type_driving_license'] = $user_login['type_driving_license'];
                    $formBody['driving_license_number'] = $user_login['driving_license_number'];
                    $formBody['validity_driving_license'] = $user_login['validity_driving_license'];
                    $formBody['avatar'] = $user_login['avatar'];
                    $formBody['age'] = $user_login['age'];
                    $formBody['employee_status'] = $user_login['employee_status'];
                    $formBody['ket_update'] = $user_login['ket_update'];

                    // USERNAME
                    $lower = strtolower(str_replace(' ', '', $user_login['full_name']));
                    $birth = $user_login['born_date'];
                    if ($birth == '0000-00-00') {
                        $birth = '000000';
                    }else{
                        $birth = Carbon::parse($birth)->format('dmy');
                    }
                    $username = substr($lower, 0, 7).$birth;

                    $userDuplicate = User::where('username', 'LIKE', '%'.$username.'%')->count();
                    if ($userDuplicate >= 1) {
                        $username = $username.($userDuplicate+1);
                    }

                    if (!$userLjr) {
                        $formBody['username'] = $username;
                        $formBody['role'] = $user_login['jabatan_name'];
                        $formBody['role_id'] = null;
                        $formBody['password'] = Hash::make('123456');
                        $user = User::create($formBody);
                    }else{
                        $userLjr->update($formBody);
                    }
                }
            }

            $delUser = User::whereNotIn('code', $user_login_code)->delete();

            return response(['status' => true, 'message' => 'data berhasil singkronkan data'], 200);
        // } catch (\Throwable $th) {
        //     return response(['status' => false, 'message' => $th->getMessage()], 200);
        // }
    }

    public function hrmAll()
    {
        $response = Http::get('http://lestarijayaraya.co.id:8009/api/karyawan/api_tampil?api_key=Y4BoXmabMlK7BQx&employee_status=0');
        if (!$response->successful()) {
            return false;
        }
        $response2 = Http::get('http://lestarijayaraya.co.id:8009/api/karyawan/api_tampil?api_key=Y4BoXmabMlK7BQx&employee_status=1');
        if (!$response2->successful()) {
            return false;
        }

        $results = array_merge($response->json(), $response2->json());

        if (is_array($results) && count($results)) {
            $user_exist = [];
            foreach ($results as $key => $result) {
                if (isset($result['user_id']) && !is_null($result['user_id'])) {
                    $cek_user = User::where('code',$result['user_id'])->first();
                    if ($cek_user) {
                        $user = User::find($cek_user->id);
                    }else{
                        $user = new User;
                        $user->code = $result['user_id'];

                        //make username
                        $lower = strtolower(str_replace(' ', '', $result['full_name']));
                        $birth = $result['born_date'];
                        if ($birth == '0000-00-00') {
                            $birth = '000000';
                        }else{
                            $birth = Carbon::parse($birth)->format('dmy');
                        }
                        $username = substr($lower, 0, 7).$birth;
                        $userDuplicate = User::where('username', 'LIKE', '%'.$username.'%')->count();
                        if ($userDuplicate >= 1) {
                            $username = $username.($userDuplicate+1);
                        }

                        $user->password = Hash::make('123456');
                        $user->username = $username;
                    }

                    $user->nip = $result['nip'];
                    $user->email = $result['email'];
                    $user->nik = $result['nik'];
                    $user->full_name = trim($result['full_name']);
                    $user->company_id = $result['company_id'];
                    $user->company_name = $result['company_name'];
                    $user->department_id = $result['department_id'];
                    $user->department_name = $result['department_name'];
                    $user->location_type_id = $result['location_type_id'];
                    $user->location_type_name = $result['location_type_name'];
                    $user->location_id = $result['location_id'];
                    $user->location_name = $result['location_name'];
                    $user->job_id = $result['job_id'];
                    $user->jabatan_name = $result['jabatan_name'];
                    $user->section_id = $result['section_id'];
                    $user->section_name = $result['section_name'];
                    $user->sub_section_id = $result['sub_section_id'];
                    $user->sub_section_name = $result['sub_section_name'];
                    $user->blood_type = $result['blood_type'];
                    $user->emergency_number = $result['emergency_number'];
                    $user->birth = $result['born_date'];
                    $user->type_driving_license = $result['type_driving_license'];
                    $user->driving_license_number = $result['driving_license_number'];
                    $user->validity_driving_license = $result['validity_driving_license'];
                    $user->avatar = $result['avatar'];
                    $user->age = $result['age'];
                    $user->employee_status = $result['employee_status'];
                    $user->ket_update = $result['ket_update'];

                    //pisah name
                    $pisah_name = explode(" ",trim($result['full_name']));
                    $firstname = "";
                    $lastname = "";
                    if (is_array($pisah_name) && count($pisah_name) > 0) {
                        for ($in=0; $in < count($pisah_name); $in++) {
                            if ($in == 0) {
                                $firstname = $pisah_name[$in];
                            }else{
                                $lastname = $pisah_name[$in].' ';
                            }
                        }
                    }else{
                        $firstname = $result['full_name'];
                        $lastname = "";
                    }
                    $user->first_name = $firstname;
                    $user->last_name = $lastname;
                    $user->role = $result['jabatan_name'];
                    $user->save();

                    $user_exist[$user->id] = $user->id;
                }
            }

            $delete_user = User::whereNotIn('id',$user_exist)->delete();
            $this->updateDriver();
        }else{
            return false;
        }
    }

    public function updateDriver(){
        $users = User::where('jabatan_name','ilike','%driver%')->get();

        $driver_exist = [];

        foreach ($users as $key => $user) {
            $cek_driver = Driver::where('user_id',$user->id)->first();
            if ($cek_driver) {
                $driver = Driver::find($cek_driver->id);
            }else{
                $driver = new Driver;
                $driver->user_id = $user->id;
                $driver->join_date = Carbon::now();
            }

            $driver->emergency_contact = $user->emergency_number;
            $driver->status	= $user->employee_status == 'AKTIF' ? 1 : 0;
            $driver->available	= $user->employee_status == 'AKTIF' ? 1 : 0;
            $driver->emergency_name = "";
            $driver->sim_validity_period = $user->validity_driving_license;
            $driver->sim_validity_period = $user->validity_driving_license == '0000-00-00' ? NULL : $user->validity_driving_license;
            $driver->driving_license = $user->driving_license_number;
            $driver->birth = $user->birth == '0000-00-00' ? NULL : $user->birth;

            $type_sim = TypeSim::where('name', $user->type_driving_license)->first();
            if ($type_sim) {
                $driver->type_sim_id = $type_sim->id;
            }else{
                $driver->type_sim_id = NULL;
            }

            $master_location = MasterLocation::where('code', $user->location_id)->first();
            if ($master_location) {
                $driver->master_location_id = $master_location->id;
            }else{
                $driver->master_location_id = NULL;
            }

            $driver->save();
            $driver_exist[$driver->id] = $driver->id;
        }

        $driver_exist = Driver::whereNotIn('id',$driver_exist)->delete();

        return true;
    }

    public function setRole(){
        $user = User::find(469);
        $user->assignRole('Administrator');
        // $user->assignRole('Admin');
    }
}
