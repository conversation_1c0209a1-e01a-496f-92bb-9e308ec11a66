<?php

namespace App\Http\Controllers\Fleet;

use App\Exports\ExportCheklistKendaraan;
use App\Exports\ExportK3;
use App\FleetMaster\Vehicle;
use App\Http\Controllers\Controller;
use App\Http\Helper;
use App\Models\Fleet\AnswerQuestion;
use App\Models\Fleet\AnswerQuestionDetail;
use App\Models\Fleet\AnswerQuestionUser;
use App\Models\Fleet\Asset;
use App\Models\Fleet\CategoryQuestion;
use App\Models\Fleet\Company;
use App\Models\Fleet\HistoryWorkshop;
use App\Models\Fleet\K3;
use App\Models\Fleet\Location;
use App\Models\Fleet\LocationChecklist;
use App\Models\Fleet\MasterLocation;
use App\Models\Fleet\RiwayatAnswerQuestionUser;
use App\Models\Fleet\StatusPerbaikan;
use App\Models\Fleet\StatusVehicle;
use App\Models\User;
use App\Workshop\Workshop;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;
use Maatwebsite\Excel\Facades\Excel;

class K3Controller extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        try {
            if ($request->has('start_date')) {
                $start_date = Carbon::parse($request->get('start_date'))->format('Y-m-d').' 00:00:00';
            }else{
                $start_date = Carbon::today()->format('Y-m-d').' 00:00:00';
            }
            if ($request->has('to_date')) {
                $to_date = Carbon::parse($request->get('to_date'))->format('Y-m-d').' 23:59:00';
            }else{
                $to_date = Carbon::today()->format('Y-m-d').' 23:59:00';
            }
            $data['start_date'] = Carbon::parse($start_date)->format('Y-m-d');
            $data['to_date'] = Carbon::parse($to_date)->format('Y-m-d');

            $user_login = Auth::user();
            if ($user_login->role == 'Administrator') {
                $uid_location_same = User::pluck('id');
            }else{
                $uid_location_same = User::whereIn('location_id', json_decode(Auth::user()->allow_location_id))->pluck('id');
            }

            if ($request->type) {
                $k3 = AnswerQuestionUser::whereBetween('created_at', [$start_date,$to_date])
                ->whereIn('user_id', $uid_location_same)
                ->where(function($query) use($request){
                    if ($request->get('status_vehicle_id')) {
                        if ($request->get('status_vehicle_id') == 2) {
                            $query->whereIn('status_vehicle_id',[2,1]);
                        }else{
                            $query->where('status_vehicle_id',$request->get('status_vehicle_id'));
                        }
                    }

                    if ($request->get('location')) {
                        $query->where('location_checklist_id', $request->get('location'));
                    }

                    if ($request->get('company')) {
                        $company = Company::find($request->get('company'))->code;
                        $user_id_company = User::where('company_id', $company)->pluck('id');
                        $query->whereIn('user_id', $user_id_company);
                    }

                    if ($request->get('master_location')) {
                        $master_location = MasterLocation::find($request->get('master_location'))->code;
                        $user_id_company = User::where('location_id', $master_location)->pluck('id');
                        $query->whereIn('user_id', $user_id_company);
                    }
                })
                ->where('status',1)
                ->whereNotNull('status_vehicle_id')
                ->where('slug', $request->type)
                ->orderBy('id', 'asc')
                ->get();
            }else{
                $k3 = AnswerQuestionUser::whereBetween('created_at', [$start_date,$to_date])
                ->whereIn('user_id', $uid_location_same)
                ->where(function($query) use($request){
                    if ($request->get('status_vehicle_id')) {
                        if ($request->get('status_vehicle_id') == 2) {
                            $query->whereIn('status_vehicle_id',[2,1]);
                        }else{
                            $query->where('status_vehicle_id',$request->get('status_vehicle_id'));
                        }
                    }

                    if ($request->get('location')) {
                        $query->where('location_checklist_id', $request->get('location'));
                    }

                    if ($request->get('company')) {
                        $company = Company::find($request->get('company'))->code;
                        $user_id_company = User::where('company_id', $company)->pluck('id');
                        $query->whereIn('user_id', $user_id_company);
                    }

                    if ($request->get('master_location')) {
                        $master_location = MasterLocation::find($request->get('master_location'))->code;
                        $user_id_company = User::where('location_id', $master_location)->pluck('id');
                        $query->whereIn('user_id', $user_id_company);
                    }
                })
                ->where('status',1)
                ->whereNotNull('status_vehicle_id')
                ->whereIn('slug', ['checklist-health','quiz'])
                ->orderBy('id', 'asc')
                ->get();
            }

            $simpan_id = [];
            $id_tampil = [];
            $repeat = [];
            foreach ($k3 as $key => $datas) {
                $tanggal_create = Carbon::parse($datas->created_at)->format('Y-m-d');
                if (isset($simpan_id[$datas->user_id][$datas->slug][$tanggal_create])) {
                    $repeat[$datas->id] = $datas->id;
                    $kedua = true;
                }else{
                    $kedua = false;
                    $simpan_id[$datas->user_id][$datas->slug][$tanggal_create] = $datas->id;
                }

                if ($request->has('check_number')) {
                    if ($request->get('check_number') == 1) {
                        if (!$kedua) {
                            $id_tampil[$datas->id] = $datas->id;
                        }
                    }elseif($request->get('check_number') == 2){
                        if ($kedua) {
                            $id_tampil[$datas->id] = $datas->id;
                        }
                    }else{
                        $id_tampil[$datas->id] = $datas->id;
                    }
                }else{
                    $id_tampil[$datas->id] = $datas->id;
                }
            }

            $data['k3'] = AnswerQuestionUser::with(['user', 'statusVehicle', 'answerQuestionDetail','riwayatTindakan.approveBy'])
            ->whereIn('id',$id_tampil)
            ->orderBy('id', 'desc')
            ->get();

            $cekPertama = [];
            $cekDua = [];
            foreach ($data['k3'] as $key => $datas) {
                if (isset($repeat[$datas->id])) {
                    $data['k3'][$key]['kedua'] = true;
                    if ($datas->slug == 'checklist-health') {
                        $cekDua[$key] = $datas->id;
                    }
                }else{
                    $data['k3'][$key]['kedua'] = false;
                    if ($datas->slug == 'checklist-health') {
                        $cekPertama[$key] = $datas->id;
                    }
                }
            }

            // JAWABAN
            // $data['jumlah_data'] = AnswerQuestionUser::whereIn('id',$id_tampil)
            // ->count();

            // DRIVER
            $data['jumlah_data'] = AnswerQuestionUser::where('slug', 'checklist-health')->whereIn('id',$id_tampil)->get()->groupBy('user_id')->count();

            $data['jumlah_data_merah_1'] = AnswerQuestionUser::whereIn('id', $cekPertama)
            ->where('status_vehicle_id', 4)
            ->count();

            $data['jumlah_data_hijau_1'] = AnswerQuestionUser::whereIn('id', $cekPertama)
            ->whereIn('status_vehicle_id', [2,1])
            ->count();

            $data['jumlah_data_merah_2'] = AnswerQuestionUser::whereIn('id', $cekDua)
            ->where('status_vehicle_id', 4)
            ->count();

            $data['jumlah_data_hijau_2'] = AnswerQuestionUser::whereIn('id', $cekDua)
            ->whereIn('status_vehicle_id', [2,1])
            ->count();

            $data['status_vehicle'] = StatusVehicle::get();
            $data['company'] = Company::get();
            $data['location'] = LocationChecklist::where('status', 1)->get();
            $data['master_location'] = MasterLocation::whereIn('code', json_decode(Auth::user()->allow_location_id))->orderBy('name', 'asc')->get();

            return response()->json($data);

        } catch (\Throwable $th) {
            return response()->json(['success' => false, 'message' => $th->getMessage()]);
        }
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        try{
            $data = AnswerQuestionUser::with(['user'])->findOrFail($id);
            if ($data['status_vehicle_id'] == 2 || $data['status_vehicle_id'] == 1) {
                $data['status'] = "Normal";
            }else if ($data['status_vehicle_id'] == 4){
                $data['status'] = 'Abnormal';
            }
            $data['user']['photo'] = "http://".\Request::getHost().":8000/".$data->user->photo;

            $dataQuestion =  [];
            foreach ($data->answerQuestionDetail as $key => $value) {
                $dataQuestion[$key]['id'] = $value->id;
                $dataQuestion[$key]['question'] = $value->question;
                $dataQuestion[$key]['answer_id'] = $value->answer_question_id;
                $answer = AnswerQuestion::findOrFail($value->answer_question_id);
                if ($answer->danger == 1) {
                    $dataQuestion[$key]['status_color'] = 'red';
                }else {
                    $statusVehicle = StatusVehicle::where('start', '<=', $answer->point)->where('to', '>=', $answer->point)->first();
                    if (!$statusVehicle) {
                        $statusVehicle = StatusVehicle::orderBy('to', 'desc')->first();
                    }

                    $dataQuestion[$key]['status_color'] = $statusVehicle->name;
                }
                $dataQuestion[$key]['point'] = $answer->point;

                $dataQuestion[$key]['answer_question'] = AnswerQuestion::where('question_id', $value->question_id)->get();
            }

            $data['answered_category_list'] = array(
                'category' => 'Cheklist Kesehatan',
                'status' => $data['status'],
                'answered_question_list' => $dataQuestion,
            );

            $data['riwayat'] = RiwayatAnswerQuestionUser::with('approveBy')->where('answer_question_user_id', $data->id)->first();
            if ($data['riwayat']) {
                $data['riwayat']['note'] = $data->note;
                $data['riwayat']['note_riwayat'] = $data['riwayat']->note;
                $data['riwayat']['approveBy'] = $data['riwayat']->approveBy;
                $data['riwayat']['approveBy']['fullname'] = $data['riwayat']->approveBy->first_name." ".$data['riwayat']->approveBy->last_name;
            }

            if (isset($data['riwayat']['status_k3'])) {
                $data['riwayat']['status'] = Helper::status_k3($data['riwayat']['status_k3']);
            }else if (isset($data['riwayat']['status_koordinator'])) {
                $data['riwayat']['status'] = Helper::status_koordinator($data['riwayat']['status_koordinator']);
            }

            return response()->json(['success' => true, 'data' => $data]);
        } catch (\Throwable $th) {
            return response()->json(['success' => false, 'message' => $th->getMessage()]);
        }
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        try {
            $data['detail'] = AnswerQuestionUser::with('user', 'statusVehicle')->findOrFail($id);
            $data['status'] = StatusVehicle::orderBy('id', 'desc')->get();

            return response()->json($data);
        } catch (\Throwable $th) {
            return response()->json(['success' => false, 'message' => $th->getMessage()]);
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        try{
            $data = $request->all();
            $datas['status_vehicle_id'] = $data['status_vehicle_id'];
            $datas['note'] = $data['note'];
            $answerQuestionUser = AnswerQuestionUser::findOrFail($id)->update($datas);

            $datas['user_id'] = Auth::user()->id;
            $datas['answer_question_user_id'] = $id;
            $k3 = K3::create($datas);

            return response()->json(['success' => true, 'message' => 'Berhasil edit data']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }

    public function pulangkan($id)
    {
        try {
            $data['bring_it_home'] = 1;
            $aqu = AnswerQuestionUser::findOrFail($id)->update($data);

            $datas['answer_question_user_id'] = $id;
            $datas['status_k3'] = 2;
            $datas['user_id'] = Auth::user()->id;
            $datas['note'] = 'Pulangkan';
            $raqu = RiwayatAnswerQuestionUser::create($datas);

            return response()->json(['success' => true, 'message' => 'Update data berhasil']);
        } catch (\Throwable $th) {
            return response()->json(['success' => false, 'message' => $th->getMessage()], 500);
        }
    }

    public function listDashboard(Request $request)
    {
        if ($request->has('start_date')) {
            $start_date = Carbon::parse($request->get('start_date'))->format('Y-m-d').' 00:00:00';
        }else{
            $start_date = Carbon::today()->format('Y-m-d').' 00:00:00';
        }
        if ($request->has('end_date')) {
            $end_date = Carbon::parse($request->get('end_date'))->format('Y-m-d').' 23:59:00';
        }else{
            $end_date = Carbon::today()->format('Y-m-d').' 23:59:00';
        }

        if ($request->status) {
            $status = $request->status;
        }else{
            $status = 'all';
        }

        $user_login = Auth::user();
        if ($user_login->role == 'Administrator' || $user_login->role == 'K3 (Corporate)') {
            $uid_location_same = User::pluck('id');
        }else{
            $uid_location_same = User::where('location_id', Auth::user()->location_id)->pluck('id');
        }

        $data = AnswerQuestionUser::whereBetween('created_at', [$start_date,$end_date])
        ->whereIn('user_id', $uid_location_same)
        ->with(['statusVehicle','user'])
        ->where(function($query) use($status){
            if ($status) {
                if ($status == 'normal') {
                    $query->whereIn('status_vehicle_id',[2,1]);
                }elseif ($status == 'upnormal') {
                    $query->where('status_vehicle_id',4);
                }else{
                    $query->whereIn('status_vehicle_id',[2,1,4]);
                }
            }
        })
        ->where('slug', 'checklist-health')
        // ->with(['user', 'statusVehicle', 'answerQuestionDetail'])
        ->orderBy('id', 'desc')
        ->select('id','user_id','slug','input_date','total_point','status_vehicle_id','location_checklist_id','note','recheck_answer_question_user_id','bring_it_home')
        ->get();

        foreach ($data as $ckey => $cvalue) {
            // PEMERIKSAAN 2
            $data[$ckey]['rechek2'] = AnswerQuestionUser::where('recheck_answer_question_user_id', $cvalue->id)->where('slug', 'checklist-health')->with('user', 'statusVehicle', 'answerQuestionDetail')->orderBy('id', 'asc')->first();

            // DECIDED KOORDINATOR
            $raqu = RiwayatAnswerQuestionUser::where('answer_question_user_id', $cvalue->id)->first() ?? null;

            if ($raqu) {
                $data[$ckey]['decided'] = true;

                $data[$ckey]['approveBy'] = $raqu->approveBy;
                $data[$ckey]['approveBy']['fullname'] = $raqu->approveBy->first_name." ".$raqu->approveBy->last_name;

                if (isset($raqu['status_k3'])) {
                    $data[$ckey]['decision_status'] = Helper::status_k3($raqu['status_k3']);
                }else if (isset($raqu['status_koordinator'])) {
                    $data[$ckey]['decision_status'] = Helper::status_koordinator($raqu['status_koordinator']);
                }else{
                    $data[$ckey]['decision_status'] = null;
                }
            }else {
                $data[$ckey]['decided'] = false;
            }
        }

        return response()->json(['success' => true, 'data' => $data]);
    }

    public function detailDashboard($id){
        $data = AnswerQuestionUser::with(['statusVehicle','user'])
        ->select('id','user_id','slug','input_date','total_point','status_vehicle_id','location_checklist_id','note','recheck_answer_question_user_id','bring_it_home')
        ->where('id',$id)
        ->first();

        $answer = AnswerQuestionDetail::select('id','answer_question_user_id','question','answer','point','image')
        ->where('answer_question_user_id',$id)
        ->get();

        return response()->json(['success' => true, 'data' => $data, 'answer' => $answer]);
    }

    public function waiting($id)
    {
        try {
            $datas = AnswerQuestionUser::findOrFail($id);
            if ($datas->waiting) {
                return response()->json(['success' => false, 'message' => 'Tidak bisa melakukan cheklist kembali!!']);
            }else {
                $data['waiting'] = now();
                $aqu = AnswerQuestionUser::findOrFail($id)->update($data);

                $riwayat['answer_question_user_id'] = $id;
                $riwayat['status_k3'] = 1;
                $riwayat['user_id'] = Auth::user()->id;
                $riwayat['note'] = 'Tunggu 30 menit';
                $raqu = RiwayatAnswerQuestionUser::create($riwayat);

                return response()->json(['success' => true, 'message' => 'Update data berhasil']);
            }
        } catch (\Throwable $th) {
            return response()->json(['success' => false, 'message' => $th->getMessage()], 500);
        }
    }

    public function tindakan(Request $request)
    {
        try {
            $id = $request->input('id_tindakan');
            if ($request->input('status') == true) {
                $data['status_vehicle_id'] = 2;
                $data['vehicle_next_danger'] = 1;

                $dataRiwayat['answer_question_user_id'] = $id;
                $dataRiwayat['status_k3'] = 3;
                $dataRiwayat['user_id'] = Auth::user()->id;
                $dataRiwayat['note'] = 'Boleh jalan dengan keterangan';
                $raqu = RiwayatAnswerQuestionUser::create($dataRiwayat);
            }else{
                $riwayat['answer_question_user_id'] = $id;
                $riwayat['status_k3'] = 4;
                $riwayat['user_id'] = Auth::user()->id;
                $riwayat['note'] = $request->input('note');
                $raqu = RiwayatAnswerQuestionUser::create($riwayat);
            }
            $data['note'] = $request->input('note');
            // return $data;
            $aqu = AnswerQuestionUser::findOrFail($id)->update($data);

            return response()->json(['success' => true, 'message' => 'Update data berhasil']);
        } catch (\Throwable $th) {
            return response()->json(['success' => false, 'message' => $th->getMessage()], 500);
        }
    }

    public function actionK3(Request $request, $id)
    {
        $action = $request->action;
        if ($action == 'note') {
            unset($action);
            $data = $request->all();
            $datas['note'] = $data['note'];
            if ($request->input('status') == true) {
                $datas['status_vehicle_id'] = 2;
                $datas['vehicle_next_danger'] = 1;

                $dataRiwayat['answer_question_user_id'] = $id;
                $dataRiwayat['status_k3'] = 3;
                $dataRiwayat['user_id'] = Auth::user()->id;
                $dataRiwayat['note'] = 'Boleh jalan dengan keterangan';
                $raqu = RiwayatAnswerQuestionUser::create($dataRiwayat);
            }else{
                $dataRiwayat['answer_question_user_id'] = $id;
                $dataRiwayat['status_k3'] = 4;
                $dataRiwayat['user_id'] = Auth::user()->id;
                $dataRiwayat['note'] = $request->input('note');
                $raqu = RiwayatAnswerQuestionUser::create($dataRiwayat);
            }
            $answerQuestionUser = AnswerQuestionUser::findOrFail($id)->update($datas);

            $datas['user_id'] = Auth::user()->id;
            $datas['answer_question_user_id'] = $id;

            $k3 = K3::create($datas);

            return response()->json(['success' => true, 'message' => 'Berhasil edit data']);
        }elseif ($action == 'pulangkan') {
            $this->pulangkan($id);
            return response()->json(['success' => true, 'message' => 'Berhasil edit data']);
        }elseif ($action == 'waiting') {
            $this->waiting($id);
            return response()->json(['success' => true, 'message' => 'Berhasil edit data']);
        }
    }

    public function koordinator(Request $request)
    {
        if ($request->has('start_date')) {
            $start_date = Carbon::parse($request->get('start_date'))->format('Y-m-d').' 00:00:00';
        }else{
            $start_date = Carbon::today()->format('Y-m-d').' 00:00:00';
        }
        if ($request->has('to_date')) {
            $to_date = Carbon::parse($request->get('to_date'))->format('Y-m-d').' 23:59:00';
        }else{
            $to_date = Carbon::today()->format('Y-m-d').' 23:59:00';
        }

        if ($request->status) {
            $status = $request->status;
        }else{
            $status = 'all';
        }

        $user_login = Auth::user();
        if ($user_login->role == 'Administrator' || $user_login->role == 'K3 (Corporate)') {
            $uid_location_same = User::pluck('id');
        }else{
            $uid_location_same = User::where('location_id', Auth::user()->location_id)->pluck('id');
        }

        $data = AnswerQuestionUser::whereBetween('created_at', [$start_date, $to_date])
        ->whereIn('user_id', $uid_location_same)
        ->where('slug', 'checklist-vehicle')
        ->whereNotNull('status_vehicle_id')
        ->orderBy('id', 'desc')
        ->select('id', 'type', 'user_id','slug','input_date','total_point','status_vehicle_id','location_checklist_id','note','recheck_answer_question_user_id','bring_it_home', 'vehicle_id', 'asset_id')
        ->get();

        if ($request->status_vehicle_id) {
            $data = AnswerQuestionUser::whereBetween('created_at', [$start_date, $to_date])
            ->where('slug', 'checklist-vehicle')
            ->whereNotNull('status_vehicle_id')
            ->where('status_vehicle_id', $request->status_vehicle_id)
            ->orderBy('id', 'desc')
            ->select('id', 'type', 'user_id','slug','input_date','total_point','status_vehicle_id','location_checklist_id','note','recheck_answer_question_user_id','bring_it_home', 'vehicle_id', 'asset_id')
            ->get();
        }

        foreach ($data as $ckey => $cvalue) {
            $data[$ckey]['fullname'] = $cvalue->user->full_name ?? null;
            $data[$ckey]['photo'] = $cvalue->user->avatar ?? null;
            $data[$ckey]['status_kendaraan'] = null;
            if($cvalue->type == 1){
                $data[$ckey]['status_kendaraan'] = 'Berangkat';
            }else if ($cvalue->type == 2) {
                $data[$ckey]['status_kendaraan'] = 'Pulang';
            }

            $data[$ckey]['license_no'] = ($cvalue->asset->assetDetail ?? null) ? collect($cvalue->asset->assetDetail)->where('attribute_code', 'nomor_polisi')->first()->value : null;

            // $dataQuestion =  [];
            // foreach ($cvalue->answerQuestionDetail as $key => $value) {
            //     $dataQuestion[$key]['id'] = $value->id;
            //     $dataQuestion[$key]['question'] = $value->question;
            //     $dataQuestion[$key]['answer_id'] = $value->answer_question_id;
            //     $answer = AnswerQuestion::findOrFail($value->answer_question_id);
            //     if ($answer->danger == 1) {
            //         $dataQuestion[$key]['status_color'] = 'red';
            //     }else {
            //         $statusVehicle = StatusVehicle::where('start', '<=', $answer->point)->where('to', '>=', $answer->point)->first();
            //         if (!$statusVehicle) {
            //             $statusVehicle = StatusVehicle::orderBy('to', 'desc')->first();
            //         }

            //         $dataQuestion[$key]['status_color'] = $statusVehicle->name;
            //     }
            //     $dataQuestion[$key]['point'] = $answer->point;
            //     if ($value->image) {
            //         $dataQuestion[$key]['image'] = "http://".\Request::getHost().":8000/".$value->image;
            //     }else {
            //         $dataQuestion[$key]['image'] = null;
            //     }

            //     $dataQuestion[$key]['answer_question'] = AnswerQuestion::where('question_id', $value->question_id)->get();
            // }

            // $data[$ckey]['answered_category_list'] = array(
            //     'category' => 'Koordinator',
            //     'answered_question_list' => $dataQuestion,
            // );
            // DECIDED KOORDINATOR
            $raqu = RiwayatAnswerQuestionUser::where('answer_question_user_id', $cvalue->id)->first() ?? null;
            if ($raqu) {
                $data[$ckey]['decided'] = true;
            }else {
                $data[$ckey]['decided'] = false;
            }
            // PEMERIKSAAN 2
            $data[$ckey]['rechek2'] = AnswerQuestionUser::where('recheck_answer_question_user_id', $cvalue->id)->where('slug', 'checklist-vehicle')->orderBy('id', 'asc')->first();
        }

        return response()->json(['success' => true, 'data' => $data]);
    }

    public function detailKordinator($id)
    {
        $data = AnswerQuestionUser::with(['statusVehicle','user'])->findOrFail($id);
        // return $data;
        // DETAIL INFO
        $cq = CategoryQuestion::where('slug', 'checklist-vehicle')->orderBy('id', 'asc')->select(['id', 'name'])->get();
        foreach ($cq as $key => $value) {
            $total_point = 0;
            $danger = false;
            $cq[$key]['question'] = $value->question()->with('answer')->select(['id', 'question'])->get();
            foreach ($cq[$key]['question'] as $bkey => $value) {
                    $aqd = AnswerQuestionDetail::where('answer_question_user_id', $data->id)->where('question_id', $value->id)->first();
                if ($aqd) {
                    $cq[$key]['question'][$bkey]['answer_id'] = $aqd->answer_question_id;
                    $total_point += $aqd->point ?? 0;
                    if ($aqd->image) {
                        $cq[$key]['question'][$bkey]['image'] = "http://".\Request::getHost().":8000/".$aqd->image;
                    }else {
                        $cq[$key]['question'][$bkey]['image'] = null;
                    }
                    if ($aqd->answerQuestion->danger == 1) {
                        $danger = true;
                    }
                }else {
                    $cq[$key]['question'][$bkey]['answer_id'] = null;
                    $cq[$key]['question'][$bkey]['image'] = null;
                }
            }

            $cq[$key]['total_point'] = $total_point;
            if ($danger == true) {
                $cq[$key]['color'] = 'red';
            }else{
                $cq[$key]['color'] = 'green';
            }
        }

        $data['answered_question_list'] = $cq;

        $data['riwayat'] = RiwayatAnswerQuestionUser::where('answer_question_user_id', $data->id)->first();
        if ($data['riwayat']) {
            $data['riwayat']['approveBy'] = $data['riwayat']->approveBy;
            $data['riwayat']['approveBy']['fullname'] = $data['riwayat']->approveBy->first_name." ".$data['riwayat']->approveBy->last_name;
        }

        if (isset($data['riwayat']['status_k3'])) {
            $data['riwayat']['status'] = Helper::status_k3($data['riwayat']['status_k3']);
        }else if (isset($data['riwayat']['status_koordinator'])) {
            $data['riwayat']['status'] = Helper::status_koordinator($data['riwayat']['status_koordinator']);
        }

        if($data->type == 1){
            $data['status'] = 'Berangkat';
        }
        if ($data->type == 2) {
            $data['status'] = 'Pulang';
        }

        if ($data->slug == 'checklist-vehicle' && $data->asset_id != null) {
            return response()->json(['success' => true, 'data' => $data]);
        }else if($data->slug == 'checklist-vehicle' && $data->asset_id != null){
            return response()->json(['success' => true, 'message' => 'Kendaraan Tidak Ditemukan!']);
        }else if($data->slug == 'checklist-health'){
            return response()->json(['success' => true, 'data' => $data]);
        }
    }

    public function indexRiwayatAnswerQuestionUser(Request $request)
    {
        if ($request->slug) {
            $aqu = AnswerQuestionUser::where('slug', $request->slug)->pluck('id');
            $data = RiwayatAnswerQuestionUser::whereIn('answer_question_user_id', $aqu)->with(['approveBy', 'answerQuestionUser'])->get();

            foreach ($data as $key => $value) {
                if (isset($value->status_k3)) {
                    $data[$key]['status'] = Helper::status_k3($value->status_k3);
                }elseif (isset($value->status_koordinator)) {
                    $data[$key]['status'] = Helper::status_koordinator($value->status_koordinator);
                }
            }
        }else if ($request->answer_question_user_id) {
            $aqu = AnswerQuestionUser::findOrFail($request->answer_question_user_id);
            $data = RiwayatAnswerQuestionUser::with(['approveBy', 'answerQuestionUser'])->where('answer_question_user_id', $aqu->id)->first();

            if (isset($data['status_k3'])) {
                $data['status'] = Helper::status_k3($data['status_k3']);
            }else if (isset($data['status_koordinator'])) {
                $data['status'] = Helper::status_koordinator($data['status_koordinator']);
            }
        }

        return response()->json(['success' => true, 'data' => $data]);
    }

    public function riwayatKoordinator(Request $request)
    {
        $data = $request->all();
        $datas['user_id'] = Auth::user()->id;
        $datas['answer_question_user_id'] = $data['answer_question_user_id'];
        $datas['status_koordinator'] = $data['status_koordinator'];
        $datas['note'] = $data['note'];
        $raqu = RiwayatAnswerQuestionUser::create($datas);

        if ($data['status_koordinator'] == 0) {
            $aqu = AnswerQuestionUser::findOrFail($data['answer_question_user_id']);
            $aqu->vehicle_repair = true;
            $aqu->save();

            $dataWorkshop = new Workshop();
            // $dataWorkshop->no_seri = 'LJR-'.now()->format('YmdHis').rand(10, 99);
            $dataAsset = Asset::find($data['asset_id']);
            $company_id = $dataAsset->company_id;
            $department_id = $dataAsset->department_id;
            $location_id = 00;
            $dataWorkshop->no_seri = Helper::genCode($company_id, $department_id, $location_id, 'SV') ?? 'SV';
            $dataWorkshop->asset_id = $data['asset_id'];
            $dataWorkshop->type = 'Service';
            // $dataWorkshop->type = 'Perbaikan';
            $dataWorkshop->driver_id = $data['driver_id'];
            $dataWorkshop->user_id = Auth::user()->id;
            $dataWorkshop->status_perbaikan_id = 1;
            $dataWorkshop->answer_question_user_id = $data['answer_question_user_id'];
            $dataWorkshop->km_service = Asset::findOrFail($aqu->asset_id)->km_actual;
            $dataWorkshop->save();

            try {
                $create_history['status_perbaikan_id'] = StatusPerbaikan::where('name', 'pending')->first()->id;
                $create_history['note'] = $request->note ?? '';
                $create_history['workshop_id'] = $dataWorkshop->id;
                $history_workshop = HistoryWorkshop::create($create_history);
            } catch (\Throwable $th) {
                $th;
            }

            $asset = Asset::findOrFail($request->asset_id);
            // $asset->maintenance = 1; hide fitur maintenance
            $asset->save();

            $status = Helper::status_koordinator($data['status_koordinator']);
            $nopol = collect($asset->assetDetail)->where('attribute_code', 'nomor_polisi')->first()->value ?? null;
            $approved_by = User::findOrFail($raqu->user_id);
            $approved_at = $raqu->created_at;

            return response()->json(['success' => true, 'message' => 'Berhasil edit data', 'workshop_id' => $dataWorkshop->id, 'approved_by' => $approved_by, 'approved_at' => $approved_at, 'action' => $status, 'nopol' => $nopol]);
        }else {
            $aqu = AnswerQuestionUser::findOrFail($data['answer_question_user_id']);
            $aqu->vehicle_next_danger = true;
            $aqu->save();

            $approved_by = User::findOrFail($raqu->user_id);
            return response()->json(['success' => true, 'message' => 'Berhasil edit data', 'approved_by' => $approved_by]);
        }
    }

    public function getHasil($id){
        $answer = AnswerQuestionDetail::with(['answerQuestion','questions.categoryQuestion'])
        ->where('answer_question_user_id',$id)
        ->get();

        return response()->json(['answer' => $answer]);
    }

    public function reportK3(Request $request)
    {
        try {
            if ($request->start_date) {
                $start_date = Carbon::parse($request->start_date)->format('Y-m-d').' 00:00:00';
            }else{
                $start_date = Carbon::today()->format('Y-m-d').' 00:00:00';
            }
            if ($request->to_date) {
                $to_date = Carbon::parse($request->to_date)->format('Y-m-d').' 23:59:00';
            }else{
                $to_date = Carbon::today()->format('Y-m-d').' 23:59:00';
            }

            $data = AnswerQuestionUser::where('status',1)
                ->where(function($query) use($request){
                    if ($request->get('status_vehicle_id')) {
                        if ($request->get('status_vehicle_id') == 2) {
                            $query->whereIn('status_vehicle_id',[2,1]);
                        }else{
                            $query->where('status_vehicle_id',$request->get('status_vehicle_id'));
                        }
                    }

                    if ($request->get('location')) {
                        $query->where('location_checklist_id', $request->get('location'));
                    }

                    if ($request->get('company')) {
                        $company = Company::findOrFail($request->get('company'))->code;
                        $user_id_company = User::where('company_id', $company)->pluck('id');
                        $query->whereIn('user_id', $user_id_company);
                    }
                })
                ->whereBetween('created_at', [$start_date,$to_date])
                ->whereNotNull('status_vehicle_id')
                ->whereNotNull('total_point')
                ->whereNotNull('status')
                ->whereNull('recheck_answer_question_user_id')
                ->whereIn('slug', ['checklist-health', 'quiz'])
                ->orderBy('id', 'desc')
                ->get();

            if ($request->search) {
                $search = $request->search;
            }else{
                $search = ['All'];
            }

            return Excel::download(new ExportK3($data, $search), 'laporan k3 '.$start_date.' - '.$to_date.'.xlsx');
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    public function reportCheklistKendaraan(Request $request)
    {
        try {
            if ($request->search) {
                $search = $request->search;
            }else{
                $search = ["All"];
            }

            if ($request->category) {
                $category = $request->category;
            }else{
                $category = ["All"];
            }

            if ($request->start_date) {
                $start_date = Carbon::parse($request->start_date)->format('Y-m-d').' 00:00:00';
            }else{
                $start_date = Carbon::today()->format('Y-m-d').' 00:00:00';
            }
            if ($request->to_date) {
                $to_date = Carbon::parse($request->to_date)->format('Y-m-d').' 23:59:00';
            }else{
                $to_date = Carbon::today()->format('Y-m-d').' 23:59:00';
            }

            // $user_login = Auth::user();
            // if ($user_login->role == 'Administrator') {
            //     $uid_location_same = User::pluck('id');
            // }else{
            //     $uid_location_same = User::whereIn('location_id', json_decode(Auth::user()->allow_location_id))->pluck('id');
            // }

            $data = AnswerQuestionUser::where('status',1)
                ->where(function($query) use($request){
                    if ($request->get('status_vehicle_id')) {
                        if ($request->get('status_vehicle_id') == 2) {
                            $query->whereIn('status_vehicle_id',[2,1]);
                        }else{
                            $query->where('status_vehicle_id',$request->get('status_vehicle_id'));
                        }
                    }

                    if ($request->get('company')) {
                        $company = Company::findOrFail($request->get('company'))->code;
                        $user_id_company = User::where('company_id', $company)->pluck('id');
                        $query->whereIn('user_id', $user_id_company);
                    }

                    if ($request->get('type')) {
                        $query->where('type', $request->get('type'));
                    }
                })
                ->whereBetween('created_at', [$start_date,$to_date])
                ->whereNotNull('status_vehicle_id')
                ->whereNotNull('total_point')
                ->whereNotNull('status')
                ->whereNotNull('asset_id')
                ->whereNull('recheck_answer_question_user_id')
                ->where('slug', 'checklist-vehicle')
                ->orderBy('id', 'desc')
                ->get();

            // dd($data[2]->kmActual->km_actual);
            foreach ($data as $key => $value) {
                $data[$key]['license_no'] = ($value->asset->assetDetail ?? null) ? collect($value->asset->assetDetail)->where('attribute_code', 'nomor_polisi')->first()->value ?? null : '';
            }

            return Excel::download(new ExportCheklistKendaraan($data, $search, $category), 'laporan cheklist kendaraan '.$start_date.' - '.$to_date.'.xlsx');
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    public function indexCheklistKendaraan(Request $request)
    {
        if ($request->start_date) {
            $start_date = Carbon::parse($request->start_date)->format('Y-m-d').' 00:00:00';
        }else{
            $start_date = Carbon::today()->format('Y-m-d').' 00:00:00';
        }
        if ($request->to_date) {
            $to_date = Carbon::parse($request->to_date)->format('Y-m-d').' 23:59:00';
        }else{
            $to_date = Carbon::today()->format('Y-m-d').' 23:59:00';
        }

        $data = AnswerQuestionUser::where('status',1)
            ->where(function($query) use($request){
                if ($request->get('status_vehicle_id')) {
                    if ($request->get('status_vehicle_id') == 2) {
                        $query->whereIn('status_vehicle_id',[2,1]);
                    }else{
                        $query->where('status_vehicle_id',$request->get('status_vehicle_id'));
                    }
                }

                if ($request->get('company')) {
                    $company = Company::findOrFail($request->get('company'))->code;
                    $user_id_company = User::where('company_id', $company)->pluck('id');
                    $query->whereIn('user_id', $user_id_company);
                }
            })
            ->whereBetween('created_at', [$start_date,$to_date])
            ->whereNotNull('status_vehicle_id')
            ->whereNotNull('total_point')
            ->whereNotNull('status')
            ->whereNotNull('asset_id')
            ->whereNull('recheck_answer_question_user_id')
            ->where('slug', 'checklist-vehicle')
            ->orderBy('id', 'desc')
            ->get();

        foreach ($data as $key => $value) {
            $data[$key]['license_no'] = collect($value->asset->assetDetail)->where('attribute_code', 'nomor_polisi')->first()->value ?? null;
        }

        if ($request->search) {
            $search = $request->search;
        }else{
            $search = ["All"];
        }

        if ($request->category) {
            $category = $request->category;
        }else{
            $category = ["All"];
        }

        return view('export.cheklist_kendaraan', ['data' => $data, 'search' => $search, 'category' => $category]);
    }


    public function indexCheklistKesehatan(Request $request)
    {
        if ($request->start_date) {
            $start_date = Carbon::parse($request->start_date)->format('Y-m-d').' 00:00:00';
        }else{
            $start_date = Carbon::today()->format('Y-m-d').' 00:00:00';
        }
        if ($request->to_date) {
            $to_date = Carbon::parse($request->to_date)->format('Y-m-d').' 23:59:00';
        }else{
            $to_date = Carbon::today()->format('Y-m-d').' 23:59:00';
        }

        $data = AnswerQuestionUser::where('status',1)
            ->where(function($query) use($request){
                if ($request->get('status_vehicle_id')) {
                    if ($request->get('status_vehicle_id') == 2) {
                        $query->whereIn('status_vehicle_id',[2,1]);
                    }else{
                        $query->where('status_vehicle_id',$request->get('status_vehicle_id'));
                    }
                }

                if ($request->get('location')) {
                    $location = MasterLocation::findOrFail($request->get('location'));
                    $user_id_location = User::where('location_id', $location->code)->pluck('id');
                    $query->whereIn('user_id', $user_id_location);
                }

                if ($request->get('company')) {
                    $company = Company::findOrFail($request->get('company'));
                    $user_id_company = User::where('company_id', $company->code)->pluck('id');
                    $query->whereIn('user_id', $user_id_company);
                }
            })
            ->whereBetween('created_at', [$start_date,$to_date])
            ->whereNotNull('status_vehicle_id')
            ->whereNotNull('total_point')
            ->whereNotNull('status')
            ->whereNull('recheck_answer_question_user_id')
            ->whereIn('slug', ['checklist-health', 'quiz'])
            ->orderBy('id', 'desc')
            ->get();

        $search = $request->search;

        return view('export.k3', ['data' => $data, 'search' => $search]);
    }

    public function categoriesReportVehicle(Request $request)
    {
        $request->category;

        $search = [
            ['name' =>'STNK', 'slug' =>'kel_doc'],
            ['name' =>'KIR', 'slug' =>'kel_doc'],
            ['name' =>'KM SPIDOMETER', 'slug' =>'kabin'],
            ['name' =>'LAMPU INDOKATOR PANEL', 'slug' =>'kabin'],
            ['name' =>'STARTER', 'slug' =>'kabin'],
            ['name' =>'REM KAKI', 'slug' =>'kabin'],
            ['name' =>'REM TANGAN', 'slug' =>'kabin'],
            ['name' =>'KOPLING', 'slug' =>'kabin'],
            ['name' =>'KLAKSON', 'slug' =>'kabin'],
            ['name' =>'WIPPER', 'slug' =>'kabin'],
            ['name' =>'LAMPU SEN DEPAN KANAN DAN KIRI', 'slug' =>'kabin'],
            ['name' =>'LAMPU SEN BELAKANG KANAN DAN KIRI', 'slug' =>'kabin'],
            ['name' =>'LAMPU UTAMA DEPAN DAN BELAKANG', 'slug' =>'kabin'],
            ['name' =>'LAMPU KECIL', 'slug' =>'kabin'],
            ['name' =>'LAMPU KABUT', 'slug' =>'kabin'],
            ['name' =>'LAMPU REM', 'slug' =>'kabin'],
            ['name' =>'LAMPU MUNDUR', 'slug' =>'kabin'],
            ['name' =>'LAMPU HAZARD', 'slug' =>'kabin'],
            ['name' =>'LAMPU KABIN', 'slug' =>'kabin'],
            ['name' =>'LAMPU BOX', 'slug' =>'kabin'],
            ['name' =>'KURSI JOK', 'slug' =>'kabin'],
            ['name' =>'SEAT BELT', 'slug' =>'kabin'],
            ['name' =>'TOOL KIT / KUNCI RODA', 'slug' =>'kabin'],
            ['name' =>'DONGKRAK', 'slug' =>'kabin'],
            ['name' =>'SEGITIGA PENGAMAN', 'slug' =>'kabin'],
            ['name' =>'APAR', 'slug' =>'kabin'],
            ['name' =>'P3K', 'slug' =>'kabin'],
            ['name' =>'KONDISI KACA DEPAN', 'slug' =>'kabin'],
            ['name' =>'SPION KANAN DAN KIRI', 'slug' =>'kabin'],
            ['name' =>'GANJEL BAN', 'slug' =>'kabin'],
            ['name' =>'PINTU KABIN KANAN DAN KIRI', 'slug' =>'kabin'],
            ['name' =>'KACA JENDELA PINTU KABIN KANAN DAN KIRI', 'slug' =>'kabin'],
            ['name' =>'KONDISI BODY KABIN', 'slug' =>'kabin'],
            ['name' =>'OLI MESIN', 'slug' =>'kabin'],
            ['name' =>'AIR RADIATOR', 'slug' =>'mesin'],
            ['name' =>'TALI KIPAS', 'slug' =>'mesin'],
            ['name' =>'AIR ACCU KENDARAAN', 'slug' =>'mesin'],
            ['name' =>'TUTUP TANGKI', 'slug' =>'mesin'],
            ['name' =>'KONDISI DINDING BOX', 'slug' =>'mesin'],
            ['name' =>'GEMBOK', 'slug' =>'mesin'],
            ['name' =>'KONDISI PINTU BOX KANAN DAN KIRI', 'slug' =>'box_kendaraan'],
            ['name' =>'KONDISI DALAM BOX', 'slug' =>'box_kendaraan'],
            ['name' =>'KONDISI KEBOCORAN BOX', 'slug' =>'box_kendaraan'],
            ['name' =>'KARET PINTU BOX', 'slug' =>'box_kendaraan'],
            ['name' =>'KONDISI BAN DEPAN KANAN', 'slug' =>'box_kendaraan'],
            ['name' =>'KONDISI BAN DEPAN KIRI', 'slug' =>'box_kendaraan'],
            ['name' =>'KONDISI BAN BELAKANG KIRI LUAR DAN DALAM', 'slug' =>'ban'],
            ['name' =>'KONDISI BAN BELAKANG KANAN LUAR DAN DALAM', 'slug' =>'ban'],
            ['name' =>'BAN STIP', 'slug' =>'ban'],
            ['name' =>'KONDISI BAN STIP', 'slug' =>'ban'],
        ];

        $data = [];

        $i = 1;
        foreach ($request->category as $key => $value) {
            $data[0] = ['name' =>'All', 'slug' =>'All'];
            foreach ($search as $ckey => $cvalue) {
                if ($cvalue['slug'] == $value || $value == "All") {
                    $data[$i] = $cvalue;
                    $i++;
                }
            }
        }
        return $data;
    }

    public function cekKeputusan(Request $request)
    {
        $state = $request->slug;


        if ($state == 'checklist-health') {
            $data = AnswerQuestionUser::where('slug', 'checklist-health')
            ->whereNotNull('status_vehicle_id')
            ->where('user_id', Auth::user()->id)
            ->whereDate('created_at', now())
            ->orderBy('id', 'desc')
            ->first();

            if ($data->bring_it_home == 1) {
                $data['message'] = 'Silahkan Istirahat Dirumah';
            }else if($data->waiting != null){
                $waiting = Carbon::parse($data->waiting)->addMinutes(30);
                if (!$waiting->lte(Carbon::now())) {
                    $data['message'] = 'Harap cek kesehatan lagi setelah '. $waiting->format('H:i d-m-Y');
                }
            }else if($data->status_vehicle_id == 4 && $data->vehicle_next_danger == null){
                $data['message'] = 'Kesehatan Anda Buruk Tunggu Arahan Dari K3';
            }
            // else if($data->status_vehicle_id != 4 && $data->vehicle_next_danger == 1){
            //     $data['message'] = 'Anda boleh lanjut';
            // }
        }else{
            $data = AnswerQuestionUser::where('slug', 'checklist-vehicle')
            ->whereNotNull('status_vehicle_id')
            ->where('user_id', Auth::user()->id)
            ->orderBy('id', 'desc')
            ->first();

            if($data->vehicle_repair == 1 && $data->vehicle_next_danger == null){
                $data['message'] = 'Kendaraan Sedang Diperbaiki';
            }else if($data->status_vehicle_id == 4){
                if ($data->vehicle_next_danger == 1) {
                    $data['message'] = 'Anda bisa jalan tekan tombol terima';
                }elseif ($data->vehicle_next_danger == 2) {
                    $data['message'] = 'Anda bisa jalan';
                }else{
                    $data['message'] = 'Status Kendaraan Merah Tunggu Arahan Dari Koordinator';
                }
            }
        }

        return response()->json(['status' => true, 'message' => $data['message'], 'data' => $data]);
    }

    // JIKA KENDARAAN MERAH DAN BOLEHJALAN OLEH KOORDI MAKA UPDATE
    // vehicle_next_danger = 2 (klik tombol terima)
    public function approveKendaraanMerah($id)
    {
        try {
            $aqu = AnswerQuestionUser::findOrFail($id);
            $aqu->vehicle_next_danger = 2;
            $aqu->save();

            return response()->json(['status' => true, 'message' => 'Kendaraan anda bisa lanjut jalan']);
        } catch (\Throwable $th) {
            return response()->json(['status' => false, 'message' => $th->getMessage()]);
        }
    }
}
