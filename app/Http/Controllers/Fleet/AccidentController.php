<?php

namespace App\Http\Controllers\Fleet;

use App\Http\Controllers\Controller;
use App\Http\Helper;
use App\Models\Fleet\Accident;
use App\Models\Fleet\AnswerQuestionUser;
use App\Models\Fleet\Asset;
use App\Models\Fleet\AssetDetail;
use App\Models\Fleet\Company;
use App\Models\Fleet\Department;
use App\Models\Fleet\DocumentAccident;
use App\Models\Fleet\MasterLocation;
use App\Workshop\Insurance;
use App\Workshop\Workshop;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class AccidentController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        try {
            if ($request->has('start_date')) {
                $start_date = Carbon::parse($request->get('start_date'))->format('Y-m-d').' 00:00:00';
            }else{
                $start_date = Carbon::today()->format('Y-m-d').' 00:00:00';
            }
            if ($request->has('end_date')) {
                $end_date = Carbon::parse($request->get('end_date'))->format('Y-m-d').' 23:59:00';
            }else{
                $end_date = Carbon::today()->format('Y-m-d').' 23:59:00';
            }


            $company = [];
            if ($request->has('company')) {
                $company_get = $request->input('company');
                if (is_array($company_get)) {
                    for ($i=0; $i < count($company_get); $i++) { 
                        $company[] = $company_get[$i];
                    }
                }
            }

            $departement = [];
            if ($request->has('departement')) {
                $departement_get = $request->input('departement');
                if (is_array($departement_get)) {
                    for ($i=0; $i < count($departement_get); $i++) { 
                        $departement[] = $departement_get[$i];
                    }
                }
            }

            $location = [];
            if ($request->has('location')) {
                $location_get = $request->input('location');
                if (is_array($location_get)) {
                    for ($i=0; $i < count($location_get); $i++) { 
                        $location[] = $location_get[$i];
                    }
                }
            }

            $companies = Company::select('name','id')->get();
            $departments = Department::select('name','id')->get();
            $locations = MasterLocation::select('name','id')->get();

            $asset_id = Asset::
                where(function($q) use($company,$location,$departement){
                if (is_array($company) && count($company)) {
                    $q->whereIn('company_id',$company);
                }
                if (is_array($location) && count($location)) {
                    $q->whereIn('location_id',$location);
                }
                if (is_array($departement) && count($departement)) {
                    $q->whereIn('department_id',$departement);
                }
            })->pluck('id');

            $accident = Accident::with('driver')
            ->whereIn('asset_id', $asset_id)
            ->whereBetween('created_at', [$start_date, $end_date])
            ->where(function($query) use($request){
                if ($request->get('status')) {
                    if ($request->get('status') == 'klaim') {
                        $query->where('status', 0);
                    }else{
                        $query->where('status', $request->get('status'));
                    }
                }
            })
            ->orderBy('id','desc')->get();

            foreach ($accident as $key => $value) {
                $value->location_accident = 'https://www.google.com/maps/@'.$value->long.','.$value->lat;
                $value->nomor_polisi = AssetDetail::where('asset_id', $value->asset_id)->where('attribute_code', 'nomor_polisi')->first()->value ?? null;
                $value->existence_insurance = Asset::findOrFail($value->asset_id)->insurance_id ? 'ada' : 'tidak ada';
                $value->insurance_name = Asset::findOrFail($value->asset_id)->insurance->name ?? '-';
                $value->company = $value->asset->companies->name ?? null;
                $value->department = $value->asset->department->name ?? null;
                $value->location = $value->asset->location->name ?? null;
                $value->status = $value->existence_insurance == 'ada' ? Helper::statusAccident($value->status) : 'Tidak Terdaftar Asuransi';
                $value->document = ($value->documentAccident->count() >= 7) ? true : false;
            }

            if ($request->get('existence_insurance')) {
                $accident_existence_insurance = [];
                $index = 0;
                foreach ($accident as $value) {
                    if ($request->get('existence_insurance') == 'ada' && $value->existence_insurance == 'ada') {
                        $accident_existence_insurance[$index] = $value;
                        $index++;
                    }elseif ($request->get('existence_insurance') == 'tidak ada' && $value->existence_insurance == 'tidak ada') {
                        $accident_existence_insurance[$index] = $value;
                        $index++;
                    }
                }

                $accident = $accident_existence_insurance;
            }

            if ($request->get('nomor_polisi')) {
                $accident_nomor_polisi = [];
                $indexNopol = 0;
                foreach ($accident as $value) {
                    $nomor_polisi = AssetDetail::where('asset_id', $value->asset_id)->where('attribute_code', 'nomor_polisi')->first()->value ?? null;
                    if ($nomor_polisi == $request->get('nomor_polisi')) {
                        $accident_nomor_polisi[$indexNopol] = $value;
                        $indexNopol++;
                    }
                }

                $accident = $accident_nomor_polisi;
            }

            return response()->json(['accident' => $accident, 'companies' => $companies, 'departments' => $departments, 'locations' => $locations]);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        try{
            $data = $request->all();
            $long = $request->long;
            $lat = $request->lat;
            if ($long != null&& $lat!= null) {
                $kendaraan_last = AnswerQuestionUser::with('statusVehicle')->where('user_id',Auth::user()->id)->where('slug','checklist-vehicle')->whereNotNull('status_vehicle_id')->orderBy('id','desc')->first();
                if ($kendaraan_last) {
                    $data['driver_id'] = $kendaraan_last->user_id;
                    $data['asset_id'] = $kendaraan_last->asset_id;
                    $data['status'] = 0;
                    $accident = Accident::create($data);
        
                    $dataWorkshop = new Workshop();
                    $dataWorkshop->no_seri = 'LJR-'.now()->format('YmdHis').rand(10, 99);
                    $dataWorkshop->asset_id = $kendaraan_last->asset_id;
                    $dataWorkshop->type = 'Accident';
                    $dataWorkshop->user_id = Auth::user()->id;
                    $dataWorkshop->driver_id = $kendaraan_last->user_id;
                    $dataWorkshop->status_perbaikan_id = 1;
                    $dataWorkshop->accident_id = $accident->id;
                    $dataWorkshop->answer_question_user_id = $kendaraan_last->id;
                    $dataWorkshop->km_service = Asset::findOrFail($kendaraan_last->asset_id)->km_actual;
                    $dataWorkshop->save();

                    $asset = Asset::findOrFail($kendaraan_last->asset_id);
                    $asset->maintenance = 1;
                    $asset->save();
                    
                    return response()->json(['success' => true, 'message' => 'Pengajuan Accident Sudah Terkirim!']);
                }else{
                    return response()->json(['success' => false, 'message' => 'Data kendaraan '.Auth::user()->full_name." Tidak Ditemukan"]);
                }
            }else{
                return response()->json(['success' => false, 'message' => "Izinkan lokasi Anda untuk mengakses fitur ini"]);
            }
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 200);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        try {
            $data = Accident::findOrFail($id);
            $docAcident = collect($data->documentAccident);
            $data->police_letter = ($docAcident->where('attribute_code', 'police_letter')->first() != null) ? true : false;
            $data->sim = ($docAcident->where('attribute_code', 'sim')->first() != null) ? true : false;
            $data->stnk = ($docAcident->where('attribute_code', 'stnk')->first() != null) ? true : false;
            $data->kir = ($docAcident->where('attribute_code', 'kir')->first() != null) ? true : false;
            $data->chronology = ($docAcident->where('attribute_code', 'chronology')->first() != null) ? true : false;
            $data->form_klaim = ($docAcident->where('attribute_code', 'form_klaim')->first() != null) ? true : false;
            $data->another_document = ($docAcident->where('attribute_code', 'another_document')->first() != null) ? true : false;
            
            // FILE
            $data->file_police_letter = $docAcident->where('attribute_code', 'police_letter')->first()->value ?? null;
            $data->file_sim = $docAcident->where('attribute_code', 'sim')->first()->value ?? null;
            $data->file_stnk = $docAcident->where('attribute_code', 'stnk')->first()->value ?? null;
            $data->file_kir = $docAcident->where('attribute_code', 'kir')->first()->value ?? null;
            $data->file_chronology = $docAcident->where('attribute_code', 'chronology')->first()->value ?? null;
            $data->file_form_klaim = $docAcident->where('attribute_code', 'form_klaim')->first()->value ?? null;
            $data->file_another_document = $docAcident->where('attribute_code', 'another_document')->first()->value ?? null;

            return response()->json($data);
        } catch (\Throwable $th) {
            return response()->json(['success' => false, 'message' => $th->getMessage()], 500);
        }
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $data = Accident::findOrFail($id);
        return response()->json($data);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        try{
            $data = $request->all();
            if ($data['date_claim']) {
                $uppdateData['date_claim'] = $data['date_claim'];
                $uppdateData['status'] = 1;
            }
            if ($data['date_survei']) {
                $uppdateData['date_survei'] = $data['date_survei'];
                $uppdateData['status'] = 3;
            }
            if ($data['date_to_workshop']) {
                $uppdateData['date_to_workshop'] = $data['date_to_workshop'];
                $uppdateData['status'] = 4;
            }
            if ($data['spk']) {
                $uppdateData['spk'] = $data['spk'];
                $uppdateData['status'] = 5;
            }
            if ($data['finish_repair']) {
                $uppdateData['finish_repair'] = $data['finish_repair'];
                $uppdateData['status'] = 6;
            }

            $WorkshopDetail = Accident::findOrFail($id)->update($uppdateData);
            
            return response()->json(['success' => true, 'message' => 'Berhasil edit data']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $data = Accident::findOrFail($id);
        $data->delete();
        $response = array(
            'success' => true,
            'message' => 'Berhasil hapus data'
        );

        return response()->json($response);
    }

    public function addDocument(Request $request, $id)
    {
        if ($request->police_letter) {
            $data_police_letter['value'] = Helper::saveFile($request->police_letter, 'police_letter');
            $data_police_letter['attribute_code'] = 'police_letter';
            $data_police_letter['name'] = 'police_letter';
            
            $data_police_letter['accident_id'] = $id;
            $doc_police_letter = DocumentAccident::create($data_police_letter);
        }
        if ($request->sim) {
            $data_sim['value'] = Helper::saveFile($request->sim, 'sim');
            $data_sim['attribute_code'] = 'sim';
            $data_sim['name'] = 'sim';
            
            $data_sim['accident_id'] = $id;
            $doc_sim = DocumentAccident::create($data_sim);
        }
        if ($request->stnk) {
            $data_stnk['value'] = Helper::saveFile($request->stnk, 'stnk');
            $data_stnk['attribute_code'] = 'stnk';
            $data_stnk['name'] = 'stnk';
            
            $data_stnk['accident_id'] = $id;
            $doc_stnk = DocumentAccident::create($data_stnk);
        }
        if ($request->kir) {
            $data_kir['value'] = Helper::saveFile($request->kir, 'kir');
            $data_kir['attribute_code'] = 'kir';
            $data_kir['name'] = 'kir';
            
            $data_kir['accident_id'] = $id;
            $doc_kir = DocumentAccident::create($data_kir);
        }
        if ($request->chronology) {
            $data_chronology['value'] = Helper::saveFile($request->chronology, 'chronology');
            $data_chronology['attribute_code'] = 'chronology';
            $data_chronology['name'] = 'chronology';
            
            $data_chronology['accident_id'] = $id;
            $doc_chronology = DocumentAccident::create($data_chronology);
        }
        if ($request->form_klaim) {
            $data_form_klaim['value'] = Helper::saveFile($request->form_klaim, 'form_klaim');
            $data_form_klaim['attribute_code'] = 'form_klaim';
            $data_form_klaim['name'] = 'form_klaim';
            
            $data_form_klaim['accident_id'] = $id;
            $doc_form_klaim = DocumentAccident::create($data_form_klaim);
        }
        if ($request->another_document) {
            // return Helper::saveFile($data['another_document'], 'another_document');
            $data_another_document['value'] = Helper::saveFile($request->another_document, 'another_document');
            $data_another_document['attribute_code'] = 'another_document';
            $data_another_document['name'] = 'another_document';
            
            $data_another_document['accident_id'] = $id;
            $doc_another_document = DocumentAccident::create($data_another_document);
        }
        
        $accident = Accident::findOrFail($id);
        $accident->status = ($accident->documentAccident->count() >= 7) ? 2 : 1;
        $accident->save();
    }
}
