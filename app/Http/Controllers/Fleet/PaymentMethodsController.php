<?php

namespace App\Http\Controllers\Fleet;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Fleet\PaymentMethod;
use Illuminate\Support\Facades\Validator;

class PaymentMethodsController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $data = PaymentMethod::orderBy('id','desc')->get();
        return response()->json($data);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        try{
            $validator = Validator::make($request->all(), [
                'payment_name' => 'required',
                'payment_type' => 'required',
                'receipt_type' => 'required',
                'use_pre_printed_stationery' => 'required',
                'open_cash_drawer' => 'required',
            ]);
    
            if ($validator->fails()) {
                return response()->json($validator->errors(), 500);
            }
            
            $PaymentTerm = PaymentMethod::create($request->all());

            return response()->json(['success' => true, 'message' => 'Berhasil input data']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $data = PaymentMethod::find($id);
        return response()->json($data);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'payment_name' => 'required',
            'payment_type' => 'required',
            'receipt_type' => 'required',
            'use_pre_printed_stationery' => 'required',
            'open_cash_drawer' => 'required',
        ]);
 
        if ($validator->fails()) {
            return response()->json($validator->errors(), 500);
        }
        
        $PaymentTerm = PaymentMethod::find($id)->update($request->all());

        return response()->json(['success' => true, 'message' => 'Berhasil update data']);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $divisi = PaymentMethod::destroy($id);

        return response()->json(['success' => true, 'message' => 'Berhasil delete data']);
    }
}
