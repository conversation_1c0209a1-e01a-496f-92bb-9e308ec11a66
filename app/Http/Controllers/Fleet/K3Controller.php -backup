<?php

namespace App\Http\Controllers\Fleet;

use App\Exports\ExportCheklistKendaraan;
use App\Exports\ExportK3;
use App\FleetMaster\Vehicle;
use App\Http\Controllers\Controller;
use App\Http\Helper;
use App\Models\Fleet\AnswerQuestion;
use App\Models\Fleet\AnswerQuestionDetail;
use App\Models\Fleet\AnswerQuestionUser;
use App\Models\Fleet\CategoryQuestion;
use App\Models\Fleet\HistoryWorkshop;
use App\Models\Fleet\K3;
use App\Models\Fleet\RiwayatAnswerQuestionUser;
use App\Models\Fleet\StatusPerbaikan;
use App\Models\Fleet\StatusVehicle;
use App\Models\User;
use App\Workshop\Workshop;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;
use Maatwebsite\Excel\Facades\Excel;

class K3Controller extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        try {
            if ($request->has('start_date')) {
                $start_date = Carbon::parse($request->get('start_date'))->format('Y-m-d').' 00:00:00';
            }else{
                $start_date = Carbon::today()->format('Y-m-d').' 00:00:00';
            }
            if ($request->has('to_date')) {
                $to_date = Carbon::parse($request->get('to_date'))->format('Y-m-d').' 23:59:00';
            }else{
                $to_date = Carbon::today()->format('Y-m-d').' 23:59:00';
            }
            $data['start_date'] = Carbon::parse($start_date)->format('Y-m-d');
            $data['to_date'] = Carbon::parse($to_date)->format('Y-m-d');

            if ($request->type) {
                $k3 = AnswerQuestionUser::whereBetween('created_at', [$start_date,$to_date])
                ->where(function($query) use($request){
                    if ($request->get('status_vehicle_id')) {
                        if ($request->get('status_vehicle_id') == 2) {
                            $query->whereIn('status_vehicle_id',[2,1]);
                        }else{
                            $query->where('status_vehicle_id',$request->get('status_vehicle_id'));
                        }
                    }
                })
                ->where('status',1)
                ->whereNotNull('status_vehicle_id')
                ->where('slug', $request->type)
                ->orderBy('id', 'asc')
                ->get();
            }else{
                $k3 = AnswerQuestionUser::whereBetween('created_at', [$start_date,$to_date])
                ->where(function($query) use($request){
                    if ($request->get('status_vehicle_id')) {
                        if ($request->get('status_vehicle_id') == 2) {
                            $query->whereIn('status_vehicle_id',[2,1]);
                        }else{
                            $query->where('status_vehicle_id',$request->get('status_vehicle_id'));
                        }
                    }
                })
                ->where('status',1)
                ->whereNotNull('status_vehicle_id')
                ->whereIn('slug', ['checklist-health','quiz'])
                ->orderBy('id', 'asc')
                ->get();
            }

            $simpan_id = [];
            $id_tampil = [];
            $repeat = [];
            foreach ($k3 as $key => $datas) {
                $tanggal_create = Carbon::parse($datas->created_at)->format('Y-m-d');
                if (isset($simpan_id[$datas->user_id][$datas->slug][$tanggal_create])) {
                    $repeat[$datas->id] = $datas->id;
                    $kedua = true;
                }else{
                    $kedua = false;
                    $simpan_id[$datas->user_id][$datas->slug][$tanggal_create] = $datas->id;
                }

                if ($request->has('check_number')) {
                    if ($request->get('check_number') == 1) {
                        if (!$kedua) {
                            $id_tampil[$datas->id] = $datas->id;
                        }
                    }elseif($request->get('check_number') == 2){
                        if ($kedua) {
                            $id_tampil[$datas->id] = $datas->id;
                        }
                    }else{
                        $id_tampil[$datas->id] = $datas->id;
                    }
                }else{
                    $id_tampil[$datas->id] = $datas->id;
                }
            }

            $data['k3'] = AnswerQuestionUser::with(['user', 'statusVehicle', 'answerQuestionDetail','riwayatTindakan.approveBy'])
            ->whereIn('id',$id_tampil)
            ->orderBy('id', 'desc')
            ->get();

            foreach ($data['k3'] as $key => $datas) {
                if (isset($repeat[$datas->id])) {
                    $data['k3'][$key]['kedua'] = true;
                }else{
                    $data['k3'][$key]['kedua'] = false;
                }
            }

            $data['jumlah_data'] = AnswerQuestionUser::whereIn('id',$id_tampil)
            ->count();

            $data['jumlah_data_merah'] = AnswerQuestionUser::whereIn('id',$id_tampil)
            ->where('status_vehicle_id', 4)
            ->count();
            
            $data['jumlah_data_hijau'] = AnswerQuestionUser::whereIn('id',$id_tampil)
            ->whereIn('status_vehicle_id', [2,1])
            ->count();

            $data['status_vehicle'] = StatusVehicle::get();
            return response()->json($data);

        } catch (\Throwable $th) {
            return response()->json(['success' => false, 'message' => $th->getMessage()]);
        }
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        try{
            $data = AnswerQuestionUser::with(['user'])->findOrFail($id);
            if ($data['status_vehicle_id'] == 2 || $data['status_vehicle_id'] == 1) {
                $data['status'] = "Abnormal";
            }else{
                $data['status'] = 'Normal';
            }
            $data['user']['photo'] = "http://".\Request::getHost().":8000/".$data->user->photo;
            
            $dataQuestion =  [];
            foreach ($data->answerQuestionDetail as $key => $value) {
                $dataQuestion[$key]['id'] = $value->id;
                $dataQuestion[$key]['question'] = $value->question;
                $dataQuestion[$key]['answer_id'] = $value->answer_question_id;
                $answer = AnswerQuestion::findOrFail($value->answer_question_id);
                if ($answer->danger == 1) {
                    $dataQuestion[$key]['status_color'] = 'red';
                }else {
                    $statusVehicle = StatusVehicle::where('start', '<=', $answer->point)->where('to', '>=', $answer->point)->first();
                    if (!$statusVehicle) {
                        $statusVehicle = StatusVehicle::orderBy('to', 'desc')->first();
                    }

                    $dataQuestion[$key]['status_color'] = $statusVehicle->name;
                }
                $dataQuestion[$key]['point'] = $answer->point;

                $dataQuestion[$key]['answer_question'] = AnswerQuestion::where('question_id', $value->question_id)->get();
            }

            $data['answered_category_list'] = array(
                'category' => 'Cheklist Kesehatan',
                'status' => $data['status'],
                'answered_question_list' => $dataQuestion,
            );

            return response()->json(['success' => true, 'data' => $data]);
        } catch (\Throwable $th) {
            return response()->json(['success' => false, 'message' => $th->getMessage()]);
        }
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        try {
            $data['detail'] = AnswerQuestionUser::with('user', 'statusVehicle')->findOrFail($id);
            $data['status'] = StatusVehicle::orderBy('id', 'desc')->get();

            return response()->json($data);
        } catch (\Throwable $th) {
            return response()->json(['success' => false, 'message' => $th->getMessage()]);
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        try{
            $data = $request->all();
            $datas['status_vehicle_id'] = $data['status_vehicle_id'];
            $datas['note'] = $data['note'];
            $answerQuestionUser = AnswerQuestionUser::findOrFail($id)->update($datas);
            
            $datas['user_id'] = Auth::user()->id;
            $datas['answer_question_user_id'] = $id;
            $k3 = K3::create($datas);

            return response()->json(['success' => true, 'message' => 'Berhasil edit data']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }

    public function pulangkan($id)
    {
        try {
            $data['bring_it_home'] = 1;
            $aqu = AnswerQuestionUser::findOrFail($id)->update($data);
            
            $datas['answer_question_user_id'] = $id;
            $datas['status_k3'] = 2;
            $datas['user_id'] = Auth::user()->id;
            $datas['note'] = 'Pulangkan';
            $raqu = RiwayatAnswerQuestionUser::create($datas);

            return response()->json(['success' => true, 'message' => 'Update data berhasil']);
        } catch (\Throwable $th) {
            return response()->json(['success' => false, 'message' => $th->getMessage()], 500);
        }
    }

    public function listDashboard(Request $request)
    {
        if ($request->tanggal) {
            $tanggal = $request->tanggal;
        }else{
            $tanggal = Carbon::today();
        }
        if ($request->status) {
            $status = $request->status;
        }else{
            $status = 'all';
        }

        // $data = [];

        $data = AnswerQuestionUser::with(['statusVehicle','user'])
        ->whereDate('created_at', $tanggal)
        ->where(function($query) use($status){
            if ($status) {
                if ($status == 'normal') {
                    $query->whereIn('status_vehicle_id',[2,1]);
                }elseif ($status == 'upnormal') {
                    $query->where('status_vehicle_id',4);
                }else{
                    $query->whereIn('status_vehicle_id',[2,1,4]);
                }
            }
        })
        ->where('slug', 'checklist-health')
        // ->with(['user', 'statusVehicle', 'answerQuestionDetail'])
        ->orderBy('id', 'desc')
        ->select('id','user_id','slug','input_date','total_point','status_vehicle_id','location_checklist_id','note','recheck_answer_question_user_id','bring_it_home')
        ->get();

        foreach ($data as $ckey => $cvalue) {
            // PEMERIKSAAN 2
            $data[$ckey]['rechek2'] = AnswerQuestionUser::where('recheck_answer_question_user_id', $cvalue->id)->where('slug', 'checklist-health')->with('user', 'statusVehicle', 'answerQuestionDetail')->orderBy('id', 'asc')->first();
        
            // DECIDED KOORDINATOR
            $raqu = RiwayatAnswerQuestionUser::where('answer_question_user_id', $cvalue->id)->first() ?? null;
            if ($raqu) {
                $data[$ckey]['decided'] = true;
            }else {
                $data[$ckey]['decided'] = false;
            }
        }

        return response()->json(['success' => true, 'data' => $data]);
    }

    public function detailDashboard($id){
        $data = AnswerQuestionUser::with(['statusVehicle','user'])
        ->select('id','user_id','slug','input_date','total_point','status_vehicle_id','location_checklist_id','note','recheck_answer_question_user_id','bring_it_home')
        ->where('id',$id)
        ->first();

        $answer = AnswerQuestionDetail::select('id','answer_question_user_id','question','answer','point','image')
        ->where('answer_question_user_id',$id)
        ->get();

        return response()->json(['success' => true, 'data' => $data, 'answer' => $answer]);
    }

    public function waiting($id)
    {
        try {
            $datas = AnswerQuestionUser::findOrFail($id);
            if ($datas->waiting) {
                return response()->json(['success' => false, 'message' => 'Tidak bisa melakukan cheklist kembali!!']);
            }else {
                $data['waiting'] = now();
                $aqu = AnswerQuestionUser::findOrFail($id)->update($data);

                $riwayat['answer_question_user_id'] = $id;
                $riwayat['status_k3'] = 1;
                $riwayat['user_id'] = Auth::user()->id;
                $riwayat['note'] = 'Tunggu 30 menit';
                $raqu = RiwayatAnswerQuestionUser::create($riwayat);
                
                return response()->json(['success' => true, 'message' => 'Update data berhasil']);
            }
        } catch (\Throwable $th) {
            return response()->json(['success' => false, 'message' => $th->getMessage()], 500);
        }
    }

    public function tindakan(Request $request)
    {
        try {
            $id = $request->input('id_tindakan');
            $datas = AnswerQuestionUser::findOrFail($id);
            $data['note'] = $request->input('note');
            $aqu = AnswerQuestionUser::findOrFail($id)->update($data);

            $riwayat['answer_question_user_id'] = $id;
            $riwayat['status_k3'] = 3;
            $riwayat['user_id'] = Auth::user()->id;
            $riwayat['note'] = $request->input('note');
            $raqu = RiwayatAnswerQuestionUser::create($riwayat);
            
            return response()->json(['success' => true, 'message' => 'Update data berhasil']);
        } catch (\Throwable $th) {
            return response()->json(['success' => false, 'message' => $th->getMessage()], 500);
        }
    }

    public function actionK3(Request $request, $id)
    {
        $action = $request->action;
        if ($action == 'note') {
            unset($action);
            $data = $request->all();
            $datas['note'] = $data['note'];
            $answerQuestionUser = AnswerQuestionUser::findOrFail($id)->update($datas);
            
            $datas['user_id'] = Auth::user()->id;
            $datas['answer_question_user_id'] = $id;
            
            $k3 = K3::create($datas);
            return response()->json(['success' => true, 'message' => 'Berhasil edit data']);
        }elseif ($action == 'pulangkan') {
            $this->pulangkan($id);
            return response()->json(['success' => true, 'message' => 'Berhasil edit data']);
        }elseif ($action == 'waiting') {
            $this->waiting($id);
            return response()->json(['success' => true, 'message' => 'Berhasil edit data']);
        }
    }

    public function koordinator(Request $request)
    {
        if ($request->has('start_date')) {
            $start_date = Carbon::parse($request->get('start_date'))->toDateString();
        }else{
            $start_date = Carbon::now()->toDateString();
        }
        if ($request->has('to_date')) {
            $to_date = Carbon::parse($request->get('to_date'))->toDateString();
            if ($request->has('to_date') == now()) {
                $to_date = Carbon::now()->addDays(1);
            }
        }else{
            $to_date = Carbon::now()->addDays(1);
        }
        if ($request->status) {
            $status = $request->status;
        }else{
            $status = 'all';
        }

        $data = AnswerQuestionUser::whereBetween('created_at', [$start_date, $to_date])
        ->where('slug', 'checklist-vehicle')
        ->orderBy('id', 'desc')
        ->select('id','user_id','slug','input_date','total_point','status_vehicle_id','location_checklist_id','note','recheck_answer_question_user_id','bring_it_home', 'vehicle_id')
        ->get();

        if ($request->status_vehicle_id) {
            $data = AnswerQuestionUser::whereBetween('created_at', [$start_date, $to_date])
            ->where('slug', 'checklist-vehicle')
            ->where('status_vehicle_id', $request->status_vehicle_id)
            ->orderBy('id', 'desc')
            ->select('id','user_id','slug','input_date','total_point','status_vehicle_id','location_checklist_id','note','recheck_answer_question_user_id','bring_it_home', 'vehicle_id')
            ->get();
        }

        foreach ($data as $ckey => $cvalue) {
            $data[$ckey]['fullname'] = $cvalue->user->first()->first_name." ".$cvalue->user->first()->last_name;
            $data[$ckey]['photo'] = "http://".\Request::getHost().":8000/".$cvalue->user->first()->photo;
            $data[$ckey]['license_no'] = $cvalue->vehicle->license_no;

            // $dataQuestion =  [];
            // foreach ($cvalue->answerQuestionDetail as $key => $value) {
            //     $dataQuestion[$key]['id'] = $value->id;
            //     $dataQuestion[$key]['question'] = $value->question;
            //     $dataQuestion[$key]['answer_id'] = $value->answer_question_id;
            //     $answer = AnswerQuestion::findOrFail($value->answer_question_id);
            //     if ($answer->danger == 1) {
            //         $dataQuestion[$key]['status_color'] = 'red';
            //     }else {
            //         $statusVehicle = StatusVehicle::where('start', '<=', $answer->point)->where('to', '>=', $answer->point)->first();
            //         if (!$statusVehicle) {
            //             $statusVehicle = StatusVehicle::orderBy('to', 'desc')->first();
            //         }
    
            //         $dataQuestion[$key]['status_color'] = $statusVehicle->name;
            //     }
            //     $dataQuestion[$key]['point'] = $answer->point;
            //     if ($value->image) {
            //         $dataQuestion[$key]['image'] = "http://".\Request::getHost().":8000/".$value->image;
            //     }else {
            //         $dataQuestion[$key]['image'] = null;
            //     }
    
            //     $dataQuestion[$key]['answer_question'] = AnswerQuestion::where('question_id', $value->question_id)->get();
            // }
            
            // $data[$ckey]['answered_category_list'] = array(
            //     'category' => 'Koordinator',
            //     'answered_question_list' => $dataQuestion,
            // );
            // DECIDED KOORDINATOR
            $raqu = RiwayatAnswerQuestionUser::where('answer_question_user_id', $cvalue->id)->first() ?? null;
            if ($raqu) {
                $data[$ckey]['decided'] = true;
            }else {
                $data[$ckey]['decided'] = false;
            }
            // PEMERIKSAAN 2
            $data[$ckey]['rechek2'] = AnswerQuestionUser::where('recheck_answer_question_user_id', $cvalue->id)->where('slug', 'checklist-vehicle')->orderBy('id', 'asc')->first();
        }

        return response()->json(['success' => true, 'data' => $data]);
    }

    public function detailKordinator($id)
    {
        $data = AnswerQuestionUser::with(['statusVehicle','user'])->findOrFail($id);
        // DETAIL INFO
        $cq = CategoryQuestion::where('slug', 'checklist-vehicle')->orderBy('id', 'asc')->select(['id', 'name'])->get();
        foreach ($cq as $key => $value) {
            $cq[$key]['status'] = $data->total_point;
            $cq[$key]['question'] = $value->question()->with('answer')->select(['id', 'question'])->get();
            
            foreach ($cq[$key]['question'] as $bkey => $value) {
                $aqd = AnswerQuestionDetail::where('answer_question_user_id', $data->id)->where('question_id', $value->id)->first();
                if ($aqd) {
                    $cq[$key]['question'][$bkey]['answer_id'] = $aqd->answer_question_id;
                    if ($aqd->image) {
                        $cq[$key]['question'][$bkey]['image'] = "http://".\Request::getHost().":8000/".$aqd->image;
                    }else {
                        $cq[$key]['question'][$bkey]['image'] = null;
                    }
                }else {
                    $cq[$key]['question'][$bkey]['answer_id'] = null;
                    $cq[$key]['question'][$bkey]['image'] = null;
                }
            }
        }

        $data['answered_question_list'] = $cq;

        return response()->json(['success' => true, 'data' => $data]);
    }

    public function indexRiwayatAnswerQuestionUser(Request $request)
    {
        if ($request->slug) {
            $aqu = AnswerQuestionUser::where('slug', $request->slug)->pluck('id');
            $data = RiwayatAnswerQuestionUser::whereIn('answer_question_user_id', $aqu)->with(['approveBy', 'answerQuestionUser'])->get();
        }
        if ($request->answer_question_user_id) {
            $data = RiwayatAnswerQuestionUser::with(['approveBy', 'answerQuestionUser'])->where('answer_question_user_id', $request->answer_question_user_id)->get();
        }

        foreach ($data as $key => $value) {
            if ($value->status_k3) {
                $data[$key]['status'] = Helper::status_k3($value->status_k3);
            }elseif ($value->status_koordinator) {
                $data[$key]['status'] = Helper::status_koordinator($value->status_koordinator);
            }
        }

        return response()->json(['success' => true, 'data' => $data]);
    }

    public function riwayatKoordinator(Request $request)
    {
        $data = $request->all();
        $datas['user_id'] = Auth::user()->id;
        $datas['answer_question_user_id'] = $data['answer_question_user_id'];
        $datas['status_koordinator'] = $data['status_koordinator'];
        $datas['note'] = $data['note'];
        $raqu = RiwayatAnswerQuestionUser::create($datas);

        if ($data['status_koordinator'] == 0) {
            $aqu =  AnswerQuestionUser::findOrFail($data['answer_question_user_id']);
    
            $dataWorkshop = new Workshop();
            $dataWorkshop->no_seri = 'LJR-'.now()->format('YmdHis').rand(10, 99);
            $dataWorkshop->vehicle_id = $data['vehicle_id'];
            $dataWorkshop->jenis_perbaikan = 'Perbaikan';
            $dataWorkshop->driver_id = $data['driver_id'];
            $dataWorkshop->answer_question_user_id = $data['answer_question_user_id'];
            $dataWorkshop->save();
    
            try {
                $create_history['status_perbaikan_id'] = StatusPerbaikan::where('name', 'pending')->first()->id;
                $create_history['note'] = $request->note ?? '';
                $create_history['workshop_id'] = $dataWorkshop->id;
                $history_workshop = HistoryWorkshop::create($create_history);
            } catch (\Throwable $th) {
                $th;
            }
    
            $vehilce = Vehicle::findOrFail($request->vehicle_id);
            $vehilce->maintenance = 1;
            $vehilce->save();

            $status = Helper::status_koordinator($data['status_koordinator']);
            $nopol = $vehilce->license_no;
            $approved_by = User::findOrFail($raqu->user_id);
            $approved_at = $raqu->created_at;

            return response()->json(['success' => true, 'message' => 'Berhasil edit data', 'workshop_id' => $dataWorkshop->id, 'approved_by' => $approved_by, 'approved_at' => $approved_at, 'action' => $status, 'nopol' => $nopol]);
        }else {
            return response()->json(['success' => true, 'message' => 'Berhasil edit data']);
            
        }
    }

    public function getHasil($id){
        $answer = AnswerQuestionDetail::with(['answerQuestion','questions.categoryQuestion'])
        ->where('answer_question_user_id',$id)
        ->get();

        return response()->json(['answer' => $answer]);
    }

    public function reportK3(Request $request)
    {
        try {
            if (!$request->search) {
                $request->search = ["All"];
            }
            return Excel::download(new ExportK3($request->get('start_date'), $request->get('to_date'), $request->get('status_vehicle_id'), $request->search), 'laporan k3.xlsx');
            // $data = AnswerQuestionUser::where('status',1)
            // ->whereNotNull('status_vehicle_id')
            // ->whereNull('recheck_answer_question_user_id')
            // ->whereIn('slug', ['checklist-health','quiz'])
            // ->orderBy('id', 'desc')
            // ->limit(20)
            // ->get();

            // foreach ($data as $ckey => $cvalue) {
            //     // PEMERIKSAAN 2
            //     $rechek = AnswerQuestionUser::where('recheck_answer_question_user_id', $cvalue->id)->whereIn('slug', ['checklist-health','quiz'])->with('statusVehicle')->orderBy('id', 'desc')->first();
            //     $data[$ckey]['rechek2'] = $rechek;
            // }

            // return view('export.k3', ['data' => $data]);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    public function reportCheklistKendaraan(Request $request)
    {
        try {
            if (!$request->search) {
                $request->search = ["All"];
            }

            if (!$request->category) {
                $request->category = ["All"];
            }
            return Excel::download(new ExportCheklistKendaraan($request->get('start_date'), $request->get('to_date'), $request->get('status_vehicle_id'), $request->search, $request->category), 'laporan cheklist kendaraan.xlsx');
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    public function indexCheklistKendaraan(Request $request)
    {
        if ($request->start_date) {
            $start_date = Carbon::parse($request->start_date)->format('Y-m-d').' 00:00:00';
        }else{
            $start_date = Carbon::today()->format('Y-m-d').' 00:00:00';
        }
        if ($request->to_date) {
            $to_date = Carbon::parse($request->to_date)->format('Y-m-d').' 23:59:00';
        }else{
            $to_date = Carbon::today()->format('Y-m-d').' 23:59:00';
        }
        
        $data = AnswerQuestionUser::where('status',1)
            ->whereBetween('created_at', [$start_date,$to_date])
            ->whereNotNull('status_vehicle_id')
            ->whereNull('recheck_answer_question_user_id')
            ->where('slug', 'checklist-vehicle')
            ->orderBy('id', 'desc')
            ->get();

        if ($request->status_vehicle_id) {
            if ($request->status_vehicle_id == 2) {
                $data = AnswerQuestionUser::where('status',1)
                        ->whereBetween('created_at', [$start_date,$to_date])
                        ->whereNotNull('status_vehicle_id')
                        ->whereNull('recheck_answer_question_user_id')
                        ->where('slug', 'checklist-vehicle')
                        ->orderBy('id', 'desc')
                        ->where('status_vehicle_id',[2,1])
                        ->get();
            }else{
                $data = AnswerQuestionUser::where('status',1)
                        ->whereBetween('created_at', [$start_date,$to_date])
                        ->whereNotNull('status_vehicle_id')
                        ->whereNull('recheck_answer_question_user_id')
                        ->whereIn('slug', 'checklist-vehicle')
                        ->orderBy('id', 'desc')
                        ->where('status_vehicle_id',$request->status_vehicle_id)
                        ->get();
            }
        }

        $search = $request->search;
        $category = $request->category;

        return view('export.cheklist_kendaraan', ['data' => $data, 'search' => $search, 'category' => $category]);
    }


    public function indexCheklistKesehatan(Request $request)
    {
        if ($request->start_date) {
            $start_date = Carbon::parse($request->start_date)->format('Y-m-d').' 00:00:00';
        }else{
            $start_date = Carbon::today()->format('Y-m-d').' 00:00:00';
        }
        if ($request->to_date) {
            $to_date = Carbon::parse($request->to_date)->format('Y-m-d').' 23:59:00';
        }else{
            $to_date = Carbon::today()->format('Y-m-d').' 23:59:00';
        }
        
        $data = AnswerQuestionUser::where('status',1)
            ->whereBetween('created_at', [$start_date,$to_date])
            ->whereNotNull('status_vehicle_id')
            ->whereNull('recheck_answer_question_user_id')
            ->whereIn('slug', ['checklist-health', 'quiz'])
            ->orderBy('id', 'desc')
            ->get();

        if ($request->status_vehicle_id) {
            if ($request->status_vehicle_id == 2) {
                $data = AnswerQuestionUser::where('status',1)
                        ->whereBetween('created_at', [$start_date,$to_date])
                        ->whereNotNull('status_vehicle_id')
                        ->whereNull('recheck_answer_question_user_id')
                        ->whereIn('slug', ['checklist-health', 'quiz'])
                        ->orderBy('id', 'desc')
                        ->where('status_vehicle_id',[2,1])
                        ->get();
            }else{
                $data = AnswerQuestionUser::where('status',1)
                        ->whereBetween('created_at', [$start_date,$to_date])
                        ->whereNotNull('status_vehicle_id')
                        ->whereNull('recheck_answer_question_user_id')
                        ->whereIn('slug', ['checklist-health', 'quiz'])
                        ->orderBy('id', 'desc')
                        ->where('status_vehicle_id',$request->status_vehicle_id)
                        ->get();
            }
        }

        $search = $request->search;

        return view('export.k3', ['data' => $data, 'search' => $search]);
    }
}
