<?php

namespace App\Http\Controllers\Fleet;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Fleet\SubSection;
use App\Models\Fleet\Section;
use App\Models\User;
use Illuminate\Support\Facades\Validator;

class SubSectionController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $data = SubSection::with('section')->orderBy('id','desc')->get();
        return response()->json($data);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $section = Section::orderBy('id','desc')->get();
        return response()->json(['section' => $section]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $sub_section = User::get()->groupBy('sub_section_id');
        $code = [];
        foreach ($sub_section as $key => $value) {
            $com = SubSection::where('code', $value->first()->sub_section_id)->first();
            if ($com) {
                $com->name = $value->first()->sub_section_name;
                $com->save();
                array_push($code, $value->first()->sub_section_id);
            }else {
                $data['code'] = $value->first()->sub_section_id;
                $data['name'] = $value->first()->sub_section_name;
                $create = SubSection::create($data);
                array_push($code, $value->first()->sub_section_id);
            }
        }
        $delete = SubSection::whereNotIn('code', $code)->delete();
        // $validator = Validator::make($request->all(), [
        //     'name' => 'required',
        //     'section_id' => 'required',
        // ]);
 
        // if ($validator->fails()) {
        //     return response()->json($validator->errors(), 500);
        // }
        
        // $divisi = SubSection::create($request->all());

        return response()->json(['success' => true, 'message' => 'Berhasil Sinkronkan data']);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $data = SubSection::with('user')->find($id);
        return response()->json($data);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $data = SubSection::find($id);
        $section = Section::orderBy('id','desc')->get();
        return response()->json(['data' => $data, 'section' => $section]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required',
            'section_id' => 'required',
        ]);
 
        if ($validator->fails()) {
            return response()->json($validator->errors(), 500);
        }
        
        $divisi = SubSection::find($id)->update($request->all());

        return response()->json(['success' => true, 'message' => 'Berhasil update data']);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $divisi = SubSection::destroy($id);

        return response()->json(['success' => true, 'message' => 'Berhasil delete data']);
    }
}
