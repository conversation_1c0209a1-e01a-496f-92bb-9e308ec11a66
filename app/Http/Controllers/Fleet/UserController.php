<?php

namespace App\Http\Controllers\Fleet;

use DB;
use view;
use App\Http\Helper;
use App\Models\User;
use Faker\Calculator\Ean;
use App\Exports\ExportUser;
use Illuminate\Http\Request;
use App\Models\Fleet\Company;
use App\Models\Fleet\Section;
use App\Models\Fleet\Location;
use PHPUnit\Framework\isEmpty;
use App\Models\Fleet\Department;
use Spatie\Permission\Models\Role;
use App\Http\Controllers\Controller;
use App\Models\Fleet\MasterLocation;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Maatwebsite\Excel\Facades\Excel;

class UserController extends Controller
{
    public function index(Request $request)
    {
        $columns = ['full_name', 'email', 'nip', 'company_name', 'department_name', 'section_name', 'sub_section_name', 'role'];
        $keyword = $request->get('keywoard');
        $user = User::where(function($result) use ($keyword,$columns){
                            foreach($columns as $column)
                            {
                                if($keyword != ''){
                                    $result->orWhere($column,'ilike','%'.$keyword.'%');
                                }
                            }
                        })
                        ->where(function($query) use($request){
                            if ($request->get('company_id')) {
                                $id_company = Company::where('id', $request->get('company_id'))->pluck('code');
                                $query->whereIn('company_id', $id_company);
                            }
                            if ($request->get('department_id')) {
                                $dept_id = Department::where('id', $request->get('department_id'))->pluck('code');
                                $query->whereIn('department_id', $dept_id);
                            }
                            if ($request->get('section')) {
                                $id_company = Section::where('id', $request->get('section'))->pluck('code');
                                $query->whereIn('section_id', $id_company);
                            }
                            if ($request->get('location_id')) {
                                $id_company = MasterLocation::where('id', $request->get('location_id'))->pluck('code');
                                $query->whereIn('location_id', $id_company);
                            }
                            if ($request->get('department') == 'auth' && Auth::user()->role != 'Administrator') {
                                $dept_id = Auth::user()->department_id;
                                $query->where('department_id', $dept_id);
                            }
                        })
                        ->orderBy('id', 'desc')->get();

        return $user;
    }

    public function create()
    {
        $role = Role::all();
        // $role = Role::whereNotIn('name',['Driver'])->pluck('name','name');
        $company = Company::all();
        $department = department::all();

        return response()->json(['role' => $role, 'company' => $company, 'department' => $department]);
    }

    public function delete($id)
    {
        $user = User::destroy($id);

        return "Delete User Success";
    }

    public function store(Request $request)
    {
        $user = new User();
        $user->first_name = $request->get('first_name');
        $user->last_name = $request->get('last_name');
        $user->email = $request->get('email');
        $user->company_id = $request->get('company_id');
        $user->branch_id = $request->get('branch_id');
        $user->birth = $request->get('birth');
        $user->department_id = $request->get('department_id');
        $user->section_id = $request->get('section_id');
        $user->sub_section_id = $request->get('sub_section_id');
        $user->role = $request->get('role');
        $password = $request->get('password');
        if ($password) {
            $user->password = Hash::make($password);
        }

        $user->save();

        $user->assignRole($request->input('role'));

        return "Update User Successfully!";
    }

    public function edit($id)
    {
        $user = User::find($id);
        $user['allow_location'] = json_decode($user->allow_location_id);

        return $user;
    }

    public function update(Request $request, $id)
    {
        $user = User::find($id);
        $user->first_name = $request->get('first_name');
        $user->last_name = $request->get('last_name');
        $user->email = $request->get('email');
        $user->department_id = $request->get('department_id');
        $user->section_id = $request->get('section_id');
        $user->sub_section_id = $request->get('sub_section_id');
        $user->username = $request->username;

        if ($request->get('allow_location_id') == 'All') {
            $allow_location = MasterLocation::pluck('code');
        }else{
            $allow_location = json_encode($request->get('allow_location_id'))?? [];
        }

        $user->allow_location_id = $allow_location;

        $role = [];
        if ($request->has('role_id')) {
            $role = Role::find($request->get('role_id'));
            $user->role = $role->name;
            $user->role_id = $role->id;
        }
        $password = $request->get('password');
        if ($password) {
            $user->password = Hash::make($password);
        }

        $user->save();

        if ($request->has('role_id')) {
            DB::table('model_has_roles')->where('model_id',$id)->delete();
            $user->assignRole($role->name);
        }

        return "Update User Successfully!";
    }

    public function forgotPassword(Request $request)
    {
        $data = $request->all();
        $user = User::find(Auth::user()->id);

        if ($user->email == null) {
            return response([
                'message' => 'Masukkan email terlebih dahulu.',
                'success' => false
            ], 200);
        }

        if (Hash::check($user->password, $data['oldPassword'])) {
            $user->password = $data['newPassword'];
            $user->save();
            return response()->json(['success' => true, 'message' => 'Password berhasil diubah']);
        }else{
            return response()->json(['success' => true, 'message' => 'Password tidak sesuai']);
        }
    }

    // BASE64
    public function photoProfile(Request $request)
    {
        try {
            if ($request->image) {
                $image_parts = explode(";base64,", $request->image);
                if ($image_parts) {
                    $image_type_aux = explode("image/", $image_parts[0]);
                    $image_type = $image_type_aux[1];
                    if ($image_type == 'jpg' || $image_type == 'jpeg' || $image_type == 'png') {
                        $image_base64 = base64_decode($image_parts[1]);
                        $folderPath = 'storage/profile/';
                        $imageName = uniqid();
                        $imageFullPath = $folderPath.$imageName.".".$image_type;
                        file_put_contents($imageFullPath, $image_base64);
                        $users['photo'] = $imageFullPath;
                    }else{
                        return "Format gambar tidak sesuai, Tipe gambar hanya jpg, jpeg, png";
                    }
                }
            }
            $user = User::find(Auth::user()->id)->update($users);
            return response()->json(['success' => true, 'message' => 'foto profile berhasil diubah']);
        } catch (\Throwable $th) {
            return response()->json(['success' => false, 'message' => $th]);
        }
    }

    // IMAGE FILE
    public function changeProfileImage(Request $request)
    {
        try {
            if ($request->hasFile('image')) {
                $file = $request->file('image');
                $file_path = $file->store('storage/profile');
                $users['photo'] = $file_path;
            }
            $user = User::find(Auth::user()->id)->update($users);
            return response()->json(['success' => true, 'message' => 'foto profile berhasil diubah']);
        } catch (\Throwable $th) {
            return response()->json(['success' => false, 'message' => $th]);
        }
    }

    public function export(Request $request)
    {
        $columns = ['full_name', 'email', 'nip', 'company_name', 'department_name', 'section_name', 'sub_section_name', 'role'];
        $keyword = $request->get('keywoard');
        $data = User::where(function($result) use ($keyword,$columns){
                foreach($columns as $column)
                {
                    if($keyword != ''){
                        $result->orWhere($column,'ilike','%'.$keyword.'%');
                    }
                }
            })
            ->where(function($query) use($request){
                if ($request->get('company_id')) {
                    $id_company = Company::where('id', $request->get('company_id'))->pluck('code');
                    $query->whereIn('company_id', $id_company);
                }
                if ($request->get('department_id')) {
                    $dept_id = Department::where('id', $request->get('department_id'))->pluck('code');
                    $query->whereIn('department_id', $dept_id);
                }
                if ($request->get('section')) {
                    $id_company = Section::where('id', $request->get('section'))->pluck('code');
                    $query->whereIn('section_id', $id_company);
                }
                if ($request->get('location_id')) {
                    $id_company = MasterLocation::where('id', $request->get('location_id'))->pluck('code');
                    $query->whereIn('location_id', $id_company);
                }
                if ($request->get('department') == 'auth' && Auth::user()->role != 'Administrator') {
                    $dept_id = Auth::user()->department_id;
                    $query->where('department_id', $dept_id);
                }
            })
            ->orderBy('id', 'desc')->get();

        return Excel::download(new ExportUser($data), 'List Data User.xlsx');
    }

    public function updateEmail(Request $request)
    {
        $user = User::find(Auth::user()->id);
        $user->email = $request->newEmail;
        $user->save();

        return response()->json(['success' => true, 'message' => 'email berhasil diubah']);
    }

    public function findUser(Request $request)
    {
        $permission_name = $request->permission ?? null;
        if($permission_name)
            $permission_name = Helper::getPermissionUser($permission_name);

        $data = User::where(function($query) use($request){
            if ($request->get('company_id')) {
                $id_company = Company::where('id', $request->get('company_id'))->pluck('code');
                $query->whereIn('company_id', $id_company);
            }
            if ($request->get('department_id')) {
                if (!$request->get('section_id') || !$request->get('location_id')) {
                    $dept_id = Department::where('id', $request->get('department_id'))->pluck('code');
                    $query->whereIn('department_id', $dept_id);
                }
            }
        })->get();

        $company_code = [];
        $departmen_code = [];
        $section_code = [];
        $location_code = [];

        foreach ($data as $key => $value) {
            $company_code[$key] = $value->company_id;
            $departmen_code[$key] = $value->department_id;
            $section_code[$key] = $value->section_id;
            $location_code[$key] = $value->location_id;
        }

        if ($request->get('department_id') || $request->get('section_id') || $request->get('location_id')) {
            $departmen_code = [];
            $departmen_code = User::where(function($query) use($request){
                if ($request->get('company_id')) {
                    $id_company = Company::where('id', $request->get('company_id'))->pluck('code');
                    $query->whereIn('company_id', $id_company);
                }
            })->pluck('department_id');
        }

        // $department = Department::whereIn('code', $departmen_code)->orWhere('name', 'ilike', '%Corporate%')->get();
        // $location = MasterLocation::whereIn('code', $location_code)->orWhere('name', 'ilike', '%Corporate%')->get();

        $company = Company::orderBy('name', 'asc')->get();
        $department = Department::where('company_id', $request->get('company_id'))
                                  ->orderBy('name', 'asc')
                                  ->get();
        $section = Section::where('department_id', $request->get('department_id'))
                            ->orderBy('name', 'asc')
                            ->get();
        $location = MasterLocation::where(['company_id' => $request->get('company_id')])
                                    ->orderBy('name', 'asc')
                                    ->get();

        if($permission_name) {
            if(count($permission_name['company']) > 0 && !in_array('all', $permission_name['company'])) {
                $company = $company->whereIn('name', $permission_name['company'] ?? []);
            }else if(count($permission_name['company']) == 0)
                $company = $company->where('name', Auth::user()->company->name ?? '');

            if(count($permission_name['department']) > 0 && !in_array('all', $permission_name['department'])) {
                $department = $department->whereIn('name', $permission_name['department']);
            }else if(count($permission_name['department']) == 0) {
                $department = $department->where('name', Auth::user()->department->name ?? '');
            }

            if(count($permission_name['location']) > 0 && !in_array('all', $permission_name['location']))
                $location = $location->whereIn('name', $permission_name['location']);
            else if(count($permission_name['location']) == 0)
                $location = $location->where('name', Auth::user()->location->name ?? '');

            $company = array_values($company->toArray());
            $department = array_values($department->toArray());
            $location = array_values($location->toArray());
        }

        return response()->json([
                                    'user' => $data,
                                    'company' => $company,
                                    'department' => $department,
                                    'section' => $section,
                                    'location' => $location,
                                ]);
    }
}
