<?php

namespace App\Http\Controllers\Fleet;

use App\Http\Controllers\Controller;
use App\Models\Fleet\TypeItem;
use Illuminate\Http\Request;

class TypeItemController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $columns = ['name', 'code'];
        $keyword = $request->get('filter');
            
        $data = TypeItem::with(['categoryItem'])
        ->where(function($result) use ($keyword,$columns){
            foreach($columns as $column)
            {
                if($keyword != ''){
                    $result->orWhere($column,'ILIKE','%'.$keyword.'%');
                }
            }
            })->orderBy('id', 'desc')->get();

        if ($request->category_item_id) {
            $data = TypeItem::with(['categoryItem'])->where('category_item_id', $request->category_item_id)
                ->where('category_item_id', $request->category_item_id)
                ->where(function($result) use ($keyword,$columns){
                foreach($columns as $column)
                {
                    if($keyword != ''){
                        $result->orWhere($column,'ILIKE','%'.$keyword.'%');
                    }
                }
                })->orderBy('code', 'asc')->get();
        }

        return response()->json($data);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        try{
            $data = $request->all();
            $create = TypeItem::create($data);
            
            return response()->json(['success' => true, 'message' => 'Berhasil input data']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $data = TypeItem::findOrFail($id);
        
        return response()->json($data);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $data = TypeItem::findOrFail($id);
        return response()->json($data);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        try{
            $data = $request->all();
            $edit = TypeItem::findOrFail($id)->update($data);
            
            return response()->json(['success' => true, 'message' => 'Berhasil edit data']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $data = TypeItem::findOrFail($id);
        $data->delete();
        $response = array(
            'success' => true,
            'message' => 'Berhasil hapus data'
        );

        return response()->json($response);
    }
}
