<?php

namespace App\Http\Controllers\GPS;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\FleetMaster\Vehicle;

class GpsMapController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $vehicle = Vehicle::with('category', 'merk', 'color')->whereNotNull('gps_tokens')
        ->whereNotNull('lat')
        ->whereNotNull('lng')
        ->orderBy('id','desc')
        ->get();

        $center = Vehicle::whereNotNull('gps_tokens')
        ->whereNotNull('lat')
        ->whereNotNull('lng')
        ->orderBy('id','desc')
        ->first();

        return response()->json(['vehicle' => $vehicle, 'center' => $center]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $center = Vehicle::find($id);

        return response()->json(['center' => $center]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}
