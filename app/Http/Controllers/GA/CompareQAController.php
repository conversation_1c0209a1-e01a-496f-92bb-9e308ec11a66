<?php

namespace App\Http\Controllers\GA;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\GA\ComparativeQuotationSetting;
use App\Models\GA\ComparativeQuotation;
use App\Models\GA\ComparativeQuotationApprovalHistory;
use App\Models\GA\ComparativeQuotationSetHistory;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use Auth;

class CompareQAController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $cek_set = $this->checkApproveSetting();
        if (!$cek_set['status']) {
            return response()->json(['success' => $cek_set['status'], 'message' => $cek_set['message']], 500);
        }

        $cq = ComparativeQuotation::with('items.stock','details.supplier','termin','purchaseRequest','user')
            ->where('authorised',0)
            ->where('status','Submit')
            ->get();

        foreach ($cq as $key => $data) {
            $total = 0;
            foreach ($data->items as $item) {
                $total += $item->amount;
            }
            $data->total = $total;
            $data->remark_status = false;

            //check approve
            $sets = ComparativeQuotationSetting::orderBy('seq','asc')->get();
            $data->text_approve = '';
            foreach ($sets as $set) {
                if ($data->text_approve != "") {
                    break;
                }
                $set_history = ComparativeQuotationSetHistory::where('comparative_quotation_id',$data->id)->where('setting_id',$set->id)->count();
                if (!$set_history) {
                    $data->text_approve = 'Waiting approve by '.$set->role;
                }
            }
        }

        return response()->json(['cq' => $cq]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        try {
            $cek_set = $this->checkApproveSetting();
            if (!$cek_set['status']) {
                return response()->json(['success' => $cek_set['status'], 'message' => $cek_set['message']], 500);
            }
            
            $cq = $request->all();

            $message_error = [];
            foreach ($cq as $key => $data) {
                if (isset($data['checkbox']) && $data['checkbox'] != "") {
                    $update = ComparativeQuotation::find($data['id']);
                    //check approve
                    $sets = ComparativeQuotationSetting::orderBy('seq','asc')->get();
                    foreach ($sets as $set) {
                        $set_history = ComparativeQuotationSetHistory::where('comparative_quotation_id',$update->id)->where('setting_id',$set->id)->count();

                        if (!$set_history) {
                            if ($set->role == Auth::user()->role) {
                                $insert_history = ComparativeQuotationSetHistory::create(['setting_id' => $set->id, 'comparative_quotation_id' => $update->id]);
                                break;
                            }else{
                                $message_error[] = $update->code.' - Anda tidak dapat akses untuk approve data ini';
                                break;
                            }
                        }
                    }

                    $jum_set = ComparativeQuotationSetting::count();
                    $jum_set_history = ComparativeQuotationSetHistory::where('comparative_quotation_id',$update->id)->count();

                    if ($jum_set == $jum_set_history) {
                        $cq_setting = ComparativeQuotationSetting::where('role',Auth::user()->role)
                        ->where('status',1)
                        ->first();
                        if ($cq_setting) {
                            $update_cq = ComparativeQuotation::find($data['id']);
                            $update_cq->authorised = 1;
                            $update_cq->authorised_date = Carbon::now();
                            $update_cq->save();

                            $history = new ComparativeQuotationApprovalHistory;
                            $history->comparative_quotation_id = $data['id'];
                            $history->date = Carbon::now();
                            $history->approved_by = Auth::user()->id;
                            if (isset($data['remark'])) {
                                $history->remark = $data['remark'];
                            }
                            $history->comparative_quotation_setting_id = $cq_setting->id;
                            $history->save();
                        }
                    }
                }
            }

            if (count($message_error)) {
                return response()->json(['success' => false, 'message' => 'Anda tidak memiliki akses approve', 'error' => $message_error]); 
            }else{
                return response()->json(['success' => true, 'message' => 'Berhasil Approve data']);
            }
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }

    public function checkApproveSetting()
    {
        $return['status'] = true;
        $return['message'] = "";
        $jum_sett = ComparativeQuotationSetting::count();
        if ($jum_sett) {
            $data_cek = ComparativeQuotationSetting::where('role','Director')->count();
            if ($data_cek) {
                $return['status'] = true;
            }else{
                $return['status'] = false;
                $return['message'] = 'Wajib Set Approval Director di setting approval';
            }
        }else{
            $return['status'] = false;
            $return['message'] = 'Wajib isi approval setting dahulu';
        }

        return $return;
    }
}
