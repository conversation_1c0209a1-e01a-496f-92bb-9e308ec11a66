<?php

namespace App\Http\Controllers\GA;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Fleet\AssetPickup;
use App\Models\Fleet\AssetPickupDetail;
use App\Models\Fleet\AssetPickupDocument;
use App\Models\GA\StockRequest;
use App\Models\Fleet\LocStock;
use App\Models\Fleet\Asset;
use App\Models\GA\StockRequestItems;
use App\Models\Fleet\AssetMasterClass;
use App\Models\Fleet\AssetMasterGroup;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use App\Models\User;
use Carbon\Carbon;
use Auth;

class PickupController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $asset = AssetPickup::with(['purchaseRequest','receiver','handover'])->orderBy('id','desc')->get();

        return response()->json($asset);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
            $asset_ready = Asset::where('status',0)->pluck('pr_id','pr_id');
            $pr = StockRequest::with(['ComparativeQuotation','items.item'])->whereIn('id',$asset_ready)->get();

            $user = User::orderBy('id','desc')->get();
            $id_user = Auth::user()->id;

            $class = AssetMasterClass::orderBy('id','desc')->get();
            $group = AssetMasterGroup::orderBy('id','desc')->get();
            
            return response()->json(['pr' => $pr, 'user' => $user, 'id_user' => $id_user, 'class' => $class, 'group' => $group]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'stock_request_id' => 'required',
                'handover' => 'required',
                'receiver' => 'required',
            ]);
    
            if ($validator->fails()) {
                return response()->json($validator->errors(), 500);
            }

            $id = $request->input('stock_request_id');

            $data = StockRequest::find($id);
            // if ($data->pick_up != 1) {
            //     return response()->json(['success' => false, 'message' => 'Data Error, Purchase request cant pickup'], 500);
            // }
            
            $items = json_decode($request->input('request'), true);
            $checkbox = 0;
            if (is_array($items)) {
                foreach ($items as $key => $item) {
                    if (!is_null($item['checkbox'])) {
                        $checkbox++;
                    }
                }
            }

            if ($data->category_id == 9) {
                $pickup = new AssetPickup;
                $pickup->code = 'PU-'.Carbon::now()->format('ymdHis').rand(10,99);
                $pickup->stock_request_id = $id;
                $pickup->receive_by = $request->input('receiver');
                $pickup->handover_by = $request->input('handover');
                $pickup->date_receive = Carbon::today();
                $pickup->jumlah = $checkbox;
                $pickup->save();

                if (is_array($items)) {
                    foreach ($items as $key => $item) {
                        if (!is_null($item['checkbox'])) {
                            $update_assets = Asset::where('id',$item['id'])->update(['status' => 1, 'pickup_date' => Carbon::today(),'on_hand_id' => $request->input('receiver'), 'in_stock' => 1, 'group_id' => $item['group_id'], 'class_id' => $item['class_id']]);

                            $detail = new AssetPickupDetail;
                            $detail->asset_pickup_id = $pickup->id;
                            $detail->asset_id = $item['id'];
                            $detail->save();

                            //update qty in table lockstock
                            $asset = Asset::find($item['id']);
                            $cek_lockstock = LocStock::where('location_id',$asset->pr->location)
                            ->where('stock_id',$asset->stock_id)
                            ->first();

                            if ($cek_lockstock) {
                                $this->updateLockStock($cek_lockstock->id);
                            }
                        }
                    }
                }

                $files = [];
                if ($request->hasFile('file')) {
                    $document = $request->file('file');
                    for ($i=0; $i < count($document); $i++) { 
                        $file = $document[$i];
                        if ($file->isValid()) {
                            $filename = round(microtime(true) * 1000).'-'.str_replace(' ','-',$file->getClientOriginalName());
                            $file->move(public_path('storage/asset_document'), $filename);                    
                            $keterangan = "";
                            if (isset($request->input('keterangan')[$i])) {
                                $keterangan = $request->input('keterangan')[$i];
                            }
                            $files[] = [
                                'file' => $filename,
                                'keterangan' => $keterangan,
                            ];
                        }
                    }
                }

                foreach ($files as $key => $file) {
                    $document = new AssetPickupDocument;
                    $document->asset_pickup_id = $pickup->id;
                    $document->file = $file['file'];
                    $document->description = $file['keterangan'];
                    $document->save();
                }
            }else{
                $data->pick_up = 3;
                $data->closed = 1;
                $data->save();

                $update_item = StockRequestItems::where('dispatch_id',$id)->update(['completed' => 1]);
            }

            $jumlah_asset_pr = Asset::where('pr_id',$id)->count();
            $jumlah_asset_pr_com = Asset::where('pr_id',$id)->where('status',1)->count();

            if ($jumlah_asset_pr == $jumlah_asset_pr_com) {
                $data->pick_up = 3;
                $data->closed = 1;
                $data->save();

                $update_item = StockRequestItems::where('dispatch_id',$id)->update(['completed' => 1]);
            }

            return response()->json(['success' => true, 'message' => 'Berhasil update pickup']);
        } catch (\Exception $e) {
            Log::info($e);
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $asset = Asset::with('stock')->where('status',0)->where('pr_id',$id)->get();
        foreach ($asset as $ass) {
            $ass->show_qr = false;
            $ass->checkbox = $ass->id;
        }

        $data = StockRequest::find($id); 

        return response()->json(['asset' => $asset, 'data' => $data]);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        try {
            $asset_pickup = AssetPickup::find($id);
            $document = AssetPickupDocument::where('asset_pickup_id',$id)->get();
            foreach ($document as $key => $doc) {
                $doc->link = asset('storage/asset_document/'.$doc->file);
            }
            $data = StockRequest::with(['ComparativeQuotation','items.item'])->find($asset_pickup->stock_request_id);
            // if ($data->pick_up != 1) {
            //     return response()->json(['success' => false, 'message' => 'Data Error, Purchase request cant pickup'], 500);
            // }
            
            $request = Asset::with(['stock','group','class'])->where('pr_id',$asset_pickup->stock_request_id)->get();
            foreach ($request as $key => $req) {
                $req->show_qr = false;
            }
            $user = User::orderBy('id','desc')->get();
            $id_user = Auth::user()->id;
            
            return response()->json(['data' => $data, 'request' => $request, 'user' => $user, 'id_user' => $id_user, 'asset_pickup' => $asset_pickup, 'document' => $document]);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }

    public function updateLockStock($id)
    {
        $lockstock = LocStock::find($id);
        
        $asset_instock = Asset::where('stock_id',$lockstock->stock_id)
        ->where('location_id',$lockstock->location_id)
        ->where('in_stock',0)
        ->count();

        $asset_pending = Asset::where('stock_id',$lockstock->stock_id)
        ->where('location_id',$lockstock->location_id)
        ->whereNull('pickup_date')
        ->count();

        $asset_pickup = Asset::where('stock_id',$lockstock->stock_id)
        ->where('location_id',$lockstock->location_id)
        ->whereNotNull('pickup_date')
        ->count();

        $update = LocStock::where('id',$id)->update(['quantity' => $asset_instock, 'qty_pending' => $asset_pending, 'qty_pickup' => $asset_pickup]);
    }
}
