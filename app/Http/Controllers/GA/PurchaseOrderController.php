<?php

namespace App\Http\Controllers\GA;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\ExportCreatePoPr;
use App\Exports\ExportCreatePr;
use App\Http\Controllers\Controller;
use App\Http\Helper;
use App\Models\GA\ComparativeQuotation;
use App\Models\GA\ComparativeQuotationItem;
use App\Models\GA\StockRequest;
use App\Models\GA\StockRequestItems;
use App\Models\GA\PurchaseOrder;
use App\Models\GA\PurchaseOrderDetail;
use App\Models\GA\PurchaseOrderSetting;
use App\Models\GA\PurchaseOrderApprovalHistory;
use App\Models\GA\PurchaseOrderSetHistory;
use App\Models\GA\Grns;
use App\Models\GA\StockMove;
use App\Models\Fleet\LocStock;
use App\Models\Fleet\Supplier;
use App\Models\Fleet\Location;
use App\Models\Fleet\Shipper;
use App\Models\Fleet\PaymentTerm;
use App\Models\Fleet\ChartMaster;
use App\Models\Fleet\FixedAsset;
use App\Models\Fleet\Asset;
use App\Models\Fleet\Deliver;
use App\Models\Fleet\Department;
use App\Models\Fleet\InvoiceAddress;
use App\Models\Fleet\LogApproval;
use App\Models\Fleet\PurchaseOrder as FleetPurchaseOrder;
use App\Models\Fleet\PurchaseOrderDetail as FleetPurchaseOrderDetail;
use App\Models\Fleet\RequestForm;
use App\Models\Fleet\RequestFormDetail;
use App\Models\Fleet\StockMaster;
use App\Models\Fleet\Tax;
use App\Models\Fleet\Top;
use App\Models\Fleet\TypeItem;
use App\Models\Fleet\Config;
use App\Models\Fleet\MasterTypeAsset;
use App\Models\Fleet\GoodReceivedDetail;
use Carbon\Carbon;
use DB;
use PDF;

class PurchaseOrderController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */

    public function listSupplier(Request $request)
    {
        $supplier = Supplier::select('id', 'name', 'partner_type')
                              ->orderBy('name', 'asc')
                              ->filter($request)
                              ->get();
        return response()->json([
            'data' => $supplier
        ]);
    }

    public function no_pr(Request $request)
    {
        $no_pr = RequestForm::select('id', 'no_pr','created_at')
                ->where('status_approval', 1)
                ->where('verified', 1)
                ->where('status_approval_pr_ga', 1)
                ->where('status_approval_pr_direktur', 1)
                ->orderBy('id', 'desc')
                ->get();

        return response()->json([
            'data' => $no_pr
        ]);
    }

    public function no_cam(Request $request)
    {
        $no_cam = FleetPurchaseOrder::select('id', DB::raw("CONCAT(no_ca, ' ', no_va) AS no_cam"), 'created_at')
                ->where('type', '!=', 'PO')
                ->orderBy('id', 'desc')
                ->get();

        return response()->json([
            'data' => $no_cam
        ]);

    }

    public function no_po(Request $request)
    {
        $no_po = FleetPurchaseOrder::select('id', 'no_po','created_at')
                ->whereNotNull('no_po')
                ->where('approve_po_direktur', 1)
                ->orderBy('id', 'desc')
                ->get();

        foreach ($no_po as $key => $value) {
            $detail = FleetPurchaseOrderDetail::where('purchase_order_id', $value['id_po'])->orderBy('purchase_order_id')->get();
            $statusTotalQty = 0;

            foreach ($detail as $a) {
                if ($a->outstanding > 0) {
                    $statusTotalQty = 1;
                    break;
                } elseif ($a->outstanding === null) {
                    $statusTotalQty = 2;
                    break;
                }
            }

            $value['status_total_qty'] = $statusTotalQty;
        }

        return response()->json([
            'data' => $no_po
        ]);
    }

    public function index(Request $request)
    {
        if($request['date-range'] && $request['po'])
            $permission_name = 'list-po';
        if($request['date-range'] && $request['pr'])
            $permission_name = 'list-payment-request';
        if($request['po-psc'])
            $permission_name = 'approval-po-rsc';
        if($request['approve_po'])
            $permission_name = 'approval-po-direktur';
        if($request['pr-psc'])
            $permission_name = 'approval-payment-request-rsc';
        if($request['approve_pr'] == 1)
            $permission_name = 'approval-payment-request-finance';
        if($request['approve_pr'] == 2)
            $permission_name = 'approval-payment-request-direktur';

        $permission = Helper::getPermissionUser($permission_name);

        $prJasa_direktur = Helper::systemSetting('prJasa_direktur');
        $poAset_direktur = Helper::systemSetting('poAset_direktur');
        $poNonAset_direktur = Helper::systemSetting('poNonAset_direktur');

        if ($request->get('date-range') == 1 || $request->has('start_date')) {
            $data = FleetPurchaseOrder::with(['company', 'department', 'location', 'user'])
            ->where(function($query) use($request, $poNonAset_direktur, $poAset_direktur, $prJasa_direktur){
                if ($request->get('approve_po') == 1) {
                    $query->where('approve_po', 1)
                            ->whereNotNull('approve_po_rsc')
                            ->whereNull('approve_po_direktur')
                            ->whereHas('purchaseOrderDetails', function($query) use ($poNonAset_direktur, $poAset_direktur, $prJasa_direktur) {
                                $query->where(function ($query) use ($poAset_direktur) {
                                    $query->where('type_submission', 1)
                                        ->whereRaw('(qty * unit_price) >= ' . $poAset_direktur);
                                })
                                ->orWhere(function ($query) use ($poNonAset_direktur) {
                                    $query->where('type_submission', 2)
                                            ->whereRaw('(qty * unit_price) >= ' . $poNonAset_direktur);
                                })
                                ->orWhere(function ($query) use ($prJasa_direktur) {
                                    $query->where('type_submission', 3)
                                          ->whereRaw('(qty * unit_price) >= ' . $prJasa_direktur);
                                });
                            });

                    // if (Auth::user()->role == 'Administrator' || Auth::user()->role == 'Direktur') {
                    //     $query->where('approve_po', 1)
                    //     ->whereNotNull('approve_po_rsc')
                    //     ->whereNull('approve_po_direktur')
                    //     ->where(function ($query) use ($asetPO, $nonasetPO, $prJasa_direktur) {
                    //         $query->where(function ($query) use ($asetPO) {
                    //                 $query->where('type_submission', 1)
                    //                     ->where('sub_total', '>=', $asetPO);
                    //             })
                    //             ->orWhere(function ($query) use ($nonasetPO) {
                    //                 $query->where('type_submission', 2)
                    //                     ->where('sub_total', '>=', $nonasetPO);
                    //             })
                    //             ->orWhere(function ($query) use ($prJasa_direktur) {
                    //                 $query->where('type_submission', 3)
                    //                     ->where('sub_total', '>=', $prJasa_direktur);
                    //             });
                    //     });
                    // }else{
                    //     // $query->where('department_id', $dept_user->id)->where('approve_po', 1)->whereNull('approve_po_direktur');
                    //     $query->where('approve_po', 1)
                    //     ->whereNotNull('approve_po_rsc')
                    //     ->whereNull('approve_po_direktur')
                    //     ->where(function ($query) use ($asetPO, $nonasetPO, $prJasa_direktur) {
                    //         $query->where(function ($query) use ($asetPO) {
                    //                 $query->where('type_submission', 1)
                    //                     ->where('sub_total', '>=', $asetPO);
                    //             })
                    //             ->orWhere(function ($query) use ($nonasetPO) {
                    //                 $query->where('type_submission', 2)
                    //                     ->where('sub_total', '>=', $nonasetPO);
                    //             })
                    //             ->orWhere(function ($query) use ($prJasa_direktur) {
                    //                 $query->where('type_submission', 3)
                    //                     ->where('sub_total', '>=', $prJasa_direktur);
                    //             });
                    //     });
                    // }
                }else if ($request->get('approve_po') == 2) {
                    $query->where('approve_po', 2);
                }else if ($request->get('approve_pr') == 1) {
                    $query->where('approve_pr', 1);
                }else if ($request->get('approve_pr') == 2) {
                    $query->where('approve_pr', 2)->whereNull('approve_pr_direktur');
                }else if ($request->get('approve_pr') == 3) {
                    $query->where('approve_pr', 3);
                }else if($request->get('po-psc')){
                    $query->where('type', 'PO')->whereNull('approve_po');
                }else if($request->get('pr-psc')){
                    $query->where('type', '!=', 'PO')->whereNull('approve_pr');
                }else if ($request->get('po') == 1){
                     $query->where('type','=', 'PO');
                }else if($request->get('pr') == 1){
                    $query->where('type', '!=', 'PO');
                }

                if ($request->get('status') == 1) {
                    $query->where('approve_po', 1);
                }elseif ($request->get('status') == 2) {
                    $query->where('approve_po_direktur', 1);
                }elseif ($request->get('status') == 3) {
                    $query->where('approve_po_direktur', 2);
                }elseif ($request->get('status') == 4) {
                    $query->where('approve_po', 2);
                }elseif ($request->get('status') == 5) {
                    $query->where('approve_pr', 1)->where('approve_pr', 1);
                }elseif ($request->get('status') == 6) {
                    $query->where('approve_pr_finance', 1)->where('approve_pr', 2);
                }elseif ($request->get('status') == 7) {
                    $query->where('approve_pr_finance', 2);
                }elseif ($request->get('status') == 8) {
                    $query->where('approve_pr_direktur', 1)->where('approve_pr', 3);
                }elseif ($request->get('status') == 9) {
                    $query->where('approve_pr_direktur', 2);
                }elseif ($request->get('status') == 10) {
                    $query->where('approve_pr', '>=', 2);
                }

                if ($request->get('company')) {
                    $query->where('company_id', $request->get('company'));
                }
                if ($request->get('location')) {
                    $query->where('location_id', $request->get('location'));
                }
                if ($request->get('department')) {
                    $query->where('department_id', $request->get('department'));
                }
                if ($request->get('type_submission')) {
                    $query->where('type_submission', $request->get('type_submission'));
                }
                if ($request->get('no_po')) {
                    $query->where('id', $request->get('no_po'));
                }
                if ($request->get('no_pr')) {
                    $query->where('id', $request->get('no_pr'));
                }
                if ($request->get('supplier_id')) {
                    $query->where('supplier_id', $request->get('supplier_id'));
                }
            })
            ->when(($request->get('start_date') ?? false) || ($request->get('to_date') ?? false) , function($query) use ($request) {
                $query->whereDate('created_at', '>=', $request->start_date)
                        ->whereDate('created_at', '<=', $request->to_date);
            })
            ->orderBy('id', 'desc');

            if(count($permission['company']) > 0 && !in_array('all', $permission['company'])) {
                $data = $data->whereIn('company_id', $permission['company']);
            }else if(count($permission['company']) == 0)
                $data = $data->where('company_id', Auth::user()->company->id ?? 0);

            if(count($permission['department']) > 0 && !in_array('all', $permission['department']))
                $data = $data->whereHas('department', function($query) use ($permission) {
                    $query->whereIn('name', $permission['department']);
                });
            else if(count($permission['department']) == 0) {
                $department_user = Auth::user()->department;
                if(strpos($department_user->name ?? '', '(Corporate)')) {
                    $data = $data->whereHas('department', function($query) use ($department_user) {
                        $query->where('name', 'ilike', $department_user->name ?? '');
                    });
                } else
                    $data = $data->where('department_id', Auth::user()->department->id ?? 0);
            }

            if(count($permission['location']) > 0 && !in_array('all', $permission['location']))
                $data = $data->whereHas('location', function($query) use ($permission) {
                    $query->whereIn('name', $permission['location']);
                });
            else if(count($permission['location']) == 0)
                $data = $data->where('location_id', Auth::user()->location->id ?? 0);

            $data = $data->get();
        }else{
            $data = FleetPurchaseOrder::with(['company', 'department', 'location', 'user'])
            ->where(function($query) use($request, $prJasa_direktur, $poAset_direktur, $poNonAset_direktur){
                if ($request->get('approve_po') == 1) {
                    $query->where('approve_po', 1)
                    ->whereNotNull('approve_po_rsc')
                    ->whereNull('approve_po_direktur')
                    ->whereHas('purchaseOrderDetails', function($query) use ($poNonAset_direktur, $poAset_direktur, $prJasa_direktur) {
                        $query->where(function ($query) use ($poAset_direktur) {
                            $query->where('type_submission', 1)
                                ->whereRaw('(qty * unit_price) >= ' . $poAset_direktur);
                        })
                        ->orWhere(function ($query) use ($poNonAset_direktur) {
                            $query->where('type_submission', 2)
                                    ->whereRaw('(qty * unit_price) >= ' . $poNonAset_direktur);
                        })
                        ->orWhere(function ($query) use ($prJasa_direktur) {
                            $query->where('type_submission', 3)
                                  ->whereRaw('(qty * unit_price) >= ' . $prJasa_direktur);
                        });
                    });

                    // if (Auth::user()->role == 'Administrator' || Auth::user()->role == 'Direktur') {
                    //     // $query->where('approve_po', 1)->whereNotNull('no_po')->whereNull('approve_po_direktur')->where('sub_total', '>=', $asetPO)->orWhere('sub_total', '>=', $nonasetPO);
                    //     $query->where('approve_po', 1)
                    //     ->whereNotNull('approve_po_rsc')
                    //     ->whereNull('approve_po_direktur')
                    //     ->where(function ($query) use ($asetPO, $nonasetPO, $prJasa_direktur) {
                    //         $query->where(function ($query) use ($asetPO) {
                    //                 $query->where('type_submission', 1)
                    //                     ->where('sub_total', '>=', $asetPO);
                    //             })
                    //             ->orWhere(function ($query) use ($nonasetPO) {
                    //                 $query->where('type_submission', 2)
                    //                     ->where('sub_total', '>=', $nonasetPO);
                    //             })
                    //             ->orWhere(function ($query) use ($prJasa_direktur) {
                    //                 $query->where('type_submission', 3)
                    //                     ->where('sub_total', '>=', $prJasa_direktur);
                    //             });
                    //     });
                    // }else{
                    //     // $query->where('department_id', $dept_user->id)->where('approve_po', 1)->whereNull('approve_po_direktur')->where('sub_total', '>=', $value);
                    //     $query->where('approve_po', 1)
                    //     ->whereNotNull('approve_po_rsc')
                    //     ->whereNull('approve_po_direktur')
                    //     ->where(function ($query) use ($asetPO, $nonasetPO, $prJasa_direktur) {
                    //         $query->where(function ($query) use ($asetPO) {
                    //                 $query->where('type_submission', 1)
                    //                     ->where('sub_total', '>=', $asetPO);
                    //             })
                    //             ->orWhere(function ($query) use ($nonasetPO) {
                    //                 $query->where('type_submission', 2)
                    //                     ->where('sub_total', '>=', $nonasetPO);
                    //             })
                    //             ->orWhere(function ($query) use ($prJasa_direktur) {
                    //                 $query->where('type_submission', 3)
                    //                     ->where('sub_total', '>=', $prJasa_direktur);
                    //             });
                    //     });
                    // }
                }else if ($request->get('approve_po') == 2) {
                    $query->where('approve_po', 2);
                }else if ($request->get('approve_pr') == 1) {
                    $query->where('approve_pr', 1);
                }else if ($request->get('approve_pr') == 2) {
                    $query->where('approve_pr', 2)->whereNull('approve_pr_direktur');
                }else if ($request->get('approve_pr') == 3) {
                    $query->where('approve_pr', 3);
                }else if($request->get('po-psc')){
                    $query->where('type', 'PO')->whereNull('approve_po');
                }else if($request->get('pr-psc')){
                    $query->where('type', '!=', 'PO')->whereNull('approve_pr');
                }else if($request->get('po') == 1){
                    $query->where('type', 'PO');
                }else if($request->get('pr') == 1){
                    $query->where('type', '!=', 'PO');
                }

                if ($request->get('status') == 1) {
                    $query->where('approve_po', 1);
                }elseif ($request->get('status') == 2) {
                    $query->where('approve_po_direktur', 1);
                }elseif ($request->get('status') == 3) {
                    $query->where('approve_po_direktur', 2);
                }elseif ($request->get('status') == 4) {
                    $query->where('approve_po', 2);
                }elseif ($request->get('status') == 5) {
                    $query->where('approve_pr', 1)->where('approve_pr', 1);
                }elseif ($request->get('status') == 6) {
                    $query->where('approve_pr_finance', 1)->where('approve_pr', 2);
                }elseif ($request->get('status') == 7) {
                    $query->where('approve_pr_finance', 2);
                }elseif ($request->get('status') == 8) {
                    $query->where('approve_pr_direktur', 1)->where('approve_pr', 3);
                }elseif ($request->get('status') == 9) {
                    $query->where('approve_pr_direktur', 2);
                }elseif ($request->get('status') == 10) {
                    $query->where('approve_pr', '>=', 2);
                }

                if ($request->get('company')) {
                    $query->where('company_id', $request->get('company'));
                }
                if ($request->get('location')) {
                    $query->where('location_id', $request->get('location'));
                }
                if ($request->get('department')) {
                    $query->where('department_id', $request->get('department'));
                }
                if ($request->get('type_submission')) {
                    $query->where('type_submission', $request->get('type_submission'));
                }
                if ($request->get('no_po')) {
                    $query->where('id', $request->get('no_po'));
                }
                if ($request->get('no_pr')) {
                    $query->where('id', $request->get('no_pr'));
                }
                if ($request->get('supplier_id')) {
                    $query->where('supplier_id', $request->get('supplier_id'));
                }
            })
            ->orderBy('id', 'desc');

            if(count($permission['company']) > 0 && !in_array('all', $permission['company'])) {
                $data = $data->whereIn('company_id', $permission['company']);
            }else if(count($permission['company']) == 0)
                $data = $data->where('company_id', Auth::user()->company->id ?? 0);

            if(count($permission['department']) > 0 && !in_array('all', $permission['department']))
                $data = $data->whereHas('department', function($query) use ($permission) {
                    $query->whereIn('name', $permission['department']);
                });
            else if(count($permission['department']) == 0) {
                $department_user = Auth::user()->department;
                if(strpos($department_user->name ?? '', '(Corporate)')) {
                    $data = $data->whereHas('department', function($query) use ($department_user) {
                        $query->where('name', 'ilike', $department_user->name ?? '');
                    });
                } else
                    $data = $data->where('department_id', Auth::user()->department->id ?? 0);
            }

            if(count($permission['location']) > 0 && !in_array('all', $permission['location']))
                $data = $data->whereHas('location', function($query) use ($permission) {
                    $query->whereIn('name', $permission['location']);
                });
            else if(count($permission['location']) == 0)
                $data = $data->where('location_id', Auth::user()->location->id ?? 0);

            $data = $data->get();
        }

        // dd($data);


        foreach ($data as $key => $value) {
            $value['company_name'] = $value->company->name ?? null;
            $value['department_name'] = $value->department->name ?? null;
            $value['location_name'] = $value->location->name ?? null;
            $value['supplier_name'] = $value->supplier->name ?? null;
            $value['section_name'] = $value->section->name ?? null;
            $value['uid'] = Auth::user()->id;
            $value['type_submission'] = $value->typeAsset->name ?? null;

            $items = $value->items;
            foreach ($items as $ckey => $cvalue) {
                $value['items'][$ckey] = $cvalue->typeItem->name ?? null;
            }

            if ($value->type == 'Transfer'){
                $value['no_pr'] = $value->no_tf;
            }else if($value->type == 'Cash Advance'){
                $value['no_pr'] = $value->no_ca;
            }else if($value->type == 'Virtual Account'){
                $value['no_pr'] = $value->no_va;
            }

            if ($value->approve_po == 1 && $value->approve_po_direktur == null) {
                $value['status'] = 'Approve PO';
            }elseif ($value->approve_po == 2 && $value->approve_po_direktur == 1 && $value->no_gr == null) {
                $value['status'] = 'Approve PO Direktur';
            }elseif ($value->approve_po_direktur == 2) {
                $value['status'] = 'Reject PO Direktur';
            }elseif ($value->approve_pr == 1 && $value->approve_pr_finance == null) {
                $value['status'] = 'Approve PR RSC';
            }elseif($value->approve_po == 4){
                $value['status'] = 'Cancel PO RSC';
            }elseif($value->approve_pr == 4){
                $value['status'] = 'Cancel PR RSC';
            }elseif ($value->approve_pr == 2 && $value->approve_pr_finance == 1) {
                $value['status'] = 'Approve PR Finance';
            }elseif ($value->approve_pr_finance == 2) {
                $value['status'] = 'Reject PR Finance';
            }elseif ($value->approve_pr == 3 && $value->approve_pr_direktur == 1 && $value->no_gr == null) {
                $value['status'] = 'Approve PR Direktur';
            }elseif ($value->approve_pr_direktur == 2) {
                $value['status'] = 'Reject PR Direktur';
            }elseif ($value->approve_po == null) {
                $value['status'] = 'Waiting Approve PO RSC';
            }elseif ($value->approve_pr == null) {
                $value['status'] = 'Waiting Approve PR RSC';
            }else{
                $value['status'] = 'Proses GR';
            }
        }
        $data = array_values($data->toArray());

        return response()->json($data);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $cek_set = $this->checkApproveSetting();
        if (!$cek_set['status']) {
            return response()->json(['success' => $cek_set['status'], 'message' => $cek_set['message']], 500);
        }

        $location = Location::all();
        $shipper = Shipper::all();
        $payment_term = PaymentTerm::all();
        $req = 'REQ-'.Carbon::now()->format('ymdHis').rand(10,99);

        return response()->json(['location' => $location, 'shipper' => $shipper, 'payment_term' => $payment_term, 'req' => $req]);
    }

    public function createPo($id,$supplier)
    {
        $cek_set = $this->checkApproveSetting();
        if (!$cek_set['status']) {
            return response()->json(['success' => $cek_set['status'], 'message' => $cek_set['message']], 500);
        }

        $cq = ComparativeQuotation::with('items.stock','details.supplier','termin','purchaseRequest')->findOrFail($id);
        $cqi = ComparativeQuotationItem::with('stock','supplier','ComparativeQuotation.termin','ComparativeQuotation.purchaseRequest','detail.supplier')->where('comparative_quotation_id',$id)->where('supplier_id',$supplier)->get();
        $location = Location::all();
        $shipper = Shipper::all();
        $payment_term = PaymentTerm::all();
        $req = 'REQ-'.Carbon::now()->format('ymdHis').rand(10,99);
        $chart_master = ChartMaster::orderBy('accountcode','asc')->get();
        $fixed_asset = FixedAsset::orderBy('id','asc')->get();

        return response()->json(['location' => $location, 'shipper' => $shipper, 'payment_term' => $payment_term, 'req' => $req, 'cqi' => $cqi, 'chart_master' => $chart_master, 'fixed_asset' => $fixed_asset, 'cq' => $cq]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request, $id, $supplier_id)
    {
        try {
            $cek_set = $this->checkApproveSetting();
            if (!$cek_set['status']) {
                return response()->json(['success' => $cek_set['status'], 'message' => $cek_set['message']], 500);
            }

            $validator = Validator::make($request->all(), [
                'exRate' => 'required',
                'requisition' => 'required',
                'location' => 'required',
                'shipper' => 'required',
                // 'comment' => 'required',
                // 'statusComment' => 'required',
                'deliveryDate' => 'required',
                'payment_term' => 'required',
            ]);

            if ($validator->fails()) {
                return response()->json($validator->errors(), 500);
            }

            $cq = ComparativeQuotation::with('items.stock','details.supplier','termin','purchaseRequest')->findOrFail($id);
            $location = Location::find($request->input('location'));
            $supplier = Supplier::find($supplier_id);

            $po = new PurchaseOrder;
            $po->supplier_no = $supplier->id;
            $po->comments = 'created';
            $po->ord_date = Carbon::now();
            $po->rate = $request->input('exRate');
            $po->allow_print = 1;
            $po->initiator = Auth::user()->id;
            $po->requisition_no = $request->input('requisition');
            $po->into_stock_location = $request->input('location');
            if ($location) {
                $po->tel = $location->tel;
                $po->contact = $location->contact;
            }
            $po->supp_tel = $supplier->telephone;
            $po->supplier_contact = $supplier->pic_phone;
            $po->version = 1;
            $po->revised = Carbon::now();
            $po->real_order_no = 0;
            $po->delivery_by = $request->input('shipper');
            $po->delivery_date = $request->input('deliveryDate');
            $po->status = 'created';
            $po->stat_comment = Carbon::now()->format('m/d/Y').' - PO Created by '.Auth::user()->first_name;
            $po->payment_terms = $request->input('payment_term');
            $po->port = $supplier->port;
            $po->comparative_quotation_id = $id;
            $po->code = 'PO-'.Carbon::now()->format('ymdHis').rand(10,99);
            $po->authorise = 0;
            if($po->save()){
                if (is_array($request->input('items'))) {
                    $items = $request->input('items');
                    for ($i=0; $i < count($request->input('items')); $i++) {
                        $detail = new PurchaseOrderDetail;
                        $detail->po_id = $po->id;
                        $detail->item_code = $items[$i]['stock_id'];
                        $detail->delivery_date = $request->input('deliveryDate');
                        $detail->item_description = $items[$i]['stock']['description'];
                        $detail->gl_code = $items[$i]['GLCode'];
                        $detail->qty_invoiced = $items[$i]['quantity'];
                        $detail->unit_price = $items[$i]['price'];
                        $detail->act_price = $items[$i]['price'];
                        $detail->std_cost_unit = $items[$i]['price'];
                        $detail->quantity_ord = $items[$i]['quantity'];
                        $detail->quantity_recd = 0;
                        $detail->shipt_ref = 0;
                        $detail->job_ref = 0;
                        $detail->completed = 0;
                        $detail->suppliers_unit = $items[$i]['uom'];
                        $detail->suppliers_part_no = 0;
                        if ($items[$i]['AssetID'] != 'Not an Asset') {
                            $detail->asset_id = $items[$i]['AssetID'];
                        }
                        $detail->save();

                        $cqi_update = ComparativeQuotationItem::find($items[$i]['id']);
                        $cqi_update->used = 1;
                        $cqi_update->save();
                    }
                }
            }

            return response()->json(['success' => true, 'message' => 'Berhasil create data']);
        } catch (\Exception $e) {
            Log::info($e);
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $data = FleetPurchaseOrder::find($id);

        $data['company_name'] = $data->company->name ?? null;
        $data['supplier_name'] = $data->supplier->name ?? null;
        $data['department_name'] = $data->department->name ?? null;
        // $data['type_submission'] = Helper::typeSubmission($data['type_submission']);
        $data['section_name'] = $data->section->name ?? null;
        $data['location_name'] = $data->location->name ?? null;
        $data['fund_keepers'] = $data->userPenanggungJawab ?? null;
        $data['fund_keeper_id'] = $data->user_pjd_id ?? null;

        $items = FleetPurchaseOrderDetail::with(['typeItem', 'requestFormDetail'])
        ->where('purchase_order_id', $id)
        ->orderBy('id', 'desc')
        ->get();

        $totalAmount = 0;

        foreach ($items as $key => $value) {
            $retur = 0;
            foreach($value->purchaseOrder->GoodReceiveDetail as $grDetail)
            {
                $retur = $grDetail->qty_retur ?? 0;
            }

            $value['qty_in'] = 0;
            $value['qty_gr'] = $value['qty_gr'] - $retur?? 0;
            $value['outstanding'] = $value['qty'] - $value['qty_gr'] - $value['qty_in'] + $retur;
            $value['nopol'] = $value->requestFormDetail->requestForm->nopol ?? null;
            $value['qty_request_buy'] = $value->requestFormDetail->qty_request_buy ?? null;
            $value['qty_create'] = $value->requestFormDetail->qty_create ?? null;
            $value['qty_retur'] = $retur;

            $totalAmount += $value['amount'];
        }

        $data['total_all'] = $totalAmount;

        $invoice_address = InvoiceAddress::orderBy('id', 'desc')->get();
        $deliver = Deliver::orderBy('id', 'desc')->get();
        $top = Top::orderBy('id', 'desc')->get();
        $supplier = Supplier::orderBy('id', 'desc')->get();
        $tax = Tax::orderBy('id', 'desc')->get();

        return response()->json(['data' => $data,
                                 'items' => $items,
                                 'invoice_address' => $invoice_address,
                                 'deliver' => $deliver,
                                 'top' => $top,
                                 'supplier' => $supplier,
                                 'tax' => $tax]);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    public function showPo($id)
    {
        $cek_set = $this->checkApproveSetting();
        if (!$cek_set['status']) {
            return response()->json(['success' => $cek_set['status'], 'message' => $cek_set['message']], 500);
        }

        $po = PurchaseOrder::with(['ComparativeQuotation','shipper','user','supplier','location','PaymentTerm', 'detail.stock', 'detail.chartMaster', 'detail.fixedAsset'])->findOrFail($id);

        return response()->json(['po' => $po]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request)
    {
        try {
            $setting = PurchaseOrderSetting::where('role',Auth::user()->role)->first();
            if (!$setting) {
                return response()->json(['success' => false, 'message' => 'You are not authorise for this menu'], 500);
            }

            $message_error = [];

            $data_po = $request->all();
            foreach ($data_po as $key => $datas) {
                if (isset($datas['checkbox'])) {
                    $id = $datas['checkbox'];
                    $update = PurchaseOrder::find($id);

                    //check approve
                    $sets = PurchaseOrderSetting::orderBy('seq','asc')->get();
                    foreach ($sets as $set) {
                        $set_history = PurchaseOrderSetHistory::where('purchase_order_id',$update->id)->where('setting_id',$set->id)->count();

                        if (!$set_history) {
                            if ($set->role == Auth::user()->role) {
                                $insert_history = PurchaseOrderSetHistory::create(['setting_id' => $set->id, 'purchase_order_id' => $update->id]);
                                break;
                            }else{
                                $message_error[] = $update->code.' - Anda tidak dapat akses untuk approve data ini';
                                break;
                            }
                        }
                    }

                    $jum_set = PurchaseOrderSetting::count();
                    $jum_set_history = PurchaseOrderSetHistory::where('purchase_order_id',$update->id)->count();

                    if ($jum_set == $jum_set_history) {
                        $update = PurchaseOrder::find($id);
                        $update->authorise = 1;
                        $update->authorise_date = Carbon::now();
                        $update->save();

                        $history = new PurchaseOrderApprovalHistory;
                        $history->purchase_order_id = $id;
                        $history->date = Carbon::now();
                        $history->approved_by = Auth::user()->id;
                        if (isset($datas['remark'])) {
                            $history->remark = $datas['remark'];
                        }
                        $history->purchase_order_setting_id = $setting->id;
                        $history->save();
                    }
                }
            }

            if (count($message_error)) {
                return response()->json(['success' => false, 'message' => 'Anda tidak memiliki akses approve', 'error' => $message_error]);
            }else{
                return response()->json(['success' => true, 'message' => 'Berhasil Approve data']);
            }
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }

    public function receiveList()
    {
        $setting = PurchaseOrderSetting::where('role',Auth::user()->role)->first();
        if (!$setting) {
            return response()->json(['success' => false, 'message' => 'You are not authorise for this menu'], 500);
        }

        $receive = PurchaseOrder::with(['ComparativeQuotation.purchaseRequest','shipper','user','supplier','location','PaymentTerm', 'detail.stock', 'detail.chartMaster', 'detail.fixedAsset'])
        ->withCount([
            'detail AS total' => function ($query) {
                $query->select(DB::raw("sum(unit_price * qty_invoiced)"));
            }
        ])
        ->orderBy('id','desc')
        ->get();

        return response()->json(['receive' => $receive]);
    }

    public function receiveShow($id)
    {
        $setting = PurchaseOrderSetting::where('role',Auth::user()->role)->first();
        if (!$setting) {
            return response()->json(['success' => false, 'message' => 'You are not authorise for this menu'], 500);
        }

        $receive = PurchaseOrder::with(['ComparativeQuotation.purchaseRequest','ComparativeQuotation.details','shipper','user','supplier','location','PaymentTerm', 'detail.stock', 'detail.chartMaster', 'detail.fixedAsset'])
        ->withCount([
            'detail AS total' => function ($query) {
                $query->select(DB::raw("sum(unit_price * qty_invoiced)"));
            }
        ])
        ->find($id);

        return response()->json(['receive' => $receive]);
    }

    public function receiveStore(Request $request, $id)
    {
        try{
            $setting = PurchaseOrderSetting::where('role',Auth::user()->role)->first();
            if (!$setting) {
                return response()->json(['success' => false, 'message' => 'You are not authorise for this menu'], 500);
            }

            $details = $request->input('detail');
            if (is_array($details)) {
                foreach ($details as $key => $detail) {
                    if (!isset($detail['receive_qty'])) {
                        $detail['receive_qty'] = 0;
                    }

                    //update detai po qty received
                    $podetail = PurchaseOrderDetail::find($detail['id']);

                    if ($detail['completed']) {
                        $detail['receive_qty'] = $podetail->quantity_ord - $podetail->quantity_recd;
                        $podetail->return_qty = 0;
                    }else{
                        $total_recieve_return = $detail['receive_qty'] + $detail['return_qty'] + $podetail->quantity_recd;
                        if ($total_recieve_return > $podetail->quantity_ord) {
                            return response()->json(['success' => false, 'message' => 'quantity receive dan return tidak sesuai'], 500);
                        }
                        $podetail->return_qty = $detail['return_qty'];
                    }

                    $total_received = $podetail->quantity_recd + $detail['receive_qty'];

                    if ($detail['completed'] || $total_received == $detail['qty_invoiced']) {
                        $podetail->completed = 1;
                        $podetail->quantity_recd = $detail['qty_invoiced'];
                    }else{
                        $podetail->quantity_recd = $podetail->quantity_recd + $detail['receive_qty'];
                    }
                    $podetail->save();


                    //add to table grns
                    $count_grns = Grns::where('po_detail_item', $detail['id'])->where('item_code', $detail['item_code'])->count();

                    $grns = new Grns;
                    $grns->grn_batch = $count_grns + 1;
                    $grns->item_code = $detail['item_code'];
                    $grns->delivery_date = $request->input('date_goods');
                    $grns->item_description = $detail['item_description'];
                    $grns->qty_recd = $detail['receive_qty'];
                    $grns->quantity_inv = $detail['qty_invoiced'];
                    $grns->supplier_id = $request->input('supplier_no');
                    $grns->std_cost_unit = $detail['std_cost_unit'];
                    $grns->save();

                    //update qty in table lockstock
                    $cek_lockstock = LocStock::where('location_id',$request->input('into_stock_location'))
                    ->where('stock_id',$detail['item_code'])
                    ->first();

                    if (!$cek_lockstock) {
                        $add_lockstock = new LocStock;
                        $add_lockstock->location_id = $request->input('into_stock_location');
                        $add_lockstock->stock_id = $detail['item_code'];
                        $add_lockstock->bin = '-';
                        $add_lockstock->save();

                        $lockstock = LocStock::find($add_lockstock->id);
                    }else{
                        $lockstock = LocStock::find($cek_lockstock->id);
                    }
                    $lockstock->qty_pending = $lockstock->qty_pending + $detail['receive_qty'];
                    $lockstock->save();

                    //add histroy stock move
                    $stockmove = new StockMove;
                    $stockmove->stock_id = $detail['item_code'];
                    $stockmove->type = 25;
                    $stockmove->trans_no = $grns->grn_batch;
                    $stockmove->loc_code = $request->input('into_stock_location');
                    $stockmove->tran_date = $request->input('date_goods');
                    $stockmove->debtor_no = $request->input('supplier_no');
                    $stockmove->branch_code = '0';
                    $stockmove->price = $detail['unit_price'] / $request->input('rate');
                    $stockmove->prd = 0;
                    $stockmove->reference = $request->input('supplier_no').' ('.$request->input('supplier')['name'].') - '.$id;
                    $stockmove->qty = $detail['receive_qty'];
                    $stockmove->discount_percent = 0;
                    $stockmove->standard_cost = $detail['unit_price'];
                    $stockmove->new_qoh = $lockstock->quantity;
                    $stockmove->save();

                    //add to table asset
                    // $purchase_order = PurchaseOrder::find($id);
                    // if (is_numeric($detail['receive_qty'])) {
                    //     for ($i=0; $i < $detail['receive_qty']; $i++) {
                    //         $stock_master = StockMaster::find($detail['item_code']);
                    //         if ($stock_master && $stock_master->category_id == 'CT001') {
                    //             $asset = new Asset;
                    //             $asset->code = 'LJR-'.Carbon::now()->format('ymdHis').rand(10,99);
                    //             $asset->created_asset = Carbon::now();
                    //             $asset->stock_id = $detail['item_code'];
                    //             $asset->created_by = Auth::user()->id;
                    //             $asset->requester = $purchase_order->ComparativeQuotation->requester;
                    //             $asset->save();
                    //         }
                    //     }
                    // }
                }
            }

            $detail_count_complete = PurchaseOrderDetail::where('po_id',$id)->where('completed',1)->count();
            $detail_count = PurchaseOrderDetail::where('po_id',$id)->count();
            if ($detail_count == $detail_count_complete) {
                $update_completed = PurchaseOrder::find($id);
                $update_completed->status = 'Completed';
                $update_completed->stat_comment = Carbon::now()->format('m/d/Y').' - Order Completed on entry of GRN';
                $update_completed->save();

                //get CQ
                if (isset($update_completed->ComparativeQuotation->id)) {
                    $cq_id = $update_completed->ComparativeQuotation->id;

                    $comparative = ComparativeQuotation::find($cq_id);
                    if ($comparative && $comparative->purchaseRequest->category_id != 9) {
                        $po_cq_all = PurchaseOrder::where('comparative_quotation_id',$comparative->id)->count();
                        $po_cq_completed = PurchaseOrder::where('comparative_quotation_id',$comparative->id)->where('status','Completed')->count();

                        // if ($po_cq_all > 0) {
                        //     //update purchase request ready pickup
                        //     $purchase_update = StockRequest::find($comparative->stock_request_id);
                        //     $purchase_update->pick_up = 1;
                        //     $purchase_update->save();
                        // }
                        // elseif ($po_cq_all > 0) {
                        //     $purchase_update = StockRequest::find($comparative->stock_request_id);
                        //     $purchase_update->pick_up = 2;
                        //     $purchase_update->save();
                        // }
                    }
                }
            }

            // Log::info($detail_count.' === '.$detail_count_complete);
            return response()->json(['success' => true, 'message' => 'Berhasil Update data']);
        } catch (\Exception $e) {
            Log::info($e);
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    public function receiveLabel($id)
    {
        try {
            $setting = PurchaseOrderSetting::where('role',Auth::user()->role)->first();
            if (!$setting) {
                return response()->json(['success' => false, 'message' => 'You are not authorise for this menu'], 500);
            }

            $receive = PurchaseOrder::with(['ComparativeQuotation.purchaseRequest','ComparativeQuotation.details','shipper','user','supplier','location','PaymentTerm', 'detail.stock', 'detail.chartMaster', 'detail.fixedAsset'])
            ->withCount([
                'detail AS total' => function ($query) {
                    $query->select(DB::raw("sum(unit_price * qty_invoiced)"));
                }
            ])
            ->find($id);

            if ($receive->ComparativeQuotation->purchaseRequest->category_id != 9) {
                return response()->json(['success' => false, 'message' => 'Data Error'], 500);
            }

            $assets = [];
            $no = 0;

            $poassets = Asset::where('po_id',$id)->get();
            foreach ($poassets as $key => $poasset) {
                $assets[$no]['id'] = $poasset->stock_id;
                $assets[$no]['label'] = $poasset->code;
                $assets[$no]['description'] = $poasset->stock->description;
                $assets[$no]['long_description'] = $poasset->stock->long_description;
                $assets[$no]['units'] = $poasset->stock->units;
                $assets[$no]['saved'] = 1;
                $assets[$no]['show_qr'] = false;
                if ($poasset->pickup_date != "") {
                    $assets[$no]['pickup'] = true;
                }
                $no++;
            }

            $details = PurchaseOrderDetail::where('po_id',$id)->get();
            $poassets_count = Asset::where('po_id',$id)->count();
            foreach ($details as $key => $detail) {
                if ($detail->stock->category_id == 'CT001') {
                    for ($i=0; $i < ($detail->quantity_recd - $poassets_count); $i++) {
                        $assets[$no]['id'] = $detail->item_code;
                        $assets[$no]['label'] = 'LJR-'.Carbon::now()->format('ymdHis').rand(10,99);
                        $assets[$no]['description'] = $detail->stock->description;
                        $assets[$no]['long_description'] = $detail->stock->long_description;
                        $assets[$no]['units'] = $detail->stock->units;
                        $assets[$no]['saved'] = 0;
                        $assets[$no]['show_qr'] = false;
                        $assets[$no]['pickup'] = false;
                        $no++;
                    }
                }
            }

            return response()->json(['receive' => $receive, 'assets' => $assets]);
        } catch (\Exception $e) {
            Log::info($e);
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    public function receiveLabelStore(Request $request, $id)
    {
        try {
            $setting = PurchaseOrderSetting::where('role',Auth::user()->role)->first();
            if (!$setting) {
                return response()->json(['success' => false, 'message' => 'You are not authorise for this menu'], 500);
            }

            $purchase_order = PurchaseOrder::find($id);
            $items = $request->input('items');

            if (is_array($items)) {
                $assetdel = Asset::where('po_id',$id)->delete();
                foreach ($items as $key => $item) {
                    $asset = new Asset;
                    $asset->po_id = $purchase_order->id;
                    $asset->pr_id = $purchase_order->ComparativeQuotation->purchaseRequest->id;
                    $asset->code = $item['label'];
                    $asset->created_asset = Carbon::now();
                    $asset->stock_id = $item['id'];
                    $asset->created_by = Auth::user()->id;
                    $asset->requester = $purchase_order->ComparativeQuotation->requester;
                    $asset->in_stock = 1;
                    $asset->save();
                }
            }

            //get CQ
            if (isset($purchase_order->ComparativeQuotation->id)) {
                $cq_id = $purchase_order->ComparativeQuotation->id;

                $comparative = ComparativeQuotation::find($cq_id);
                if ($comparative && $comparative->purchaseRequest->category_id == 9) {
                    $po_cq_asset_all = PurchaseOrder::where('comparative_quotation_id',$comparative->id)->pluck('id','id');
                    $total_asset_po = PurchaseOrderDetail::whereIn('po_id',$po_cq_asset_all)->sum('qty_invoiced');
                    $total_asset_label = Asset::whereIn('po_id',$po_cq_asset_all)->count();

                    if ($total_asset_label > 0 && $total_asset_po == $total_asset_label) {
                        //update purchase request ready pickup
                        $purchase_update = StockRequest::find($comparative->stock_request_id);
                        $purchase_update->pick_up = 1;
                        $purchase_update->save();
                    }elseif ($total_asset_label > 0) {
                        $purchase_update = StockRequest::find($comparative->stock_request_id);
                        $purchase_update->pick_up = 2;
                        $purchase_update->save();
                    }
                }
            }

            return response()->json(['success' => true, 'message' => 'Berhasil Labeling Asset']);
        } catch (\Exception $e) {
            Log::info($e);
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    public function checkApproveSetting()
    {
        $return['status'] = true;
        $return['message'] = "";
        $jum_sett = PurchaseOrderSetting::count();
        if ($jum_sett) {
            $data_cek = PurchaseOrderSetting::where('role','Director')->count();
            if ($data_cek) {
                $return['status'] = true;
            }else{
                $return['status'] = false;
                $return['message'] = 'Wajib Set Approval Director di setting approval';
            }
        }else{
            $return['status'] = false;
            $return['message'] = 'Wajib isi approval setting dahulu';
        }

        return $return;
    }

    public function getListRfDetail(Request $request)
    {
        dd('masuk');

        $prNonAset_direktur = Helper::systemSetting('prNonAset_direktur');
        $prJasa_direktur = Helper::systemSetting('prJasa_direktur');
        if ($request->get('date-range') == 1) {
            $rf_id = RequestForm::with(['company', 'department', 'location', 'section', 'workshop', 'user'])
            ->where('status_approval', 1)->where('verified', 1)->where('status_approval_pr_ga', 1)
            ->where(function($query) use($request){
                if ($request->get('company')) {
                    $query->where('company_id', $request->get('company'));
                }
                if ($request->get('department')) {
                    $query->where('department_id', $request->get('department'));
                }
                if ($request->get('section')) {
                    $query->where('section_id', $request->get('section'));
                }
                if ($request->get('location')) {
                    $query->where('location_id', $request->get('location'));
                }
                if ($request->get('type_submission')) {
                    $query->where('type_submission', $request->get('type_submission'));
                }
                if ($request->get('no_pr')) {
                    $query->where('id', $request->get('no_pr'));
                }
            })
            ->orderBy('id', 'desc');
        }else{
            $rf_id = RequestForm::with(['company', 'department', 'location', 'section', 'workshop', 'user'])
            ->where('status_approval', 1)->where('verified', 1)->where('status_approval_pr_ga', 1)
            ->where(function($query) use($request){
                if ($request->get('company')) {
                    $query->where('company_id', $request->get('company'));
                }
                if ($request->get('department')) {
                    $query->where('department_id', $request->get('department'));
                }
                if ($request->get('section')) {
                    $query->where('section_id', $request->get('section'));
                }
                if ($request->get('location')) {
                    $query->where('location_id', $request->get('location'));
                }
                if ($request->get('type_submission')) {
                    $query->where('type_submission', $request->get('type_submission'));
                }
                if ($request->get('no_pr')) {
                    $query->where('id', $request->get('no_pr'));
                }
            })
            ->orderBy('id', 'desc');
        }

        $rf_id = $rf_id->whereHas('requestFormDetail', function($query) use ($prNonAset_direktur, $prJasa_direktur) {
                        $query
                            ->where(function($subQuery) {
                                $subQuery->where('type_submission_code', 1);
                            })
                            ->orWhere(function($subQuery) use ($prNonAset_direktur) {
                                $subQuery->where('type_submission_code', 2)
                                        ->whereRaw('(estimate_price * qty_request_buy) - budget >= ' . $prNonAset_direktur)
                                        ->where('status_approval_pr_direktur', 1)
                                        ->orWhereRaw('(estimate_price * qty_request_buy) - budget < 0');
                            })->orWhere(function($subQuery) {
                                $subQuery->where('type_submission_code', 2);
                            })->orWhere(function($subQuery) use ($prJasa_direktur) {
                                $subQuery->where('type_submission_code', 3)
                                        ->whereRaw('(estimate_price * qty_request_buy) - budget >= ' . $prJasa_direktur)
                                        ->where('status_approval_pr_direktur', 1)
                                        ->orWhereRaw('(estimate_price * qty_request_buy) - budget < 0');
                            })->orWhere(function($subQuery) {
                                $subQuery->where('type_submission_code', 3);
                            });
                    })->pluck('id');

        $dataPr = RequestFormDetail::with('requestForm')
        ->whereIn('request_form_id', $rf_id)
        ->where(function($query) use($request){
            if ($request->get('category_item')) {
                $query->where('category_item_id', $request->get('category_item'));
            }

            if ($request->get('type_item')) {
                $query->where('type_item_id', $request->get('type_item'));
            }
        })
        ->orderBy('id', 'desc')
        ->get();

        $data = [];
        $index = 0;

        foreach ($dataPr as $key => $value) {
            $value['no_pr'] = $value->requestForm->no_pr ?? null;
            $value['type_submission'] = $value->requestForm->type_submission ?? null;
            $value['company_name'] = $value->requestForm->company->name ?? null;
            $value['department_name'] = $value->requestForm->department->name ?? null;
            $value['section_name'] = $value->requestForm->section->name ?? null;
            $value['location_name'] = $value->requestForm->location->name ?? null;
            $value['company_id'] = $value->requestForm->company_id ?? null;
            $value['department_id'] = $value->requestForm->department_id ?? null;
            $value['section_id'] = $value->requestForm->section_id ?? null;
            $value['location_id'] = $value->requestForm->location_id ?? null;
            $value['category_item_name'] = $value->categoryItem->name ?? null;
            $value['type_item_name'] = $value->typeItem->name ?? null;
            $value['type_item_uom'] = $value->typeItem->uom ?? null;
            $value['no_seri'] = $value->requestForm->workshop->no_seri ?? null;
            $value['nopol'] = $value->requestForm->nopol ?? null;
            $value['po_qty'] = $value->qty_create;
            $value['qty_create'] = 0;
            $value['balance'] = $value['qty_request_buy'] - $value['po_qty'] - $value['qty_create'];
            $value['check'] = false;
            $value['qty_total'] = $value['qty_item_approve'] + $value['qty_request_buy'];
            if ($value['balance'] > 0) {
                $data[$index] = $value;
                $index ++;
            }
        }

        return response()->json($data);
    }

    public function exportListRfDetail(Request $request)
    {
        $prNonAset_direktur = Helper::systemSetting('prNonAset_direktur');
        $prJasa_direktur = Helper::systemSetting('prJasa_direktur');
        if ($request->get('date-range') == 1) {
            $rf_id = RequestForm::with(['company', 'department', 'location', 'section', 'workshop', 'user'])
            ->where('status_approval', 1)->where('verified', 1)->where('status_approval_pr_ga', 1)->where('status_approval_pr_direktur', 1)
            ->where(function($query) use($request){
                if ($request->get('no_pr')) {
                    $query->where('id', $request->get('no_pr'));
                }
                if ($request->get('company')) {
                    $query->where('company_id', $request->get('company'));
                }
                if ($request->get('department')) {
                    $query->where('department_id', $request->get('department'));
                }
                if ($request->get('section')) {
                    $query->where('section_id', $request->get('section'));
                }
                if ($request->get('location')) {
                    $query->where('location_id', $request->get('location'));
                }
                if ($request->get('type_submission')) {
                    $query->where('type_submission', $request->get('type_submission'));
                }
            })
            ->when(($request->get('start_date') ?? false) || ($request->get('to_date') ?? false) , function($query) use ($request) {
                $query->whereDate('created_at', '>=', $request->start_date)
                        ->whereDate('created_at', '<=', $request->to_date);
            })
            ->orderBy('id', 'desc');
        }else{
            $rf_id = RequestForm::with(['company', 'department', 'location', 'section', 'workshop', 'user'])
            ->where('status_approval', 1)->where('verified', 1)->where('status_approval_pr_ga', 1)->where('status_approval_pr_direktur', 1)
            ->where(function($query) use($request){
                if ($request->get('no_pr')) {
                    $query->where('id', $request->get('no_pr'));
                }
                if ($request->get('company')) {
                    $query->where('company_id', $request->get('company'));
                }
                if ($request->get('department')) {
                    $query->where('department_id', $request->get('department'));
                }
                if ($request->get('section')) {
                    $query->where('section_id', $request->get('section'));
                }
                if ($request->get('location')) {
                    $query->where('location_id', $request->get('location'));
                }
                if ($request->get('type_submission')) {
                    $query->where('type_submission', $request->get('type_submission'));
                }
            })
            ->orderBy('id', 'desc');
        }

        $rf_id = $rf_id->whereHas('requestFormDetail', function($query) use ($prNonAset_direktur, $prJasa_direktur) {
            $query
                ->where(function($subQuery) {
                    $subQuery->whereIn('type_submission_code', [1]);
                })
                ->orWhere(function($subQuery) use ($prNonAset_direktur) {
                    $subQuery->where('type_submission_code', 3)
                            ->whereRaw('(estimate_price * qty_request_buy) - budget > ' . $prNonAset_direktur)
                            ->where('status_approval_pr_direktur', 1)
                            ->orWhereRaw('(estimate_price * qty_request_buy) - budget < 0');
                })->orWhere(function($subQuery) {
                    $subQuery->where('type_submission_code', 3);
                })->orWhere(function($subQuery) use ($prJasa_direktur) {
                    $subQuery->where('type_submission_code', 4)
                            ->whereRaw('(estimate_price * qty_request_buy) - budget > ' . $prJasa_direktur)
                            ->where('status_approval_pr_direktur', 1)
                            ->orWhereRaw('(estimate_price * qty_request_buy) - budget < 0');
                });
        })->pluck('id');

        $dataPr = RequestFormDetail::with('requestForm')
        ->whereIn('request_form_id', $rf_id)
        ->where(function($query) use($request){
            if ($request->get('category_item')) {
                $query->where('category_item_id', $request->get('category_item'));
            }

            if ($request->get('type_item')) {
                $query->where('type_item_id', $request->get('type_item'));
            }
        })
        ->orderBy('id', 'desc')
        ->get();

        $data = [];
        $index = 0;

        foreach ($dataPr as $key => $value) {
            $value['no_pr'] = $value->requestForm->no_pr ?? null;
            $value['type_submission'] = $value->requestForm->type_submission ?? null;
            $value['company_name'] = $value->requestForm->company->name ?? null;
            $value['department_name'] = $value->requestForm->department->name ?? null;
            $value['section_name'] = $value->requestForm->section->name ?? null;
            $value['location_name'] = $value->requestForm->location->name ?? null;
            $value['company_id'] = $value->requestForm->company_id ?? null;
            $value['department_id'] = $value->requestForm->department_id ?? null;
            $value['section_id'] = $value->requestForm->section_id ?? null;
            $value['location_id'] = $value->requestForm->location_id ?? null;
            $value['category_item_name'] = $value->categoryItem->name ?? null;
            $value['type_item_name'] = $value->typeItem->name ?? null;
            $value['type_item_uom'] = $value->typeItem->uom ?? null;
            $value['no_seri'] = $value->requestForm->workshop->no_seri ?? null;
            $value['nopol'] = $value->requestForm->nopol ?? null;
            $value['po_qty'] = $value->qty_create;
            $value['qty_create'] = 0;
            $value['balance'] = $value['qty_request_buy'] - $value['po_qty'] - $value['qty_create'];
            $value['check'] = false;
            $value['qty_total'] = $value['qty_item_approve'] + $value['qty_request_buy'];
            if ($value['balance'] > 0) {
                $data[$index] = $value;
                $index ++;
            }
        }

        // return view('export.detail_pr', ['data' => $data]);
        return Excel::download(new ExportCreatePoPr($data), "List Create PO & Payment Req.xlsx");
    }

    public function sessionItemList(Request $request)
    {
        $data = $request->all();

        $item_detail_id = '';
        $index = 0;
        foreach ($data as $key => $value) {
            if ($value['check'] == true) {
                $item_detail_id .= 'items['.$index.']='.$value['id'].'&';

                $rfd = RequestFormDetail::find($value['id']);
                $rfd->qty_create_temporary = $value['qty_create'];
                // $rfd->balance = $value->balance;
                $rfd->save();

                $index++;
            }
        }

        return response()->json($item_detail_id);
    }

    public function getSessionItemList(Request $request)
    {
        $rfd_id = $request->id ?? [];
        $rfd_id = explode(',', $rfd_id);

        $idAll = [];
        $qtyTemporary = [];
        foreach($rfd_id as $rf) {
            $idAll[] = explode('-', $rf);
            $qtyTemporary[explode('-', $rf)[1]] = explode('-', $rf)[0];
        }

        $company  = array_column($idAll, 2);
        $company  = Helper::checkSameObjectInArray($company);

        $department  = array_column($idAll, 3);
        $department  = Helper::checkSameObjectInArray($department);

        $section  = array_column($idAll, 4);
        $section  = Helper::checkSameObjectInArray($section);

        $location  = array_column($idAll, 5);
        $location  = Helper::checkSameObjectInArray($location);

        $type  = array_column($idAll, 6);
        $type  = Helper::checkSameObjectInArray($type);

        if($company && $department && $section && $location && $type)
        {
            try {
                $items = RequestFormDetail::with(['categoryItem', 'typeItem'])->whereIn('id', array_column($idAll, 1))->orderBy('id', 'desc')->get();
            }catch(\Exception $e) {
                return response()->json(['status' => false, 'message' => $e->getMessage()]);
            }

            $data['sub_total'] = 0;
            foreach ($items as $key => $value) {
                $value->qty_create_temporary = $qtyTemporary[$value->id] ?? 0;
                $data['sub_total'] += $value->qty_create_temporary * $value->estimate_price;
                $value['amount'] = $value->qty_create_temporary * $value->estimate_price;

                if ($key == 0) {
                    $rf = RequestForm::find($value->request_form_id);
                    $TypeAsset = MasterTypeAsset::where('name', $rf->type_submission)->first();

                    $data['department_id'] = $rf->department_id;
                    $data['location_id'] = $rf->location_id;
                    $data['company_id'] = $rf->company_id;
                    $data['section_id'] = $rf->section_id;
                    $data['type_submission'] = $TypeAsset->code;
                }
                $value['qty_balance'] = $value['qty_request_buy'] - ($qtyTemporary[$value->id] ?? 0) - $value['qty_create'];
            }

            $data['ppn'] = 0;
            $data['total'] = $data['sub_total'] + $data['ppn'];

            $invoice_address = InvoiceAddress::orderBy('id', 'desc')->get();
            $deliver = Deliver::orderBy('id', 'desc')->get();
            $top = Top::orderBy('id', 'desc')->get();
            // $supplier = Supplier::orderBy('id', 'desc')->get();
            $tax = Tax::orderBy('id', 'desc')->get();

            return response()->json([
                'status'            => true,
                'data'              => $data,
                'items'             => $items,
                'invoice_address'   => $invoice_address,
                'deliver'           => $deliver,
                'top'               => $top,
                'tax'               => $tax,
            //  'supplier' => $supplier,
            ]);
        }else{
            return response()->json([
                'status' => false,
                'message' => 'Data tidak dapat diproses dikarenakan company atau jenis pengajuan tidak sama'
            ]);
        }
    }

    public function export(Request $request)
    {
        $data = FleetPurchaseOrder::with(['company', 'department', 'location', 'user'])
        ->where(function($query) use($request){
            if ($request->get('pr') == 1) {
                $query->where('type','!=', 'PO');
            }else if ($request->get('po') == 1) {
                $query->where('type','=', 'PO');
            }

            if ($request->get('status') == 1) {
                $query->where('approve_po', 1);
            }elseif ($request->get('status') == 2) {
                $query->where('approve_po_direktur', 1);
            }elseif ($request->get('status') == 3) {
                $query->where('approve_po_direktur', 2);
            }elseif ($request->get('status') == 4) {
                $query->where('approve_po', 2);
            }elseif ($request->get('status') == 5) {
                $query->where('approve_pr', 1)->where('approve_pr', 1);
            }elseif ($request->get('status') == 6) {
                $query->where('approve_pr_finance', 1)->where('approve_pr', 2);
            }elseif ($request->get('status') == 7) {
                $query->where('approve_pr_finance', 2);
            }elseif ($request->get('status') == 8) {
                $query->where('approve_pr_direktur', 1)->where('approve_pr', 3);
            }elseif ($request->get('status') == 9) {
                $query->where('approve_pr_direktur', 2);
            }elseif ($request->get('status') == 10) {
                $query->where('approve_pr', '>=', 2);
            }

            if ($request->get('company')) {
                $query->where('company_id', $request->get('company'));
            }
            if ($request->get('location')) {
                $query->where('location_id', $request->get('location'));
            }
            if ($request->get('department')) {
                $query->where('department_id', $request->get('department'));
            }
            if ($request->get('type_submission')) {
                $query->where('type_submission', $request->get('type_submission'));
            }
            if ($request->get('no_po')) {
                $query->where('id', $request->get('no_po'));
            }
            if ($request->get('no_pr')) {
                $query->where('id', $request->get('no_pr'));
            }
            if ($request->get('supplier_id')) {
                $query->where('supplier_id', $request->get('supplier_id'));
            }
        })
        ->when(($request->get('start_date') ?? false) || ($request->get('to_date') ?? false) , function($query) use ($request) {
                $query->whereDate('created_at', '>=', $request->start_date)
                ->whereDate('created_at', '<=', $request->to_date);
        })
        ->orderBy('id', 'desc')
        ->get();

        foreach ($data as $key => $value) {
            $value['company_name'] = $value->company->name ?? null;
            $value['department_name'] = $value->department->name ?? null;
            $value['location_name'] = $value->location->name ?? null;
            $value['supplier_name'] = $value->supplier->name ?? null;
            $value['section_name'] = $value->section->name ?? null;

            $items = $value->items;
            foreach ($items as $ckey => $cvalue) {
                $value['items'][$ckey] = $cvalue->typeItem->name ?? null;
            }

            if ($value->type == 'Transfer'){
                $value['no_pr'] = $value->no_tf;
            }else if($value->type == 'Cash Advance'){
                $value['no_pr'] = $value->no_ca;
            }else if($value->type == 'Virtual Account'){
                $value['no_pr'] = $value->no_va;
            }

            if($value->type_submission == 1){
                $value['type_submission'] = 'Asset';
            }elseif($value->type_submission == 2){
                $value['type_submission'] = 'Non Asset';
            }elseif($value->type_submission == 3){
                $value['type_submission'] = 'Jasa';
            }

            if ($value->approve_po == 1 && $value->approve_po_direktur == null) {
                $value['status'] = 'Approve PO';
            }elseif ($value->approve_po == 2 && $value->approve_po_direktur == 1 && $value->no_gr == null) {
                $value['status'] = 'Approve PO Direktur';
            }elseif ($value->approve_po_direktur == 2) {
                $value['status'] = 'Reject PO Direktur';
            }elseif ($value->approve_pr == 1 && $value->approve_pr_finance == null) {
                $value['status'] = 'Approve PR RSC';
            }elseif ($value->approve_pr == 2 && $value->approve_pr_finance == 1) {
                $value['status'] = 'Approve PR Finance';
            }elseif ($value->approve_pr_finance == 2) {
                $value['status'] = 'Reject PR Finance';
            }elseif ($value->approve_pr == 3 && $value->approve_pr_direktur == 1 && $value->no_gr == null) {
                $value['status'] = 'Approve PR Direktur';
            }elseif ($value->approve_pr_direktur == 2) {
                $value['status'] = 'Reject PR Direktur';
            }elseif ($value->approve_po == null) {
                $value['status'] = 'Waiting Approve PO RSC';
            }elseif ($value->approve_pr == null) {
                $value['status'] = 'Waiting Approve PR RSC';
            }else{
                $value['status'] = 'Proses GR';
            }
        }

        if ($request->get('po')) {
            $type = 'po';
            $name = 'Purchase Order';
        }else if ($request->get('pr')) {
            $type = 'pr';
            $name = 'Payment Request';
        }

        return Excel::download(new ExportCreatePr($data,$type), "List ". $name.".xlsx");

    }

    public function createdPO(Request $request)
    {
        $data = $request->all();
        // dd($data);
        $dataPO['user_id'] = Auth::user()->id;
        $dataPO['tnc'] = $data['tnc'] ?? null;

        if ($data['type'] == 'Transfer'){
            $dataPO['no_tf'] = Helper::genCode($data['company_id'], $data['department_id'], $data['location_id'],$data['type_submission'], 'TF') ?? 'TF';
            $no_ref = $dataPO['no_tf'];
        }else if($data['type'] == 'Cash Advance'){
            $dataPO['no_ca'] = Helper::genCode($data['company_id'], $data['department_id'], $data['location_id'],$data['type_submission'], 'CA') ?? 'CA';
            $no_ref = $dataPO['no_ca'];
        }else if($data['type'] == 'Virtual Account'){
            $dataPO['no_va'] = Helper::genCode($data['company_id'], $data['department_id'], $data['location_id'],$data['type_submission'], 'VA') ?? 'VA';
            $no_ref = $dataPO['no_va'];
        }else{
            $no_po = Helper::genCode($data['company_id'], $data['department_id'], $data['location_id'],$data['type_submission'], 'PO');
            $dataPO['no_po'] = $no_po;
            $no_ref = $dataPO['no_po'];
        }

        $dataPO['department_id'] = $data['department_id'];
        $dataPO['section_id'] = $data['section_id'];
        $dataPO['location_id'] = $data['location_id'];
        $dataPO['company_id'] = $data['company_id'];
        $dataPO['supplier_id'] = $data['supplier_id'] ?? null;
        $dataPO['type'] = $data['type'] ?? null;
        $dataPO['deliver_id'] = $data['deliver_id'] ?? null;
        $dataPO['invoice_address_id'] = $data['invoice_address_id'] ?? null;
        $dataPO['top_id'] = $data['top_id'] ?? null;
        $dataPO['tax_id'] = $data['tax_id'] ?? null;
        $type_submission = $data['items'][0]['category_item']['type_asset'];
        $dataPO['type_submission'] = $type_submission;
        $dataPO['total'] = $data['total'] ?? null;
        $dataPO['sub_total'] = $data['sub_total'] ?? null;
        $dataPO['ppn'] = $data['ppn'] ?? null;
        $dataPO['va_number'] = $data['va_number'] ?? null;
        $dataPO['price_ongkir'] = $data['price_ongkir'] ?? null;
        $dataPO['remark'] = $data['remark'] ?? null;
        $dataPO['no_rek'] = $data['no_rek'] ?? null;
        $dataPO['type_bank'] = $data['type_bank'] ?? null;
        $dataPO['rek_name'] = $data['rek_name'] ?? null;
        $dataPO['user_pjd_id'] = $data['user_pjd_id'] ?? null;

        $limit_approval_directur = (int)Helper::systemSetting('approval_direktur');
        if ($data['total'] < $limit_approval_directur && $dataPO['type_submission'] != 1) {
            $dataPO['approve_po_direktur'] = 1;
            $dataPO['approve_pr_direktur'] = 1;
        }else{
            $dataPO['approve_po_direktur'] = null;
            $dataPO['approve_pr_direktur'] = null;
        }

        $po = FleetPurchaseOrder::create($dataPO);

        foreach ($data['items'] as $key => $value) {
            $dataDetail['purchase_order_id'] = $po->id;
            $dataDetail['request_form_detail_id'] = $value['id'];
            $dataDetail['type_item_id'] = $value['type_item_id'];
            $dataDetail['qty'] = $value['qty_create_temporary'];
            $dataDetail['qty_asset'] = $value['qty'];
            $dataDetail['unit_price'] = $value['estimate_price'];
            $dataDetail['amount'] = $value['amount'];

            FleetPurchaseOrderDetail::create($dataDetail);

            $rfd = RequestFormDetail::find($value['id']);
            $rfd->po_id = $po->id;
            // $rfd->qty_create = (int)$value['qty_create_temporary'] + (int)$value['qty_create'];
            $rfd->save();
        }

        return response()->json(['status' => true, 'message' => 'Pengajuan anda sudah tersimpan dengan No : ', 'no_po' => $no_ref, 'id' => $po->id]);
    }

    public function approvePo(Request $request, $id)
    {
        $data = $request->all();

        if (isset($data['po_rsc'])) {
            // $po = FleetPurchaseOrder::find($id);
            // $po->approve_po = $data['po_rsc'];
            // $po->approve_po_rsc = 1;
            // $po->save();

            $detail = FleetPurchaseOrderDetail::where('purchase_order_id', $id)->get();
            $amount = 0;
            $ppn = 0;

            foreach ($detail as $key => $value) {
                $amount += $value->amount;
            }

            $po = FleetPurchaseOrder::find($id);
            $po->approve_po = $data['po_rsc'];
            $po->approve_po_rsc = 1;
            $po->sub_total = $amount;

            if ($po->tax_id == 1) {
                $ppn = $amount * (11/100);
            }

            $po->ppn = $ppn ;
            $po->total = $amount + $ppn;
            $po->save();

            LogApproval::createLogApproval(1, 'approval-po-rsc', 'RSC', $id, Auth::user()->id);

        }elseif (isset($data['po_direktur'])) {
            $po = FleetPurchaseOrder::find($id);
            $po->approve_po = $data['po_direktur'];
            $po->approve_po_direktur = $data['status'];
            $po->save();

            LogApproval::createLogApproval(1, 'approval-po-direktur', 'DIREKTUR', $id);
        }else{
            $dataPO = FleetPurchaseOrder::find($id);
            $dataPO->department_id = $data['department_id'];
            $dataPO->section_id = $data['section_id'];
            $dataPO->location_id = $data['location_id'];
            $dataPO->company_id = $data['company_id'];
            $dataPO->supplier_id = $data['supplier_id'] ?? null;
            $dataPO->type = $data['type'] ?? null;
            $dataPO->deliver_id = $data['deliver_id'] ?? null;
            $dataPO->tnc = $data['tnc'] ?? null;
            $dataPO->invoice_address_id = $data['invoice_address_id'] ?? null;
            $dataPO->top_id = $data['top_id'] ?? null;
            $dataPO->tax_id = $data['tax_id'] ?? null;
            $dataPO->total = $data['total'] ?? null;
            $dataPO->sub_total = $data['sub_total'] ?? null;
            $dataPO->ppn = $data['ppn'] ?? null;
            $dataPO->va_number = $data['va_number'] ?? null;
            $dataPO->price_ongkir = $data['price_ongkir'] ?? null;
            $dataPO->remark = $data['remark'] ?? null;
            $dataPO->no_rek = $data['no_rek'] ?? null;
            $dataPO->type_bank = $data['type_bank'] ?? null;
            $dataPO->rek_name = $data['rek_name'] ?? null;
            $dataPO->user_pjd_id = $data['user_pjd_id'] ?? null;
            $dataPO->save();

            foreach ($data['items'] as $key => $value) {
                $dataDetails = FleetPurchaseOrderDetail::find($value['id']);
                $dataDetail = FleetPurchaseOrderDetail::find($value['id']);
                $dataDetail->type_item_id = $value['type_item_id'];
                $dataDetail->qty =  $value['qty'];
                $dataDetail->unit_price = $value['unit_price'];
                $dataDetail->amount = $value['amount'];
                $dataDetail->save();
                // if($dataDetail->requestFormDetail) {
                //     $dataDetail->requestFormDetail->qty_create = $value['qty'];
                //     $dataDetail->requestFormDetail->save();
                // }

                $rf = RequestFormDetail::find($dataDetail->request_form_detail_id);
                if($value['qty'] < $dataDetails->qty)
                {
                    if($dataDetail->requestFormDetail) {
                        $dataDetail->requestFormDetail->qty_create = $rf->qty_create + $value['qty']- $dataDetails->qty;
                        $dataDetail->requestFormDetail->save();
                    }
                }elseif($value['qty'] > $dataDetails->qty){
                    if($dataDetail->requestFormDetail) {
                        $dataDetail->requestFormDetail->qty_create = $rf->qty_create + $value['qty'] + $dataDetails->qty;
                        $dataDetail->requestFormDetail->save();
                    }
                }
            }

            $po = FleetPurchaseOrder::find($id);
            $po->approve_po = $data['po_rscs'];
            $po->approve_po_rsc = $data['status'];
            $po->save();

            LogApproval::createLogApproval($data['status'], 'approval-po-rsc', 'RSC', $id);
        }

        return response()->json(['status' => true, 'message' => 'Dokumen Berhasil Diupdate']);
    }

    public function approvePr(Request $request, $id)
    {
        $data = $request->all();

        if (isset($data['pr_rsc'])) {
            // $pr = FleetPurchaseOrder::find($id);
            // $pr->approve_pr = 1;
            // $pr->save();

            // LogApproval::createLogApproval(1, 'approval-pr-rsc', 'RSC', $id);

            $detail = FleetPurchaseOrderDetail::where('purchase_order_id', $id)->get();
            $amount = 0;
            $ppn = 0;
            $ongkir = 0;

            foreach ($detail as $dataDetail) {
                $amount += $dataDetail->amount;
            }

            $pr = FleetPurchaseOrder::find($id);
            $pr->approve_pr = 1;
            $pr->sub_total = $amount;

            if ($pr->tax_id == 1) {
                $ppn = $amount * (11/100);
            }

            if($data['type'] == 'Virtual Account')
            {
                $ongkir = $dataDetail->price_ongkir ;
                $pr->total = $amount + $ongkir;
            }elseif($data['type'] == 'Cash Advance')
            {
                $pr->total = $amount;
            }else{
                $ongkir = $dataDetail->price_ongkir ;
                $pr->ppn = $ppn ;
                $pr->total = $amount + $ppn + $ongkir;
            }

            $pr->save();

            LogApproval::createLogApproval(1, 'approval-pr-rsc', 'RSC', $id);
        }elseif (isset($data['pr_finance'])) {
            $pr = FleetPurchaseOrder::find($id);
            $pr->approve_pr = $data['pr_finance'];
            $pr->approve_pr_finance = ($data['status'] == 0) ? null : $data['status'];
            $pr->save();

            LogApproval::createLogApproval(1, 'approval-pr-finance', 'FINANCE', $id, Auth::user()->id);
        }elseif (isset($data['pr_direktur'])) {
            $pr = FleetPurchaseOrder::find($id);
            $pr->approve_pr = $data['pr_direktur'];
            $pr->approve_pr_direktur = $data['status'];
            $pr->save();

            LogApproval::createLogApproval(1, 'approval-pr-direktur', 'DIREKTUR', $id, Auth::user()->id);
        }else{
            $dataPR = FleetPurchaseOrder::find($id);
            $dataPR->department_id = $data['department_id'];
            $dataPR->section_id = $data['section_id'];
            $dataPR->location_id = $data['location_id'];
            $dataPR->company_id = $data['company_id'];
            $dataPR->supplier_id = $data['supplier_id'] ?? null;
            $dataPR->type = $data['type'] ?? null;
            $dataPR->deliver_id = $data['deliver_id'] ?? null;
            $dataPR->tnc = $data['tnc'] ?? null;
            $dataPR->invoice_address_id = $data['invoice_address_id'] ?? null;
            $dataPR->top_id = $data['top_id'] ?? null;
            $dataPR->tax_id = $data['tax_id'] ?? null;
            $dataPR->total = $data['total'] ?? null;
            $dataPR->sub_total = $data['sub_total'] ?? null;
            $dataPR->ppn = $data['ppn'] ?? null;
            $dataPR->va_number = $data['va_number'] ?? null;
            $dataPR->price_ongkir = $data['price_ongkir'] ?? null;
            $dataPR->remark = $data['remark'] ?? null;
            $dataPR->no_rek = $data['no_rek'] ?? null;
            $dataPR->type_bank = $data['type_bank'] ?? null;
            $dataPR->rek_name = $data['rek_name'] ?? null;
            $dataPR->user_pjd_id = $data['user_pjd_id'] ?? null;
            $dataPR->save();


            foreach ($data['items'] as $key => $value) {
                $dataDetails = FleetPurchaseOrderDetail::find($value['id']);
                $dataDetail = FleetPurchaseOrderDetail::find($value['id']);
                $dataDetail->type_item_id = $value['type_item_id'];
                $dataDetail->qty = $value['qty'];
                $dataDetail->unit_price = $value['unit_price'];
                $dataDetail->amount = $value['amount'];
                $dataDetail->save();

                $rf = RequestFormDetail::find($dataDetail->request_form_detail_id);
                // if($value['qty'] < $dataDetail->qty)
                // {
                //     if($dataDetail->requestFormDetail) {
                //         $dataDetail->requestFormDetail->qty_create = $rf->qty_create + $value['qty']-$dataDetail->qty;
                //         $dataDetail->requestFormDetail->save();
                //     }
                // }else{
                //     if($dataDetail->requestFormDetail) {
                //         $dataDetail->requestFormDetail->qty_create = $value['qty'];
                //         $dataDetail->requestFormDetail->save();
                //     }
                // }
                if($value['qty'] < $dataDetails->qty)
                {
                    if($dataDetail->requestFormDetail) {
                        $dataDetail->requestFormDetail->qty_create = $rf->qty_create + $value['qty'] - $dataDetails->qty;
                        $dataDetail->requestFormDetail->save();
                    }
                }elseif($value['qty'] > $dataDetails->qty){
                    if($dataDetail->requestFormDetail) {
                        $dataDetail->requestFormDetail->qty_create = $rf->qty_create + $value['qty'] + $dataDetails->qty;
                        $dataDetail->requestFormDetail->save();
                    }
                }
            }

            $pr = FleetPurchaseOrder::find($id);
            $pr->approve_pr = 1;
            $pr->save();

            LogApproval::createLogApproval(1, 'approval-pr-rsc', 'RSC', $id);
        }

        return response()->json(['status' => true, 'message' => 'Dokumen Berhasil Diapprove']);
    }

    public function pdfApprovePo(Request $request)
    {
        $data = $request->all();
        $id = $request->id;

        if (isset($data['po_rsc'])) {
            $po = FleetPurchaseOrder::find($id);
            $po->approve_po = $data['po_direktur'];
            $po->approve_po_direktur = $data['status'];
            $po->save();

            LogApproval::createLogApproval(1, 'approval-po-rsc', 'RSC', $id, $data['uid']);
        }elseif (isset($data['po_direktur'])) {
            $po = FleetPurchaseOrder::find($id);
            $po->approve_po = $data['po_direktur'];
            $po->approve_po_direktur = $data['status'];
            $po->save();

            LogApproval::createLogApproval(1, 'approval-po-direktur', 'DIREKTUR', $id, $data['uid']);
        }

        return redirect()->back()->with('success', 'Dokumen Berhasil Diapprove');
    }

    public function pdfApprovePr(Request $request)
    {
        $data = $request->all();
        $id = $request->id;

        if (isset($data['pr_rsc'])) {
            $pr = FleetPurchaseOrder::find($id);
            $pr->approve_pr = 1;
            $pr->approve_pr_finance = null;
            $pr->save();

            LogApproval::createLogApproval(1, 'approval-pr-rsc', 'RSC', $id);
        }elseif (isset($data['pr_finance'])) {
            $pr = FleetPurchaseOrder::find($id);
            $pr->approve_pr = $data['pr_finance'];
            $pr->approve_pr_finance = ($data['status'] == 0) ? null : $data['status'];
            $pr->save();

            LogApproval::createLogApproval(1, 'approval-pr-finance', 'FINANCE', $id, $data['uid']);
        }elseif (isset($data['pr_direktur'])) {
            $pr = FleetPurchaseOrder::find($id);
            $pr->approve_pr = $data['pr_direktur'];
            $pr->approve_pr_direktur = $data['status'];
            $pr->save();

            LogApproval::createLogApproval(1, 'approval-pr-direktur', 'DIREKTUR', $id, $data['uid']);
        }

        return redirect()->back()->with('success', 'Dokumen Berhasil Diapprove');
    }

    public function approvePoDirektur(Request $request)
    {
        $data = FleetPurchaseOrder::find($request->id);
        if ($data) {
            $data['approval_rsc'] = LogApproval::where('slug', 'approval-po-rsc')->where('ref_id', $data->id)->orderBy('id', 'desc')->first() ?? null;
            $data['approval_direktur'] = LogApproval::where('slug', 'approval-po-direktur')->where('ref_id', $data->id)->orderBy('id', 'desc')->first() ?? null;
        }else{
            $data['approval_rsc'] = null;
            $data['approval_direktur'] = null;
        }

        return view('approval.po.direktur', ['data' => $data]);
    }

    public function approvePrFinance(Request $request)
    {
        $data = FleetPurchaseOrder::find($request->id);
        $data['approval_rsc'] = LogApproval::where('slug', 'approval-pr-rsc')->where('ref_id', $data->id)->orderBy('id', 'desc')->first();
        $data['approval_finance'] = LogApproval::where('slug', 'approval-pr-finance')->where('ref_id', $data->id)->orderBy('id', 'desc')->first();
        $data['approval_direktur'] = LogApproval::where('slug', 'approval-pr-direktur')->where('ref_id', $data->id)->orderBy('id', 'desc')->first();
        if ($data->type == 'Transfer'){
            $data['no_pr'] = $data->no_tf;
        }else if($data->type == 'Cash Advance'){
            $data['no_pr'] = $data->no_ca;
        }else if($data->type == 'Virtual Account'){
            $data['no_pr'] = $data->no_va;
        }

        return view('approval.pr.finance', ['data' => $data]);
    }

    public function approvePrDirektur(Request $request)
    {
        $data = FleetPurchaseOrder::find($request->id);
        $data['approval_rsc'] = LogApproval::where('slug', 'approval-pr-rsc')->where('ref_id', $data->id)->orderBy('id', 'desc')->first() ?? null;
        $data['approval_finance'] = LogApproval::where('slug', 'approval-pr-finance')->where('ref_id', $data->id)->orderBy('id', 'desc')->first() ?? null;
        $data['approval_direktur'] = LogApproval::where('slug', 'approval-pr-direktur')->where('ref_id', $data->id)->orderBy('id', 'desc')->first() ?? null;
        if ($data->type == 'Transfer'){
            $data['no_pr'] = $data->no_tf;
        }else if($data->type == 'Cash Advance'){
            $data['no_pr'] = $data->no_ca;
        }else if($data->type == 'Virtual Account'){
            $data['no_pr'] = $data->no_va;
        }

        return view('approval.pr.direktur', ['data' => $data]);
    }

    public function approveReport(Request $request)
    {
        $data = FleetPurchaseOrder::find($request->id);
        $data['approval_rsc'] = LogApproval::where('slug', 'approval-po-rsc')->where('ref_id', $data->id)->orderBy('id', 'desc')->first() ?? null;
        $data['approval_direktur'] = LogApproval::where('slug', 'approval-po-direktur')->where('ref_id', $data->id)->orderBy('id', 'desc')->first() ?? null;
        if ($data->type == 'Transfer'){
            $data['no_pr'] = $data->no_tf;
        }else if($data->type == 'Cash Advance'){
            $data['no_pr'] = $data->no_ca;
        }else if($data->type == 'Virtual Account'){
            $data['no_pr'] = $data->no_va;
        }

        $cetak = "Form PO.pdf";

        $pdf = PDF::loadview('approval.po.report', compact('data'))
                    ->setPaper('A4', 'portrait')
                    ->setOptions(['isPhpEnabled' => true, 'enable_remote' => true]);

        $contxt = stream_context_create([
            'ssl' => [
                'verify_peer' => FALSE,
                'verify_peer_name' => FALSE,
                'allow_self_signed' => TRUE
            ]
        ]);

        return $pdf->stream($cetak);
    }

    public function approveReportPr(Request $request)
    {
        $data = FleetPurchaseOrder::find($request->id);
        $data['approval_rsc'] = LogApproval::where('slug', 'approval-pr-rsc')->where('ref_id', $data->id)->orderBy('id', 'desc')->first();
        $data['approval_finance'] = LogApproval::where('slug', 'approval-pr-finance')->where('ref_id', $data->id)->orderBy('id', 'desc')->first();
        $data['approval_direktur'] = LogApproval::where('slug', 'approval-pr-direktur')->where('ref_id', $data->id)->orderBy('id', 'desc')->first();
        if ($data->type == 'Transfer'){
            $data['no_pr'] = $data->no_tf;
        }else if($data->type == 'Cash Advance'){
            $data['no_pr'] = $data->no_ca;
        }else if($data->type == 'Virtual Account'){
            $data['no_pr'] = $data->no_va;
        }

        $cetak = "Form PR.pdf";

        $pdf = PDF::loadview('approval.pr.report', compact('data'))
                    ->setPaper('A4', 'portrait')
                    ->setOptions(['isPhpEnabled' => true, 'enable_remote' => true]);

        $contxt = stream_context_create([
            'ssl' => [
                'verify_peer' => FALSE,
                'verify_peer_name' => FALSE,
                'allow_self_signed' => TRUE
            ]
        ]);

        return $pdf->stream($cetak);
    }
}
