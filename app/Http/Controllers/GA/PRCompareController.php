<?php

namespace App\Http\Controllers\GA;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\GA\StockRequest;
use App\Models\GA\StockRequestItems;
use App\Models\GA\ComparativeQuotation;
use App\Models\GA\ComparativeQuotationTermins;
use App\Models\GA\ComparativeQuotationDetail;
use App\Models\GA\ComparativeQuotationItem;
use App\Models\Fleet\Department;
use App\Models\Fleet\Supplier;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use Auth;

class PRCompareController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $data = StockRequest::with('department','category','items','history')
        ->where('authorised',1)
        ->where('pick_up','!=',1)
        // ->where('closed',0)
        ->get()
        ->toArray();
        
        foreach ($data as $key => $datas) {
            $count_completed = StockRequest::find($datas['id'])->items->where('completed',0)->count();

            $totalAll = 0;
            $countExist = 0;
            $countItem = StockRequestItems::where('dispatch_id', $datas['id'])->count();
            $item = StockRequestItems::where('dispatch_id', $datas['id'])->get();
            foreach ($item as $items) {
                $exist = ComparativeQuotationItem::where('stock_request_items_id',$items->id)->count();
                if ($exist) {
                    $countExist++;
                }
                $totalAll += $items->quantity * $items->price;
            }
            $data[$key]['totalAll'] = $totalAll;
            if ($countExist == $countItem) {
                unset($data[$key]);
            }
            if ($count_completed == 0) {
                unset($data[$key]);
            }
        }

        $nego = ComparativeQuotation::where('status','Draft')
        ->where('authorised',0)
        ->with('purchaseRequest.category','user')
        ->get();

        $po = ComparativeQuotation::where('status','Submit')
        ->with('purchaseRequest.category','user')
        ->get();

        return response()->json(['data' => $data, 'nego' => $nego, 'po' => $po]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create(Request $request)
    {
        $checkbox = $request->input('checkbox');
        if (!is_array($checkbox)) {
            $checkbox = [];
            $checkbox[] = $request->input('checkbox');
        }
        $stock_request = StockRequest::with('supplier')->whereIn('id',$checkbox)->get();
        $exist = ComparativeQuotation::whereIn('stock_request_id',$checkbox)->pluck('id','id');
        $existItem = ComparativeQuotationItem::whereIn('comparative_quotation_id',$exist)->pluck('stock_request_items_id','stock_request_items_id');

        $items = StockRequestItems::whereIn('dispatch_id',$checkbox)->whereNotIn('id',$existItem)->with('purchase','item')->where('completed',0)->where('authorised',1)->get();
        foreach ($items as $key => $item) {
            if ($item->quantity <= $item->qty_ready) {
                unset($items[$key]);
            }
        }

        $supplier = Supplier::all();

        return response()->json(['request' => $items, 'supplier' => $supplier, 'stock_request' => $stock_request]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'dp' => 'required|numeric',
                'retensi' => 'required|numeric',
                'pelunasan' => 'required|numeric',
                'description' => 'required',
                'reason' => 'required',
            ]);
    
            if ($validator->fails()) {
                return response()->json($validator->errors(), 500);
            }
            
            $pr_cq = [];
            if (is_array($request->stock_request)) {
                $check_supp = [];
                foreach ($request->list_request as $key => $req) {
                    if ($req['supplier'] != "") {
                        $check_supp[$req['dispatch_id']] = $req['dispatch_id'];
                    }
                }
                foreach ($request->stock_request as $key => $stock) {
                    if (isset($check_supp[$stock['id']])) {
                        $data = new ComparativeQuotation;
                        $data->code = 'CQS-'.Carbon::now()->format('ymdHis').rand(10,99);
                        $data->date = Carbon::now();
                        $data->authorised = 0;
                        $data->requester = Auth::user()->id;
                        $data->description = $request->description;
                        $data->reason = $request->reason;
                        $data->stock_request_id = $stock['id'];
                        $data->status = 'Draft';
                        $data->save();

                        $pr_cq[$data->stock_request_id] = $data->id;

                        if ($stock['category_id'] != "") {
                            $termins = new ComparativeQuotationTermins;
                            $termins->comparative_quotation_id = $data->id;
                            $termins->dp = $request->dp;
                            $termins->retensi = $request->retensi;
                            $termins->pelunasan = $request->pelunasan;
                            $termins->category_id = $stock['category_id'];
                            $termins->save();
                        }
                    }
                }
            }

            $ppn = [];
            if (is_array($request->ppn)) {
                foreach ($request->ppn as $key => $ppnsup) {
                    $ppn[$ppnsup['supplier']] = $ppnsup['ppn'];
                }
            }
            
            if (is_array($request->list_request)) {
                foreach ($request->list_request as $key => $req) {
                    if ($req['supplier'] != "") {
                        $supplier = Supplier::find($req['supplier']);

                        $detail = new ComparativeQuotationDetail;
                        $detail->supplier_id = $req['supplier'];
                        if (isset($pr_cq[$req['dispatch_id']])) {
                            $detail->comparative_quotation_id = $pr_cq[$req['dispatch_id']];
                        }else{
                            $detail->comparative_quotation_id = 0;
                        }
                        $detail->discount = 0;
                        if (isset($ppn[$req['supplier']])) {
                            $detail->vat = $ppn[$req['supplier']];
                        }else{
                            $detail->vat = 0;
                        }
                        $detail->total = $req['total'];
                        $detail->save();

                        $item = new ComparativeQuotationItem;
                        $item->supplier_id = $req['supplier'];
                        $item->stock_id = $req['code'];
                        $item->quantity = $req['quantity'];
                        $item->uom = $req['uom'];
                        if ($supplier) {
                            $item->curr_code = $supplier->currency_id;
                        }
                        $item->price = $req['price'];
                        $item->amount = $req['total'];
                        $item->cq_detail_id = $detail->id;
                        $item->stock_request_items_id = $req['id'];
                        $item->choosed = $req['choosed'];
                        if (isset($pr_cq[$req['dispatch_id']])) {
                            $item->comparative_quotation_id = $pr_cq[$req['dispatch_id']];
                        }else{
                            $item->comparative_quotation_id = 0;
                        }
                        $item->save();
                    }
                }
            }

            return response()->json(['success' => true, 'message' => 'Berhasil Tambah CQS']);
        } catch (\Exception $e) {
            Log::error($e);
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $cq = ComparativeQuotation::with('items.stock','details.supplier','termin','purchaseRequest')->findOrFail($id);
        $stock_request = StockRequest::where('id',$cq->stock_request_id)->get();
        $items = StockRequestItems::where('dispatch_id',$cq->stock_request_id)->where('completed',0)->with('purchase')->get();
        $supplier = Supplier::all();

        return response()->json(['request' => $items, 'supplier' => $supplier, 'stock_request' => $stock_request, 'cq' => $cq]);
    }

    public function submitNego($id)
    {
        try{
            $cq = ComparativeQuotation::findOrFail($id);
            $cq->status = 'Submit';
            $cq->save();
            return response()->json(['success' => true, 'message' => 'Berhasil Submit data']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }

    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        try {
            $validator = Validator::make($request->all(), [
                'dp' => 'required|numeric',
                'retensi' => 'required|numeric',
                'pelunasan' => 'required|numeric',
                'description' => 'required',
                'reason' => 'required',
            ]);
    
            if ($validator->fails()) {
                return response()->json($validator->errors(), 500);
            }
            
            $pr_cq = [];
            if (is_array($request->stock_request)) {
                $check_supp = [];
                foreach ($request->list_request as $key => $req) {
                    if ($req['supplier'] != "") {
                        $check_supp[$req['dispatch_id']] = $req['dispatch_id'];
                    }
                }
                $data = ComparativeQuotation::find($id);
                $data->authorised = 0;
                $data->requester = Auth::user()->id;
                $data->description = $request->description;
                $data->reason = $request->reason;
                $data->save();

                $pr_cq[$data->stock_request_id] = $data->id;

                $termins = ComparativeQuotationTermins::where('comparative_quotation_id',$id)->first();
                $termins->dp = $request->dp;
                $termins->retensi = $request->retensi;
                $termins->pelunasan = $request->pelunasan;
                $termins->save();
            }

            $ppn = [];
            if (is_array($request->ppn)) {
                foreach ($request->ppn as $key => $ppnsup) {
                    $detail = ComparativeQuotationDetail::find($ppnsup['id']);
                    $detail->supplier_id = $ppnsup['supplier'];
                    $detail->discount = 0;
                    $detail->vat = $ppnsup['ppn'];
                    $detail->save();
                }
            }
            
            if (is_array($request->list_request)) {
                foreach ($request->list_request as $key => $req) {
                    if ($req['supplier'] != "") {
                        $supplier = Supplier::find($req['supplier']);

                        $item = ComparativeQuotationItem::find($req['id']);;
                        $item->supplier_id = $req['supplier'];
                        $item->stock_id = $req['code'];
                        $item->quantity = $req['quantity'];
                        $item->uom = $req['uom'];
                        if ($supplier) {
                            $item->curr_code = $supplier->currency_id;
                        }
                        $item->price = $req['price'];
                        $item->amount = $req['total'];
                        $item->cq_detail_id = $detail->id;
                        $item->stock_request_items_id = $req['id'];
                        $item->choosed = $req['choosed'];
                        if (isset($pr_cq[$req['dispatch_id']])) {
                            $item->comparative_quotation_id = $pr_cq[$req['dispatch_id']];
                        }else{
                            $item->comparative_quotation_id = 0;
                        }
                        $item->save();
                    }
                }
            }

            return response()->json(['success' => true, 'message' => 'Berhasil Update CQS']);
        } catch (\Exception $e) {
            Log::error($e);
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}
