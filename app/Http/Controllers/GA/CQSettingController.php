<?php

namespace App\Http\Controllers\GA;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\GA\ComparativeQuotationSetting;
use Spatie\Permission\Models\Role;
use Illuminate\Support\Facades\Validator;

class CQSettingController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $data = ComparativeQuotationSetting::orderBy('id','asc')->get();
        return response()->json($data);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $role_exist = ComparativeQuotationSetting::pluck('role','role');
        $role = Role::whereNotIn('name',$role_exist)->get();
        $urut_get = ComparativeQuotationSetting::orderBy('seq','desc')->first();
        if (!$urut_get) {
            $urut = 1;
        }else {
            $urut = $urut_get->seq + 1;
        }

        return response()->json(['role' => $role, 'urut' => $urut]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'role' => 'required',
                'type' => 'required',
                'seq' => 'required|numeric',
                'amount' => 'required|numeric',
            ]);
    
            if ($validator->fails()) {
                return response()->json($validator->errors(), 500);
            }

            $seq = $request->input('seq');
            $cek_seq = ComparativeQuotationSetting::where('seq',$seq)->count();
            if($cek_seq > 0)
            {
                return response()->json(['success' => false, 'message' => 'Nomor Urut Tidak Boleh Sama']);
            }
            
            $data = ComparativeQuotationSetting::create($request->all());

            return response()->json(['success' => true, 'message' => 'Berhasil input data']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $data = ComparativeQuotationSetting::find($id);
        $role_exist = ComparativeQuotationSetting::where('role','!=',$data->role)->pluck('role','role');
        $role = Role::whereNotIn('name',$role_exist)->get();

        return response()->json(['data' => $data, 'role' => $role]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        try {
            $validator = Validator::make($request->all(), [
                'role' => 'required',
                'type' => 'required',
                'seq' => 'required|numeric',
                'amount' => 'required|numeric',
            ]);
    
            if ($validator->fails()) {
                return response()->json($validator->errors(), 500);
            }
            
            $data = ComparativeQuotationSetting::find($id)->update($request->all());

            return response()->json(['success' => true, 'message' => 'Berhasil update data']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        try{
            $data = ComparativeQuotationSetting::destroy($id);

            return response()->json(['success' => true, 'message' => 'Berhasil deleted data']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }
}
