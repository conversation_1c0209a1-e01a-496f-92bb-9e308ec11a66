<?php

namespace App\Workshop;

use App\FleetMaster\Vehicle;
use App\Models\Fleet\AnswerQuestionUser;
use App\Models\Fleet\Asset;
use App\Models\Fleet\KategoriKerusakan;
use App\Models\Fleet\StatusPerbaikan;
use App\Models\Fleet\WorkshopDecision;
use App\Models\Fleet\WorkshopDetail;
use App\Models\Fleet\WorkshopFinalDecision;
use App\Models\Fleet\WorkshopInspection;
use App\Models\Fleet\WorkshopSla;
use App\Models\User;
use Illuminate\Database\Eloquent\Model;

class Workshop extends Model
{

    protected $fillable = ['status_perbaikan_id', 'penetapan_sla', 'pemeriksaan_selesai', 'km_service', 'type', 'accident_id', 'emergency_id', 'service'];

    public function vehicle()
    {
        return $this->belongsTo(Vehicle::class,'vehicle_id');
    }

    public function driver()
    {
        return $this->belongsTo(User::class,'driver_id');
    }

    public function technical()
    {
        return $this->belongsTo(User::class,'approval_by');
    }

    public function kategoriKerusakan()
    {
        return $this->belongsTo(KategoriKerusakan::class, 'kategori_kerusakan_id', 'id');
    }

    public function answerQuestionUser()
    {
        return $this->belongsTo(AnswerQuestionUser::class, 'answer_question_user_id', 'id');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public function workshopDetail()
    {
        return $this->hasMany(WorkshopDetail::class);
    }

    public function workshopSla()
    {
        return $this->hasMany(WorkshopSla::class);
    }

    public function asset()
    {
        return $this->belongsTo(Asset::class, 'asset_id', 'id');
    }

    public function workshopDecision()
    {
        return $this->hasOne(WorkshopDecision::class);
    }

    public function statusPerbaikan()
    {
        return $this->belongsTo(StatusPerbaikan::class, 'status_perbaikan_id', 'id');
    }

    public function WorkshopFinalDecision()
    {
        return $this->hasMany(WorkshopFinalDecision::class);
    }
    
    public function workshopInspection()
    {
        return $this->hasMany(WorkshopInspection::class);
    }
}
