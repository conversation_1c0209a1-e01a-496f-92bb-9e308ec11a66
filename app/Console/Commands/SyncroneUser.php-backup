<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Fleet\UserLjr;
use App\Models\Fleet\Driver;
use App\Models\Fleet\TypeSim;
use App\Models\Fleet\MasterLocation;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class SyncroneUser extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'synchrone:user';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Synchrone User & Driver from hrm api';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $synchrone = $this->hrmAll();
    }

    public function hrmAll()
    {
        // $response = Http::get('http://lestarijayaraya.co.id:8009/api/karyawan/api_tampil?api_key=Y4BoXmabMlK7BQx&employee_status=0');
        $response = Http::get('http://*********:8009/api/karyawan/api_tampil?api_key=Y4BoXmabMlK7BQx&employee_status=0');
        if (!$response->successful()) {
            return false;
        }
        // $response2 = Http::get('http://lestarijayaraya.co.id:8009/api/karyawan/api_tampil?api_key=Y4BoXmabMlK7BQx&employee_status=1');
        $response2 = Http::get('http://*********:8009/api/karyawan/api_tampil?api_key=Y4BoXmabMlK7BQx&employee_status=1');
        if (!$response2->successful()) {
            return false;
        }

        $results = array_merge($response->json(), $response2->json());

        if (is_array($results) && count($results)) {
            $user_exist = [];
            $no = 1;
            foreach ($results as $key => $result) {
                if (isset($result['user_id']) && !is_null($result['user_id']) && $result['employee_status'] == 'AKTIF') {
                    $cek_user = User::where('code',$result['user_id'])->first();
                    if ($cek_user) {
                        $user = User::find($cek_user->id);
                    }else{
                        $user = new User;
                        $user->code = $result['user_id'];

                        //make username
                        $lower = strtolower(str_replace(' ', '', $result['full_name']));
                        $birth = $result['born_date'];
                        if ($birth == '0000-00-00') {
                            $birth = '000000';
                        }else{
                            $birth = Carbon::parse($birth)->format('dmy');
                        }
                        $username = substr($lower, 0, 7).$birth;
                        $userDuplicate = User::where('username', 'LIKE', '%'.$username.'%')->count();
                        if ($userDuplicate >= 1) {
                            $username = $username.($userDuplicate+1);
                        }

                        $check_user_again = User::where('username', $username)->count();
                        if ($check_user_again) {
                            $username = substr($lower, 0, 7).substr($result['nip'], 0,4);
                        }

                        $role_name = $result['jabatan_name'];
                        if (strpos($result['jabatan_name'], 'Driver') !== false) {
                            $role_name = 'Driver';
                        }
                        if (strpos($result['jabatan_name'], 'driver') !== false) {
                            $role_name = 'Driver';
                        }   
                        $user->role = $role_name;    

                        $user->password = Hash::make('123456');
                        $user->username = $username;
                    }

                    $user->nip = $result['nip'];
                    $user->email = $result['email'];
                    $user->nik = $result['nik'];
                    $user->full_name = trim($result['full_name']);
                    $user->company_id = $result['company_id'];
                    $user->company_name = $result['company_name'];
                    $user->department_id = $result['department_id'];
                    $user->department_name = $result['department_name'];
                    $user->location_type_id = $result['location_type_id'];
                    $user->location_type_name = $result['location_type_name'];
                    $user->location_id = $result['location_id'];
                    $user->location_name = $result['location_name'];
                    $user->job_id = $result['job_id'];
                    $user->jabatan_name = $result['jabatan_name'];
                    $user->section_id = $result['section_id'];
                    $user->section_name = $result['section_name'];
                    $user->sub_section_id = $result['sub_section_id'];
                    $user->sub_section_name = $result['sub_section_name'];
                    $user->blood_type = $result['blood_type'];
                    $user->emergency_number = $result['emergency_number'];
                    $user->birth = $result['born_date'];
                    $user->type_driving_license = $result['type_driving_license'];
                    $user->driving_license_number = $result['driving_license_number'];
                    $user->validity_driving_license = $result['validity_driving_license'];
                    $user->avatar = $result['avatar'];
                    $user->age = $result['age'];
                    $user->employee_status = $result['employee_status'];
                    $user->ket_update = $result['ket_update'];
                    
                    //pisah name
                    $pisah_name = explode(" ",trim($result['full_name']));
                    $firstname = "";
                    $lastname = "";
                    if (is_array($pisah_name) && count($pisah_name) > 0) {
                        for ($in=0; $in < count($pisah_name); $in++) { 
                            if ($in == 0) {
                                $firstname = $pisah_name[$in];
                            }else{
                                $lastname = $pisah_name[$in].' ';
                            }
                        }
                    }else{
                        $firstname = $result['full_name'];
                        $lastname = "";
                    }
                    $user->first_name = $firstname;
                    $user->last_name = $lastname;         
                    $user->save();
                    $this->info('user '.$no++);

                    $user_exist[$user->id] = $user->id;
                }
            }

            $delete_user = User::whereNotIn('id',$user_exist)->delete();
            Log::info('SYNCHRONE USER PROCESS');
            $this->updateDriver();
        }else{
            return false;
        }
    }

    public function updateDriver(){
        $users = User::where('jabatan_name','ilike','%driver%')->get();

        $driver_exist = [];

        foreach ($users as $key => $user) {
            $cek_driver = Driver::where('user_id',$user->id)->first();
            if ($cek_driver) {
                $driver = Driver::find($cek_driver->id);
            }else{
                $driver = new Driver;
                $driver->user_id = $user->id;
                $driver->join_date = Carbon::now();
            }

            $driver->emergency_contact = $user->emergency_number;
            $driver->status	= $user->employee_status == 'AKTIF' ? 1 : 0;
            $driver->available	= $user->employee_status == 'AKTIF' ? 1 : 0;
            $driver->emergency_name = "";
            $driver->sim_validity_period = $user->validity_driving_license;
            $driver->sim_validity_period = $user->validity_driving_license == '0000-00-00' ? NULL : $user->validity_driving_license;
            $driver->driving_license = $user->driving_license_number;
            $driver->birth = $user->birth == '0000-00-00' ? NULL : $user->birth;

            $type_sim = TypeSim::where('name', $user->type_driving_license)->first();
            if ($type_sim) {
                $driver->type_sim_id = $type_sim->id;
            }else{
                $driver->type_sim_id = NULL;
            }

            $master_location = MasterLocation::where('code', $user->location_id)->first();
            if ($master_location) {
                $driver->master_location_id = $master_location->id;
            }else{
                $driver->master_location_id = NULL;
            }

            $driver->save();
            $this->info('driver '.$key);
            $driver_exist[$driver->id] = $driver->id; 
        }

        $driver_exist = Driver::whereNotIn('id',$driver_exist)->delete();
        Log::info('SYNCHRONE DRIVER PROCESS');

        return true;
    }
}
