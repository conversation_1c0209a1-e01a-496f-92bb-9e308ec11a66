<?php

namespace App\Console\Commands;

use App\Models\Fleet\CategoryPartner;
use App\Models\Fleet\Company;
use App\Models\Fleet\Department;
use Illuminate\Console\Command;
use App\Models\Fleet\UserLjr;
use App\Models\Fleet\Driver;
use App\Models\Fleet\Experience;
use App\Models\Fleet\TypeSim;
use App\Models\Fleet\MasterLocation;
use App\Models\Fleet\PartnerTransaction;
use App\Models\Fleet\Section;
use App\Models\Fleet\SubSection;
use App\Models\Fleet\Supplier;
use App\Models\Fleet\SupplierContact;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Http;

class SyncroneUser extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'synchrone:user';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Synchrone User & Driver from hrm api';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $synchrone = $this->hrmAll();
    }

    public function hrmAll()
    {
        // $response = Http::get('http://lestarijayaraya.co.id:8009/api/karyawan/api_tampil?api_key=Y4BoXmabMlK7BQx&employee_status=0');
        $response = Http::get('http://*********:8009/api/karyawan/api_tampil?api_key=Y4BoXmabMlK7BQx&employee_status=0');
        if (!$response->successful()) {
            return false;
        }
        // $response2 = Http::get('http://lestarijayaraya.co.id:8009/api/karyawan/api_tampil?api_key=Y4BoXmabMlK7BQx&employee_status=1');
        $response2 = Http::get('http://*********:8009/api/karyawan/api_tampil?api_key=Y4BoXmabMlK7BQx&employee_status=1');
        if (!$response2->successful()) {
            return false;
        }

        $company_id = [];
        $department_id = [];
        $location_id = [];
        $section_id = [];
        $sub_section_id = [];

        $results = array_merge($response->json(), $response2->json());

        if (is_array($results) && count($results)) {
            $user_exist = [];
            $no = 1;
            foreach ($results as $key => $result) {
                if (isset($result['user_id']) && !is_null($result['user_id']) && $result['employee_status'] == 'AKTIF') {
                    $cek_user = User::where('code',$result['user_id'])->first();
                    if ($cek_user) {
                        $user = User::find($cek_user->id);
                    }else{
                        $user = new User;
                        $user->code = $result['user_id'];

                        //make username
                        $lower = strtolower(str_replace(' ', '', $result['full_name']));
                        $birth = $result['born_date'];
                        if ($birth == '0000-00-00') {
                            $birth = '000000';
                        }else{
                            $birth = Carbon::parse($birth)->format('dmy');
                        }
                        $username = substr($lower, 0, 7).$birth;
                        $userDuplicate = User::where('username', 'LIKE', '%'.$username.'%')->count();
                        if ($userDuplicate >= 1) {
                            $username = $username.($userDuplicate+1);
                        }

                        $check_user_again = User::where('username', $username)->count();
                        if ($check_user_again) {
                            $username = substr($lower, 0, 7).substr($result['nip'], 0,4);
                        }

                        if (strpos($result['jabatan_name'], 'Driver') !== false) {
                            $role_name = $result['jabatan_name'];
                            $role_name = 'Driver';
                        }
                        if (strpos($result['jabatan_name'], 'driver') !== false) {
                            $role_name = $result['jabatan_name'];
                            $role_name = 'Driver';
                        }
                        $user->role = $role_name ?? '';

                        $user->password = Hash::make('123456');
                        $user->username = $username;
                    }

                    $company_id[$key] = $result['company_id'];
                    $department_id[$key] = $result['department_id'];
                    $location_id[$key] = $result['location_id'];
                    $section_id[$key] = $result['section_id'];
                    $sub_section_id[$key] = $result['sub_section_id'];

                    $user->nip = $result['nip'];
                    $user->email = $result['email'];
                    $user->nik = $result['nik'];
                    $user->full_name = trim($result['full_name']);
                    $user->company_id = $result['company_id'];
                    $user->company_name = $result['company_name'];
                    $user->department_id = $result['department_id'];
                    $user->department_name = $result['department_name'];
                    $user->location_type_id = $result['location_type_id'];
                    $user->location_type_name = $result['location_type_name'];
                    $user->location_id = $result['location_id'];
                    $user->location_name = $result['location_name'];
                    $user->job_id = $result['job_id'];
                    $user->jabatan_name = $result['jabatan_name'];
                    $user->section_id = $result['section_id'];
                    $user->section_name = $result['section_name'];
                    $user->sub_section_id = $result['sub_section_id'];
                    $user->sub_section_name = $result['sub_section_name'];
                    $user->blood_type = $result['blood_type'];
                    $user->emergency_number = $result['emergency_number'];
                    $user->birth = $result['born_date'];
                    $user->type_driving_license = $result['type_driving_license'];
                    $user->driving_license_number = $result['driving_license_number'];
                    $user->validity_driving_license = $result['validity_driving_license'];
                    $user->avatar = $result['avatar'];
                    $user->age = $result['age'];
                    $user->employee_status = $result['employee_status'];
                    $user->ket_update = $result['ket_update'];

                    //pisah name
                    $pisah_name = explode(" ",trim($result['full_name']));
                    $firstname = "";
                    $lastname = "";
                    if (is_array($pisah_name) && count($pisah_name) > 0) {
                        for ($in=0; $in < count($pisah_name); $in++) {
                            if ($in == 0) {
                                $firstname = $pisah_name[$in];
                            }else{
                                $lastname = $pisah_name[$in].' ';
                            }
                        }
                    }else{
                        $firstname = $result['full_name'];
                        $lastname = "";
                    }
                    $user->first_name = $firstname;
                    $user->last_name = $lastname;
                    $user->save();
                    $this->info('user '.$no++);

                    $user_exist[$user->id] = $user->id;
                }
            }

            $delete_user = User::whereNotIn('id',$user_exist)->delete();
            $this->updateDriver();
            $this->updateCompany($company_id);
            $this->updateDepartment($department_id);
            $this->updateLocation($location_id);
            $this->updateSection($section_id);
            $this->updateSubSection($sub_section_id);

            try {
                $this->syncApiSupplier();
            } catch (\Exception $e) {
                $this->info($e);
            }
        }else{
            return false;
        }
    }

    public function updateDriver(){
        $users = User::where('role','ilike','%driver%')->get();

        $driver_exist = [];

        foreach ($users as $key => $user) {
            $cek_driver = Driver::where('user_id',$user->id)->first();
            if ($cek_driver) {
                $driver = Driver::find($cek_driver->id);
            }else{
                $driver = new Driver;
                $driver->user_id = $user->id;
                $driver->join_date = Carbon::now();
            }

            $driver->emergency_contact = $user->emergency_number;
            $driver->status	= $user->employee_status == 'AKTIF' ? 1 : 0;
            $driver->available	= $user->employee_status == 'AKTIF' ? 1 : 0;
            $driver->emergency_name = "";
            $driver->sim_validity_period = $user->validity_driving_license;
            $driver->sim_validity_period = $user->validity_driving_license == '0000-00-00' ? NULL : $user->validity_driving_license;
            $driver->driving_license = $user->driving_license_number;
            $driver->birth = $user->birth == '0000-00-00' ? NULL : $user->birth;

            $type_sim = TypeSim::where('name', $user->type_driving_license)->first();
            if ($type_sim) {
                $driver->type_sim_id = $type_sim->id;
            }else{
                $driver->type_sim_id = NULL;
            }

            $master_location = MasterLocation::where('code', $user->location_id)->first();
            if ($master_location) {
                $driver->master_location_id = $master_location->id;
            }else{
                $driver->master_location_id = NULL;
            }

            $driver->save();
            $this->info('driver '.$key);
            $driver_exist[$driver->id] = $driver->id;
        }

        $driver_exist = Driver::whereNotIn('id',$driver_exist)->delete();

        return true;
    }

    public function updateCompany($company_id)
    {
        $company_id = array_unique($company_id);
        $company_exist = [];
        foreach ($company_id as $key => $value) {
            $cek_company = Company::where('code',$value)->first();
            if ($cek_company) {
                $company = Company::find($cek_company->id);
            }else{
                $company = new Company;
                $company->code = $value;
            }

            $company_name = User::where('company_id',$value)->first()->company_name;
            $company->name = $company_name;
            $company->save();
            $this->info('company '.$key);
            $company_exist[$company->id] = $company->id;
        }

        $company_exist = Company::whereNotIn('id',$company_exist)->delete();

        return true;
    }

    public function updateDepartment($department_id)
    {
        $department_id = array_unique($department_id);
        $department_exist = [];
        foreach ($department_id as $key => $value) {
            $cek_department = Department::where('code',$value)->first();
            if ($cek_department) {
                $department = Department::find($cek_department->id);
            }else{
                $department = new Department;
                $department->code = $value;
            }

            $department_name = User::where('department_id',$value)->first()->department_name;
            $department->name = $department_name;
            $department->save();
            $this->info('department '.$key);
            $department_exist[$department->id] = $department->id;
        }

        $department_exist = Department::whereNotIn('id',$department_exist)->delete();

        return true;
    }

    public function updateLocation($location_id)
    {
        $location_id = array_unique($location_id);
        $location_exist = [];
        foreach ($location_id as $key => $value) {
            $cek_location = MasterLocation::where('code',$value)->first();
            if ($cek_location) {
                $location = MasterLocation::find($cek_location->id);
            }else{
                $location = new MasterLocation;
                $location->code = $value;
            }

            $location_name = User::where('location_id',$value)->first()->location_name;
            $location->name = $location_name;
            $location->save();
            $this->info('location '.$key);
            $location_exist[$location->id] = $location->id;
        }

        $location_exist = MasterLocation::whereNotIn('id',$location_exist)->delete();

        return true;
    }

    public function updateSection($section_id)
    {
        $section_id = array_unique($section_id);
        $section_exist = [];
        foreach ($section_id as $key => $value) {
            $cek_section = Section::where('code',$value)->first();
            if ($cek_section) {
                $section = Section::find($cek_section->id);
            }else{
                $section = new Section;
                $section->code = $value;
            }

            $section_name = User::where('section_id',$value)->first()->section_name ?? '';
            $section->name = $section_name;
            $section->save();
            $this->info('section '.$key);
            $section_exist[$section->id] = $section->id;
        }

        $section_exist = Section::whereNotIn('id',$section_exist)->delete();

        return true;
    }

    public function updateSubSection($sub_section_id)
    {
        $sub_section_id = array_unique($sub_section_id);
        $sub_section_exist = [];
        foreach ($sub_section_id as $key => $value) {
            $cek_sub_section = SubSection::where('code',$value)->first();
            if ($cek_sub_section) {
                $sub_section = SubSection::find($cek_sub_section->id);
            }else{
                $sub_section = new SubSection;
                $sub_section->code = $value;
            }

            $sub_section_name = User::where('sub_section_id',$value)->first()->sub_section_name ?? '';
            $sub_section->name = $sub_section_name;
            $sub_section->save();
            $this->info('sub_section '.$key);
            $sub_section_exist[$sub_section->id] = $sub_section->id;
        }

        $sub_section_exist = SubSection::whereNotIn('id',$sub_section_exist)->delete();

        return true;
    }

    public function syncApiSupplier()
    {
        $partner_type_count = 3;

        $supplier_exist = [];
        for ($a=1; $a <= $partner_type_count; $a++) {
            $partner_type_id = $a;

            $base_url = "http://10.1.10.26:8080/api/v1/partners?partner_type_id=$partner_type_id&pagination%5Bpage%5D=0&pagination%5Bperpage%5D=10";
            $response = Http::withHeaders([
                                'Authorization' => 'Bearer BAPYYUYgZbXUiYIT2m50hdxZXbwClSrQf1yhpY7n',
                            ])->get($base_url)->json();

            $pages = $response['meta']['pages'];

            for ($i=1; $i <= $pages; $i++) {
                $base_url_pages = "http://10.1.10.26:8080/api/v1/partners?partner_type_id=$partner_type_id&pagination%5Bpage%5D=$i&pagination%5Bperpage%5D=10";
                $response2 = Http::withHeaders([
                                'Authorization' => 'Bearer BAPYYUYgZbXUiYIT2m50hdxZXbwClSrQf1yhpY7n',
                            ])->get($base_url_pages)->json();

                foreach ($response2['data'] as $key => $value) {
                    $cek_supplier = Supplier::where('code_id', $value['id'])->first();
                    if ($cek_supplier) {
                        $supplier = Supplier::find($cek_supplier['id']);
                    }else{
                        $supplier = new Supplier;
                    }

                    $supplier->code_id = $value['id'];
                    $supplier->name = $value['name'];
                    $supplier->handphone = $value['handphone'];
                    $supplier->partner_type = $value['partner_type']['type'];
                    $supplier->npwp = $value['npwp'];
                    $supplier->bank = $value['bank'];
                    $supplier->account_number = $value['account_number'];
                    $supplier->address = $value['address'];
                    $supplier->payment_address = $value['payment_address'];
                    $supplier->terms_of_payment = $value['terms_of_payment'];
                    $supplier->owner = $value['owner'];

                    foreach ($value['experiences'] as $bkey => $bvalue) {
                        $cek_experiences = Experience::where('experience', $bvalue['experience'])->where('year', $bvalue['year'])->first();
                        if ($cek_experiences) {
                            $experiences = Experience::find($cek_experiences->id);
                        }else{
                            $experiences = new Experience;
                        }

                        $experiences->supplier_code_id = $value['id'];
                        $experiences->experience = $bvalue['experience'];
                        $experiences->year = $bvalue['year'];
                        $experiences->save();
                    }

                    $supplier->building_photo = json_encode($value['building_photo']);
                    $supplier->current_step = $value['current_step'];
                    $supplier->document_pks = $value['document_pks'];
                    $supplier->register_complete = $value['register_complete'];

                    foreach ($value['partner_transactions'] as $ckey => $cvalue) {
                        $cek_partner_transactions = PartnerTransaction::where('code_id', $cvalue['id'])->first();
                        if ($cek_partner_transactions) {
                            $partner_transactions = PartnerTransaction::find($cek_partner_transactions->id);
                        }else{
                            $partner_transactions = new PartnerTransaction;
                        }

                        $partner_transactions->supplier_code_id = $value['id'];
                        $partner_transactions->code_id = $cvalue['id'];
                        $partner_transactions->registration_code = $cvalue['registration_code'];
                        $partner_transactions->partner_id = $cvalue['partner_id'];
                        $partner_transactions->register_complete = $cvalue['register_complete'];
                        $partner_transactions->save();
                    }

                    foreach ($value['category_partner'] as $dkey => $dvalue) {
                        $cek_category_partner = CategoryPartner::where('supplier_code_id', $value['id'])->where('category_partner_id', $dvalue['id'])->first();
                        if ($cek_category_partner) {
                            $category_partner = CategoryPartner::find($cek_category_partner->id);
                        }else{
                            $category_partner = new CategoryPartner;
                        }

                        $partner_transactions->supplier_code_id = $value['id'];
                        $category_partner->category_partner_id = $dvalue['id'];
                        $category_partner->partner_type_id = $dvalue['partner_type_id'];
                        $category_partner->category_name = $dvalue['category_name'];
                        $category_partner->is_active = $dvalue['is_active'];
                        $category_partner->created_by = $dvalue['created_by'];
                        $category_partner->updated_by = $dvalue['updated_by'];
                        $category_partner->laravel_through_key = $dvalue['laravel_through_key'];
                        $category_partner->save();
                    }

                    foreach ($value['supplier_contact'] as $ekey => $evalue) {
                        $cek_supplier_contact = SupplierContact::where('code_id', $evalue['id'])->first();
                        if ($cek_supplier_contact) {
                            $supplier_contact = SupplierContact::find($cek_supplier_contact->id);
                        }else{
                            $supplier_contact = new SupplierContact;
                        }

                        $supplier_contact->supplier_code_id = $value['id'];
                        $supplier_contact->code_id = $evalue['id'];
                        $supplier_contact->partner_id = $evalue['partner_id'];
                        $supplier_contact->name = $evalue['name'];
                        $supplier_contact->email = $evalue['email'];
                        $supplier_contact->phone_1 = $evalue['phone_1'];
                        $supplier_contact->phone_2 = $evalue['phone_2'];
                        $supplier_contact->phone_3 = $evalue['phone_3'];
                        $supplier_contact->address = $evalue['address'];
                        $supplier_contact->is_active = $evalue['is_active'];
                        $supplier_contact->created_by = $evalue['created_by'];
                        $supplier_contact->updated_by = $evalue['updated_by'];

                        $supplier_contact->save();
                    }

                    $supplier->save();

                    $supplier_exist[$supplier->id] = $supplier->id;
                }
            }

        }

        $delete_supplier = Supplier::whereNotIn('id',$supplier_exist)->delete();

        return true;
    }
}
