<?php

namespace App\Models\Fleet;

use Illuminate\Database\Eloquent\Model;

class AnswerQuestionDetail extends Model
{
    protected $fillable = [
        'answer_question_user_id',
        'question_id',
        'question',
        'answer_question_id',
        'answer',
        'point',
    ];

    public function answerUser()
    {
        return $this->hasOne('answer_question_user_id', 'id');
    }
}
