<?php

namespace App\Models\Fleet;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Department extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'code',
        'division_id',
        'authoriser',
        'description',
        'company_id',
        'name',
        'code_id',
    ];

    public function division()
    {
        return $this->belongsTo('App\Models\Fleet\Divisi', 'division_id', 'id')->withDefault();
    }

    public function company()
    {
        return $this->belongsTo('App\Models\Fleet\Company', 'company_id', 'id')->withDefault();
    }

    public function user()
    {
        return $this->hasMany('App\User', 'department_id', 'id');
    }

    public function section()
    {
        return $this->hasMany('App\Models\Fleet\Section', 'department_id', 'id');
    }
}
