<?php

namespace App\Models\Fleet;

use Illuminate\Database\Eloquent\Model;

class Company extends Model
{
    protected $fillable = [
        'name',
        'gst_no',
        'company_number',
        'telephone',
        'fax',
        'email',
        'currency_id',
        'debtors_act',
        'pyt_discount_act',
        'creditors_act',
        'payroll_act',
        'grn_act',
        'exchange_diff_act',
        'purchases_exchange_diff_act',
        'retainedearnings',
        'gl_link_debtors',
        'gl_link_creditors',
        'gl_link_stock',
        'freight_act',
        'pkp',
        'pkp_date',
        'code',
        'code_number',
        'images',
        'npwp',
        'address',
    ];

    public function currency()
    {
        return $this->belongsTo('App\Models\Fleet\Currency', 'currency_id', 'id');
    }

    public function branch()
    {
        return $this->hasMany('App\Models\Fleet\Branch', 'company_id', 'id');
    }

    public function department()
    {
        return $this->hasMany('App\Models\Fleet\Department', 'company_id', 'id');
    }
}
