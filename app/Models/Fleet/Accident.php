<?php

namespace App\Models\Fleet;

use App\Models\User;
use Illuminate\Database\Eloquent\Model;

class Accident extends Model
{
    protected $fillable = ['asset_id',
                            'driver_id',
                            'long',
                            'lat',
                            'status',
                            'date_claim',
                            'date_survei',
                            'date_to_workshop',
                            'spk',
                            'finish_repair'];

    public function asset()
    {
        return $this->belongsTo(Asset::class, 'asset_id', 'id');
    }

    public function driver()
    {
        return $this->belongsTo(User::class, 'driver_id', 'id');
    }

    public function documentAccident()
    {
        return $this->hasMany(DocumentAccident::class);
    }
}
