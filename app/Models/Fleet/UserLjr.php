<?php

namespace App\Models\Fleet;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Lara<PERSON>\Passport\HasApiTokens;
use Spatie\Permission\Traits\HasRoles;
use Illuminate\Database\Eloquent\Model;

class UserLjr extends Authenticatable implements MustVerifyEmail
{
    use Notifiable, HasApiTokens, HasRoles;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'code'
        ,'email'
        ,'nip'
        ,'nik'
        ,'full_name'
        ,'first_name'
        ,'last_name'
        ,'company_id'
        ,'company_name'
        ,'department_id'
        ,'department_name'
        ,'location_type_id'
        ,'location_type_name'
        ,'location_id'
        ,'location_name'
        ,'job_id'
        ,'jabatan_name'
        ,'section_id'
        ,'section_name'
        ,'sub_section_id'
        ,'sub_section_name'
        ,'blood_type'
        ,'emergency_number'
        ,'born_date',
        'birth'
        ,'type_driving_license'
        ,'driving_license_number'
        ,'validity_driving_license'
        ,'avatar'
        ,'age'
        ,'employee_status'
        ,'ket_update'
        ,'role_id'
        ,'username'
        ,'password'
    ];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'password',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
}
