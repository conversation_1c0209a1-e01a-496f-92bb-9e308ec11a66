<?php

namespace App\Models\Fleet;

use Illuminate\Database\Eloquent\Model;

class HistoryWorkshop extends Model
{
    protected $table = 'public.history_workshops';
    protected $guarded = [];
    protected $fillable = ['status_perbaikan_id', 'note', 'workshop_id'];

    public function statusPerbaikan()
    {
        return $this->belongsTo(StatusPerbaikan::class, 'status_perbaikan_id', 'id');
    }
}
