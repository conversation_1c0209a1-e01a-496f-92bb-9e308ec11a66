<?php

namespace App\Models\Fleet;

use App\FleetMaster\VehicleKmActual;
use App\Models\User;
use Illuminate\Database\Eloquent\Model;

class AnswerQuestionUser extends Model
{
    protected $table = 'public.answer_question_users';
    protected $guarded = [];

    public function answer()
    {
        return $this->belongsTo(AnswerQuestion::class, 'answer_id', 'id');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public function question()
    {
        return $this->belongsTo(Question::class, 'question_id', 'id');
    }

    public function questionMark()
    {
        return $this->belongsTo(QuestionMark::class, 'question_mark_id', 'id');
    }

    public function answerQuestionDetail()
    {
        return $this->hasMany(AnswerQuestionDetail::class);
    }

    public function vehicle()
    {
        return  $this->belongsTo(Vehicle::class, 'vehicle_id', 'id');
    }

    public function statusVehicle()
    {
        return $this->belongsTo(StatusVehicle::class, 'status_vehicle_id', 'id');
    }

    public function riwayatTindakan()
    {
        return $this->hasMany(RiwayatAnswerQuestionUser::class, 'answer_question_user_id', 'id');
    }

    public function kmActual()
    {
        return $this->hasOne(VehicleKmActual::class);
    }

    public function asset()
    {
        return  $this->belongsTo(Asset::class, 'asset_id', 'id');
    }

    public function scopeFilter($query, $filter)
    {
        return $query->when($filter->keyword ?? false, function($query) use ($filter) {
            return $query->whereHas('user', function($query) use ($filter) {
                return $query->where(function ($query) use ($filter) {
                           $query->where('first_name', 'ilike', '%'.$filter->keyword.'%')
                                ->orWhere('last_name', 'ilike', '%'.$filter->keyword.'%');
                        });
            })->orWhereHas('asset', function($query) use ($filter) {
                return $query->whereHas('assetDetail', function($query) use ($filter) {
                    $query->where('value', 'ilike', '%'.$filter->keyword.'%');
                });
            });
        });
    }
}
