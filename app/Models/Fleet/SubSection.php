<?php

namespace App\Models\Fleet;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class SubSection extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'section_id',
        'name',
        'description',
        'code',
    ];

    public function section()
    {
        return $this->belongsTo('App\Models\Fleet\Section', 'section_id', 'id')->withDefault();
    }
}
