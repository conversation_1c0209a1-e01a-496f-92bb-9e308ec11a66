<?php

namespace App\Models\Fleet;

use App\Models\User;
use App\Workshop\Workshop;
use Illuminate\Database\Eloquent\Model;

class WorkshopDetail extends Model
{
    protected $fillable = ['user_id', 'jenis_kerusakan', 'jenis_perbaikan', 'note', 'sparepart', 'workshop_id', 'item_id', 'item_name', 'mekanik_id'];

    public function workshop()
    {
        return $this->belongsTo(Workshop::class, 'workshop_id', 'id');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }
}
