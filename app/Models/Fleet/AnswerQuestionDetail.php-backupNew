<?php

namespace App\Models\Fleet;

use Illuminate\Database\Eloquent\Model;

class AnswerQuestionDetail extends Model
{
    protected $fillable = [
        'answer_question_user_id',
        'question_id',
        'question',
        'answer_question_id',
        'answer',
        'point',
        'driver_note',
        'admin_note',
        'image',
        'status',
    ];

    public function answerUser()
    {
        return $this->hasOne(AnswerQuestionUser::class, 'id', 'answer_question_user_id');
    }

    public function questions()
    {
        return $this->belongsTo(Question::class, 'question_id', 'id');
    }

    public function answerQuestion()
    {
        return $this->belongsTo(AnswerQuestion::class, 'answer_question_id', 'id');
    }
}
