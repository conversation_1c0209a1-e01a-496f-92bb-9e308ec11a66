<?php

namespace App\Models\Fleet;

use Illuminate\Database\Eloquent\Model;

class Emergency extends Model
{
    protected $fillable = ['asset_id',
                            'driver_id',
                            'long',
                            'lat',
                            'status',
                            'finish_repair'];

    public function asset()
    {
        return $this->belongsTo(Asset::class, 'asset_id', 'id');
    }

    public function driver()
    {
        return $this->belongsTo(User::class, 'driver_id', 'id');
    }
}
