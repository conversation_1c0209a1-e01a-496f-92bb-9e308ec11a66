<?php

namespace App\Models\Fleet;

use Illuminate\Database\Eloquent\Model;

class StockMaster extends Model
{
    public function groupBarang()
    {
        return $this->belongsTo(StockMasterGroup::class, 'group_id', 'id')->withDefault();
    }

    public function classBarang()
    {
        return $this->belongsTo(StockMasterClass::class, 'class_id', 'id')->withDefault();
    }

    public function stockCategoryGNRIVT()
    {
        return $this->belongsTo(StockCategory::class, 'category_id', 'code')->withDefault();
    }
    
    public function UOM()
    {
        return $this->belongsTo(UnitOfMeasure::class, 'units', 'id')->withDefault();
    }

    public function taxCategory()
    {
        return $this->belongsTo(TaxCategory::class, 'taxcat_id', 'id')->withDefault();
    }
}