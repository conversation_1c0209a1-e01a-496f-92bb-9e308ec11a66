<?php

namespace App\Models\Fleet;

use App\Workshop\Workshop;
use Illuminate\Database\Eloquent\Model;

class WorkshopSla extends Model
{
    protected $fillable = ['status'
                            ,'workshop_id'
                            ,'approve_id'
                            ,'mekanik_id'
                            ,'note'
                            ,'tingkat_kerusakan'
                            ,'estima<PERSON>_pengerjaan'
                            ,'rincian_pengerjaan'
                            ,'work_estimate_id'];

    public function workshop()
    {
        return $this->belongsTo(Workshop::class, 'workshop_id', 'id');
    }

    public function workEstimate()
    {
        return $this->belongsTo(WorkEstimate::class, 'work_estimate_id', 'id');
    }
}
