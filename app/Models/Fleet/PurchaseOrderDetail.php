<?php

namespace App\Models\Fleet;

use Illuminate\Database\Eloquent\Model;

class PurchaseOrderDetail extends Model
{
    protected $fillable = [
        'request_form_detail_id',
        'type_item_id',
        'qty',
        'unit_price',
        'amount',
        'purchase_order_id',
    ];

    public function purchaseOrder()
    {
        return $this->belongsTo(PurchaseOrder::class, 'purchase_order_id', 'id');
    }

    public function requestFormDetail()
    {
        return $this->belongsTo(RequestFormDetail::class, 'request_form_detail_id', 'id');
    }

    public function typeItem()
    {
        return $this->belongsTo(TypeItem::class, 'type_item_id', 'id');
    }
}
