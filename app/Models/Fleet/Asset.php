<?php

namespace App\Models\Fleet;

use App\Workshop\Insurance;
use App\Models\Fleet\HandOverDetail;
use Illuminate\Database\Eloquent\Model;
use App\Models\Fleet\PurchaseOrderDetail;
use App\Models\User;
use App\Workshop\Workshop;
use IntlChar;

class Asset extends Model
{
    protected $table = 'public.assets';
    protected $guarded = [];
    protected $fillable = ['id','code', 'company', 'created_asset', 'pickup_date', 'category_asset_id', 'category_item_id','type_asset_id', 'km_actual',
                        'company_id', 'department_id', 'location_id', 'maintenance', 'maintenance_odometer', 'trip', 'user_id', 'insurance_id',
                        'section_id', 'detail_location', 'good_receive_date', 'spesification', 'used', 'purchase_price_no_ppn', 'upload_doc', 'type_item_id', 'status_asset',
                        'purchase_order_detail_id', 'pic_used_user_id', 'uom', 'issued_asset_code','doc_aset','tahun_bulan_aset', 'code_type_aset'];

    protected $appends = ['id_items'];

    public function getIdItemsAttribute()
    {
        $code_jenis = 1;
        $category_code = $this->categoryAsset ? str_replace(0, '', $this->categoryAsset->code) : '';
        $code = $this->typeAsset->code ?? '';

        $id_item = $code_jenis . $category_code . $code;

        return $id_item;
    }

    public function uploadDoc()
    {
        return $this->belongsTo('App\Models\Fleet\UploadDoc', 'upload_doc', 'id');
    }

    public function workshop()
    {
        return $this->hasMany(Workshop::class, 'asset_id', 'id');
    }

    public function po()
    {
        return $this->belongsTo('App\Models\GA\PurchaseOrder', 'po_id', 'id');
    }

    public function pr()
    {
        return $this->belongsTo('App\Models\GA\StockRequest', 'pr_id', 'id');
    }

    public function stock()
    {
        return $this->belongsTo('App\Models\Fleet\StockMaster', 'stock_id', 'id');
    }

    public function createdBy()
    {
        return $this->belongsTo('App\Models\User', 'created_by', 'id')->withDefault();
    }

    public function requester()
    {
        return $this->belongsTo('App\Models\User', 'requester', 'id')->withDefault();
    }

    public function onHand()
    {
        return $this->belongsTo('App\Models\User', 'on_hand_id', 'id')->withDefault();
    }

    public function group()
    {
        return $this->belongsTo('App\Models\Fleet\AssetMasterGroup', 'group_id', 'id');
    }

    public function class()
    {
        return $this->belongsTo('App\Models\Fleet\AssetMasterClass', 'class_id', 'id');
    }

    public function categoryAsset()
    {
        return $this->belongsTo(CategoryAsset::class, 'category_asset_id', 'id');
    }

    public function categoryItem()
    {
        return $this->belongsTo(CategoryItem::class, 'category_item_id', 'id');
    }

    public function typeAsset()
    {
        return $this->belongsTo(TypeAsset::class, 'type_asset_id', 'id');
    }

    public function assetDetail()
    {
        return $this->hasMany(AssetDetail::class);
    }

    public function companies()
    {
        return $this->belongsTo(Company::class, 'company_id', 'id');
    }

    public function department()
    {
        return $this->belongsTo(Department::class, 'department_id', 'id');
    }

    public function location()
    {
        return $this->belongsTo(MasterLocation::class, 'location_id', 'id');
    }

    public function user_pic1()
    {
        return $this->belongsTo('App\Models\User', 'pic_1', 'id')->withDefault();
    }

    public function user_pic2()
    {
        return $this->belongsTo('App\Models\User', 'pic_2', 'id')->withDefault();
    }

    public function user()
    {
        return $this->belongsTo('App\Models\User', 'user_id', 'id')->withDefault();
    }

    public function insurance()
    {
        return $this->belongsTo(Insurance::class, 'insurance_id', 'id');
    }

    public function section()
    {
        return $this->belongsTo(Section::class, 'section_id', 'id');
    }

    public function detailAsset()
    {
        return $this->hasMany(AssetDetail::class, 'asset_id', 'id');
    }

    public function typeItem()
    {
        return $this->belongsTo(TypeItem::class, 'type_item_id', 'id');
    }

    public function detailLocation()
    {
        return $this->belongsTo(DetailLocation::class, 'detail_location', 'id');
    }

    public function handoverDetail()
    {
        return $this->hasOne(HandOverDetail::class, 'asset_id', 'id');
    }

    public function SOAsset()
    {
        return $this->hasOne(SOAsset::class, 'asset_id', 'id');
    }

    public function Area()
    {
        return $this->belongsTo(Area::class, 'area_id', 'id');
    }

    public function purchaseOrderDetail()
    {
        return $this->belongsTo(PurchaseOrderDetail::class, 'purchase_order_detail_id');
    }

    public function MappingSO()
    {
        return $this->hasOne(MappingSO::class, 'asset_id', 'id');
    }

    public function AnswerQuestionUser()
    {
        return $this->hasMany(AnswerQuestionUser::class, 'asset_id', 'id');
    }

    public function picUsedUser()
    {
        return $this->belongsTo(User::class, 'pic_used_user_id', 'id');
    }

    public function AssetSecurityLog()
    {
        return $this->hasMany(AssetSecurityLog::class, 'asset_id', 'id');
    }

    public function scopeFilter($query, $filter)
    {
        return $query->when($filter->category ?? false, function($query) use ($filter) {
            $query->whereHas('categoryItem', function($query) use($filter){
                return $query->where('name', 'ilike',$filter->category);
            });
        })->when($filter->keywoard ?? false, function($query) use ($filter) {
            return $query->where('code', 'like', "%$filter->keywoard%")
                         ->where('code', 'like', "%$filter->keywoard%");
        })->when($filter->no_aset ?? false, function($query) use ($filter) {
            return $query->whereRaw('LOWER(code) LIKE ?', ['%' . strtolower($filter->no_aset) . '%']);
        })->when($filter->category_asset ?? false, function($query) use ($filter) {
            return $query->where('category_asset_id', $filter->category_asset);
        })->when($filter->type_asset ?? false, function($query) use ($filter) {
            return $query->where('type_asset_id', $filter->type_asset);
        })->when($filter->company ?? false, function($query) use ($filter) {
            return $query->where('company_id', $filter->company);
        })->when($filter->section ?? false, function($query) use ($filter) {
            return $query->where('section_id', $filter->section);
        })->when($filter->department ?? false, function($query) use ($filter) {
            return $query->where('department_id', $filter->department);
        })->when($filter->location ?? false, function($query) use ($filter) {
            return $query->where('location_id', $filter->location);
        })->when($filter->detail_location ?? false, function($query) use ($filter) {
            return $query->where('detail_location', $filter->detail_location);
        })->when($filter->no_asset ?? false, function($query) use ($filter) {
            return $query->where('code', $filter->no_asset );
        })->when($filter->type_item ?? false, function($query) use ($filter) {
            return $query->where('type_item_id', $filter->type_item );
        })->when($filter->start_date ?? false  || $filter->to_date ?? false, function ($query) use ($filter) {
            return $query->whereBetween('good_receive_date', [$filter->start_date, $filter->to_date]);
        })->when($filter->nopol ?? false, function($query) use ($filter) {
            $query->whereHas('assetDetail', function($query) use($filter){
                return $query->where('attribute_code', 'nomor_polisi')
                ->where('value', 'like', "%$filter->nopol%");
            });
        })->when($filter->merk ?? false, function($query) use ($filter) {
            $query->whereHas('assetDetail', function($query) use($filter){
                return $query->where('attribute_code', 'merk_kendaraan')
                ->where('value', 'like', "%$filter->merk%");
            });
        })->when($filter->no_jenis ?? false, function($query) use ($filter) {
            $query->whereHas('assetDetail', function($query) use($filter){
                return $query->where('attribute_code', 'type_kendaraan')
                ->where('value', 'like', "%$filter->no_jenis%");
            });
        })->when($filter->group ?? false, function($query) use ($filter) {
            $query->whereHas('MappingSO', function($query) use($filter){
                return $query->where('group', $filter->group);
            });
        })->when($filter->area ?? false, function($query) use ($filter) {
            $query->whereHas('MappingSO', function($query) use($filter){
                return $query->where('area', $filter->area);
            });
        })->when($filter->code_type_aset ?? false, function($query) use ($filter) {
            $query->where('code_type_aset', intval($filter->code_type_aset));
        })->when($filter->type_sim ?? false, function($query) use ($filter) {
            $query->whereHas('typeItem', function($query) use($filter){
                return $query->whereHas('simVehicle', function($query) use($filter){
                    return $query->where('type_sim', 'ilike', "%$filter->type_sim%");
                });
            });
        })->when($filter->keyword ?? false, function($query) use ($filter) {
            return $query->where('code', 'ilike', "%$filter->keyword%")
                          ->orWhere(function($query) use ($filter) {
                            return $query->whereHas('typeItem', function($query) use($filter){
                                return $query->where('name', 'ilike', "%$filter->keyword%");
                            });
                          })->orWhere(function($query) use ($filter) {
                            return $query->whereHas('categoryitem', function($query) use($filter){
                                return $query->where('name', 'ilike', "%$filter->keyword%");
                            });
                          });
        })->when($filter->category_item_id ?? false, function($query) use ($filter) {
            return $query->where('category_item_id', $filter->category_item_id);
        })->when($filter->category_asset_name ?? false, function($query) use ($filter) {
            return $query->whereHas('categoryAsset', function($query) use($filter){
                return $query->where('name', 'ilike', "%$filter->category_asset_name%");
            });
        });
        // ->when(($filter->start_date_asc ?? false) || ($filter->to_date_asc ?? false), function($query) use ($filter) {
        //     $query->whereHas('assetDetail', function($query) use($filter){
        //         return $query->when($filter->start_date_asc ?? false, function($query) use ($filter) {
        //             return $query->where('attribute_code', 'mulai_berlaku_polis');
        //                         //  ->whereDate('value','>=', $filter->start_date_asc);
        //         })->when($filter->to_date_asc ?? false, function($query) use ($filter) {
        //             // return $query->where('attribute_code', 'mulai_berakhir_polis');
        //                         //  ->whereDate('value','<=', $filter->to_date_asc);
        //         });
        //     });
        // });
    }
}
