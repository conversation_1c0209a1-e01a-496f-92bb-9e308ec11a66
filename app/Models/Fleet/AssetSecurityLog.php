<?php

namespace App\Models\Fleet;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class AssetSecurityLog extends Model
{
    use SoftDeletes;
    
    protected $table = 'public.asset_security_logs';
    protected $guarded = [];

    protected $casts = [
        'log_description' => 'array',
    ];

    public function user()
    {
        return $this->belongsTo('App\Models\User', 'user_id', 'id')->withDefault();
    }

    public function asset()
    {
        return $this->belongsTo(Asset::class, 'asset_id', 'id');
    }
}
