<?php

namespace App\Models\Fleet;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class LocStock extends Model
{
    use SoftDeletes;
    
    protected $table = 'public.loc_stocks';
    protected $guarded = [];

    public function location()
    {
        return $this->belongsTo('App\Models\Fleet\Location', 'location_id', 'id');
    }

    public function stock()
    {
        return $this->belongsTo('App\Models\Fleet\StockMaster', 'stock_id', 'id');
    }
}
