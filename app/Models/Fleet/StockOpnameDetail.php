<?php

namespace App\Models\Fleet;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class StockOpnameDetail extends Model
{
    use SoftDeletes;
    
    protected $table = 'public.stock_opname_details';
    protected $guarded = [];

    public function stockOpname()
    {
        return $this->belongsTo('App\Models\Fleet\StockOpname', 'stock_opname_id', 'id');
    }

    public function locStock()
    {
        return $this->belongsTo('App\Models\Fleet\LocStock', 'locstock_id', 'id');
    }
}
