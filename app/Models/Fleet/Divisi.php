<?php

namespace App\Models\Fleet;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Divisi extends Model
{
    use SoftDeletes;
    
    protected $table = 'public.divisions';
    protected $guarded = [];

    public function branch()
    {
        return $this->belongsTo('App\Models\Fleet\Branch', 'branch_id', 'id');
    }

    public function departement()
    {
        return $this->hasMany('App\Models\Fleet\Department', 'division_id', 'id');
    }
}
