<?php

namespace App\Models\Fleet;

use App\Models\User;
use App\Workshop\Workshop;
use Illuminate\Database\Eloquent\Model;

class RequestForm extends Model
{
    protected $fillable = ['company_id',
                            'department_id',
                            'section_id',
                            'location_id',
                            'nopol',
                            'workshop_id',
                            'user_id',
                            'type_submission',
                            'status_approval',
                            'verified',
                            'no_rf',
                            'no_out',
                            'no_pr',
                            'status_approval_pr_ga',
                            'status_approval_pr_direktur',
                            'no_out',
                            'total_price',
                            'create_pr',];

    protected $appends = ['created'];

    public function getCreatedAttribute()
    {
        return date('d-m-Y H:i:s', strtotime($this->created_at));
    }

    public function workshop()
    {
        return $this->belongsTo(Workshop::class, 'workshop_id', 'id');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public function company()
    {
        return $this->belongsTo(Company::class, 'company_id', 'id');
    }

    public function department()
    {
        return $this->belongsTo(Department::class, 'department_id', 'id');
    }

    public function section()
    {
        return $this->belongsTo(Section::class, 'section_id', 'id');
    }

    public function location()
    {
        return $this->belongsTo(MasterLocation::class, 'location_id', 'id');
    }

    public function requestFormDetail()
    {
        return $this->hasMany(RequestFormDetail::class)->whereNotNull('category_item_id')->whereNotNull('type_item_id');
    }

    public function handover()
    {
        return $this->hasOne(HandOver::class, 'request_form_id', 'id');
    }

    public function scopeFilter($query, $filter)
    {
        return $query->when($filter->start_date ?? false, function ($query) use ($filter) {
            return $query->whereDate('created_at', '>=', $filter->start_date);
        })->when($filter->to_date ?? false, function ($query) use ($filter) {
            return $query->whereDate('created_at', '<=', $filter->to_date);
        })->when($filter->no_rf ?? false, function ($query) use ($filter) {
            return $query->where('no_rf', $filter->no_rf);
        });
    }
}
