<?php

namespace App\Models\Fleet;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class SupplierType extends Model
{
    use SoftDeletes;
    
    protected $table = 'public.supplier_types';
    protected $guarded = [];

    public function category()
    {
        return $this->belongsTo('App\Models\Fleet\SupplierCategory', 'category_id', 'id')->withDefault();
    }
}
