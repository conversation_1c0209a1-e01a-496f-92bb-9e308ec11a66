<?php

namespace App\Models\GA;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ComparativeQuotationItem extends Model
{
    use SoftDeletes;

    protected $table = 'ga.comparative_quotation_items';
    protected $guarded = [];

    public function stock()
    {
        return $this->belongsTo('App\Models\Fleet\StockMaster', 'stock_id', 'id');
    }

    public function ComparativeQuotation()
    {
        return $this->belongsTo('App\Models\GA\ComparativeQuotation', 'comparative_quotation_id', 'id');
    }

    public function supplier()
    {
        return $this->belongsTo('App\Models\Fleet\Supplier', 'supplier_id', 'id');
    }

    public function detail()
    {
        return $this->belongsTo('App\Models\GA\ComparativeQuotationDetail', 'cq_detail_id', 'id');
    }
}
