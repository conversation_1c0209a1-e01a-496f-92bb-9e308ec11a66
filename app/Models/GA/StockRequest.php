<?php

namespace App\Models\GA;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class StockRequest extends Model
{
    use SoftDeletes;

    protected $table = 'ga.stock_requests';
    protected $guarded = [];

    public function department()
    {
        return $this->belongsTo('App\Models\Fleet\Department', 'department_id', 'id')->withDefault();
    }

    public function supplier()
    {
        return $this->belongsTo('App\Models\Fleet\Supplier', 'supplier_rec', 'id')->withDefault();
    }

    public function location()
    {
        return $this->belongsTo('App\Models\Fleet\Location', 'location', 'id')->withDefault();
    }

    public function category()
    {
        return $this->belongsTo('App\Models\Fleet\StockCategory', 'category_id', 'id')->withDefault();
    }

    public function history()
    {
        return $this->hasMany('App\Models\GA\StockRequestApprovalHistory', 'stockrequest_id', 'id');
    }

    public function items()
    {
        return $this->hasMany('App\Models\GA\StockRequestItems', 'dispatch_id', 'id');
    }

    public function ComparativeQuotation()
    {
        return $this->belongsTo('App\Models\GA\ComparativeQuotation', 'id', 'stock_request_id')->withDefault();
    }
}
