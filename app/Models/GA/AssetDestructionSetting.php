<?php

namespace App\Models\GA;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class AssetDestructionSetting extends Model
{
    use SoftDeletes;

    protected $table = 'ga.asset_destruction_settings';
    protected $guarded = [];

    public function role()
    {
        return $this->belongsTo('App\Models\Fleet\Role', 'role_id', 'id')->withDefault();
    }
}
