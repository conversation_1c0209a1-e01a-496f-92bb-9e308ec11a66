<?php

namespace App\Models\GA;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ComparativeQuotationDetail extends Model
{
    use SoftDeletes;

    protected $table = 'ga.comparative_quotation_details';
    protected $guarded = [];

    public function supplier()
    {
        return $this->belongsTo('App\Models\Fleet\Supplier', 'supplier_id', 'id');
    }
}
