<?php

namespace App\Models\GA;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class StockRequestApprovalHistory extends Model
{
    use SoftDeletes;

    protected $table = 'ga.stock_request_approval_histories';
    protected $guarded = [];

    public function user()
    {
        return $this->belongsTo('App\Models\User', 'approved_by', 'id')->withDefault();
    }
}
