<?php

namespace App\FleetMaster;

use App\Models\Fleet\AnswerQuestionUser;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class VehicleKmActual extends Model
{
    use SoftDeletes;
    
    protected $table = 'public.vehicle_km_actuals';
    protected $guarded = [];
    
    public function vehicle()
    {
        return $this->belongsTo(Vehicle::class, 'id_vehicle', 'id');
    }

    public function answerQuestionUser()
    {
        return $this->belongsTo(AnswerQuestionUser::class, 'answer_question_user_id', 'id');
    }
}
