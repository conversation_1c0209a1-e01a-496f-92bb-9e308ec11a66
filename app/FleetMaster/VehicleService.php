<?php

namespace App\FleetMaster;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class VehicleService extends Model
{
    use SoftDeletes;
    
    protected $table = 'public.vehicle_services';
    protected $guarded = [];

    public function type()
    {
        return $this->belongsTo('App\Workshop\WorkshopType', 'workshop_type_id', 'id')->withDefault();
    }
}
