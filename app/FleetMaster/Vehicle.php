<?php

namespace App\FleetMaster;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Vehicle extends Model
{
    protected $table = 'public.vehicles';
    protected $guarded = [];
    use SoftDeletes;
    
    public function category()
    {
        return $this->belongsTo(VehicleCategory::class,'vehicle_category_id');
    }

    public function color()
    {
        return $this->belongsTo(VehicleColor::class,'vehicle_color_id');
    }

    public function engine()
    {
        return $this->belongsTo(VehicleEngine::class,'vehicle_engine_id');
    }

    public function merk()
    {
        return $this->belongsTo(VehicleMerk::class,'vehicle_merk_id');
    }

    public function driver()
    {
        return $this->hasMany(VehicleDriver::class,'vehicle_id');
    }

    public function actual()
    {
        return $this->hasMany(VehicleKmActual::class,'id_vehicle');
    }

    public function kir()
    {
        return $this->hasMany(VehicleKir::class,'id_vehicle');
    }

    public function service()
    {
        return $this->hasMany(VehicleService::class,'id_vehicle');
    }
}
