<?php

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;

class PermissionTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $permissions = [
            'dashboard-view',
            'user-list',
            'user-create',
            'user-edit',
            'user-delete',
            'company-list',
            'company-create',
            'company-edit',
            'company-delete',
            'branch-list',
            'branch-create',
            'branch-edit',
            'branch-delete',
            'divisi-list',
            'divisi-create',
            'divisi-edit',
            'divisi-delete',
            'department-list',
            'department-create',
            'department-edit',
            'department-delete',
            'currency-list',
            'currency-create',
            'currency-edit',
            'currency-delete',
            'discount-category-list',
            'discount-category-create',
            'discount-category-edit',
            'discount-category-delete',
            'supplier-list',
            'supplier-create',
            'supplier-edit',
            'supplier-delete',
            'category-list',
            'category-create',
            'category-edit',
            'category-delete',
            'type-list',
            'type-create',
            'type-edit',
            'type-delete',
            'uom-list',
            'uom-create',
            'uom-edit',
            'uom-delete',
            'group-barang-list',
            'group-barang-create',
            'group-barang-edit',
            'group-barang-delete',
            'class-barang-list',
            'class-barang-create',
            'class-barang-edit',
            'class-barang-delete',
            'storage-location-list',
            'storage-location-create',
            'storage-location-edit',
            'storage-location-delete',
            'stock-category-list',
            'stock-category-create',
            'stock-category-edit',
            'stock-category-delete',
            'item-maintenance-list',
            'item-maintenance-create',
            'item-maintenance-edit',
            'item-maintenance-delete',
            'tax-category-list',
            'tax-category-create',
            'tax-category-edit',
            'tax-category-delete',
            'tax-group-list',
            'tax-group-create',
            'tax-group-edit',
            'tax-group-delete',
            'tax-authority-list',
            'tax-authority-create',
            'tax-authority-edit',
            'tax-authority-delete',
            'payment-term-list',
            'payment-term-create',
            'payment-term-edit',
            'payment-term-delete',
            'payment-method-list',
            'payment-method-create',
            'payment-method-edit',
            'payment-method-delete',
            'driver-list',
            'driver-create',
            'driver-edit',
            'driver-delete',
            'technical-list',
            'technical-create',
            'technical-edit',
            'technical-delete',
            'merk-kendaraan-list',
            'merk-kendaraan-create',
            'merk-kendaraan-edit',
            'merk-kendaraan-delete',
            'warna-kendaraan-list',
            'warna-kendaraan-create',
            'warna-kendaraan-edit',
            'warna-kendaraan-delete',
            'engine-kendaraan-list',
            'engine-kendaraan-create',
            'engine-kendaraan-edit',
            'engine-kendaraan-delete',
            'tipe-kendaraan-list',
            'tipe-kendaraan-create',
            'tipe-kendaraan-edit',
            'tipe-kendaraan-delete',
            'kategori-pertanyaan-list',
            'kategori-pertanyaan-create',
            'kategori-pertanyaan-edit',
            'kategori-pertanyaan-delete',
            'daftar-pertanyaan-list',
            'daftar-pertanyaan-create',
            'daftar-pertanyaan-edit',
            'daftar-pertanyaan-delete',
            'status-kendaraan-list',
            'status-kendaraan-create',
            'status-kendaraan-edit',
            'status-kendaraan-delete',
            'checklist-kendaraan-list',
            'checklist-kendaraan-create',
            'checklist-kendaraan-edit',
            'checklist-kendaraan-delete',
            'kendaraan-list',
            'kendaraan-create',
            'kendaraan-edit',
            'kendaraan-delete',
            'insurance-list',
            'insurance-create',
            'insurance-edit',
            'insurance-delete',
            'pengajuan-perbaikan-list',
            'pengajuan-perbaikan-create',
            'pengajuan-perbaikan-edit',
            'pengajuan-perbaikan-delete',
            'jenis-service-list',
            'jenis-service-create',
            'jenis-service-edit',
            'jenis-service-delete',
            'kategori-kerusakan-list',
            'kategori-kerusakan-create',
            'kategori-kerusakan-edit',
            'kategori-kerusakan-delete',
            'shipper-list',
            'shipper-create',
            'shipper-edit',
            'shipper-delete',
            'system-setting',
            'pr-setting-list',
            'pr-setting-create',
            'pr-setting-edit',
            'pr-setting-delete',
            'purchase-request-list',
            'purchase-request-create',
            'purchase-request-edit',
            'purchase-request-delete',
            'approval-pr',
            'compare-pr-list',
            'compare-pr-approve',
            'pickup-pr-list',
            'pickup-pr-create',
            'cq-setting-list',
            'cq-setting-create',
            'cq-setting-edit',
            'cq-setting-delete',
            'approval-cq',
            'po-setting-list',
            'po-setting-create',
            'po-setting-edit',
            'po-setting-delete',
            'purchase-order-list',
            'purchase-order-create',
            'purchase-order-edit',
            'purchase-order-delete',
            'approval-po',
            'receive-po',
            'payment-po',
            'list-inventory-stock',
            'transfer-stock-list',
            'transfer-stock-create',
            'group-asset-list',
            'group-asset-create',
            'group-asset-edit',
            'group-asset-delete',
            'asset-list',
            'asset-create',
            'asset-show',
            'pemusnahaan-asset-setting-list',
            'pemusnahaan-asset-setting-create',
            'pemusnahaan-asset-setting-edit',
            'pemusnahaan-asset-setting-delete',
            'pemusnahaan-asset-list',
            'pemusnahaan-asset-create',
            'pemusnahaan-asset-edit',
            'pemusnahaan-asset-delete',
            'approval-pemusnahaan-asset',
            'pemusnahaan-asset-pemasukan-list',
            'pemusnahaan-asset-pemasukan-create',
            'pemusnahaan-asset-pemasukan-edit',
            'pemusnahaan-asset-pemasukan-delete',
            'stock-opname-list',
            'stock-opname-create',
            'stock-opname-edit',
            'stock-opname-delete',
            'gps-map',
            'gps-setting',
        ];

        foreach ($permissions as $permission) {
            Permission::create(['name' => $permission, 'guard_name' => 'api']);
        }
    }
}
