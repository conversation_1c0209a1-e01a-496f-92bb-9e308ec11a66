<?php

use App\Models\Fleet\StockType;
use Illuminate\Database\Seeder;

class StockTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        StockType::create([
            "code" => "D",
            "name" => "Dummy Item - (No Movements)",
        ]);
        StockType::create([
            "code" => "F",
            "name" => "Finished Goods",
        ]);
        StockType::create([
            "code" => "L",
            "name" => "Labour",
        ]);
        StockType::create([
            "code" => "M",
            "name" => "Raw Materials",
        ]);
        StockType::create([
            "code" => "R",
            "name" => "Rent Material",
        ]);
    }
}
