<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateLocationsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('locations', function (Blueprint $table) {
            $table->id();
            $table->string('code');
            $table->string('name');
            $table->string('del_add1');
            $table->string('del_add2');
            $table->string('del_add3');
            $table->string('del_add4');
            $table->string('del_add5');
            $table->string('del_add6');
            $table->string('tel');
            $table->string('fax');
            $table->string('email');
            $table->string('contact');
            $table->string('tax_province_id');
            $table->string('cash_sale_customer')->nullable();
            $table->string('managed')->nullable();
            $table->string('cash_sale_branch')->nullable();
            $table->string('internal_request');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('locations');
    }
}
