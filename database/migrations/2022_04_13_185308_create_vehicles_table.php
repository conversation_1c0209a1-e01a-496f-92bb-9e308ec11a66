<?php

use App\FleetMaster\VehicleCategory;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateVehiclesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('vehicles', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('vehicle_category_id')->index();
            $table->unsignedBigInteger("vehicle_color_id")->index()->nullable();
            $table->unsignedBigInteger("vehicle_engine_id")->index()->nullable();
            $table->unsignedBigInteger("vehicle_merk_id")->index()->nullable();
            $table->string("tenaga_cc",100)->nullable();
            $table->char("tahun_kendaraan")->default(2012);
            $table->string("vin",100)->nullable();
            $table->string("license_no",100)->nullable();
            $table->string("jarak_tempuh_awal",100)->nullable();
            $table->string("license_expire_date",100)->nullable();
            $table->string("reg_expire_date",100)->nullable();
            $table->enum("status",[0,1])->default(1);
            $table->enum("service_status",[0,1])->default(0);
            // TAMBAHAN
            $table->string('stnk_file');
            $table->string('police_number');
            $table->string('owner');
            $table->string('owner_address')->nullable();
            // $table->string('brand_id')->nullable();
            // $table->string('type_id')->nullable();
            $table->string('kind_id')->nullable();
            $table->string('model_id')->nullable();
            $table->date('year');
            $table->string('colour_id')->nullable();
            $table->string('chasis_number');
            $table->string('machine_number_id');
            $table->string('cilinder')->nullable();
            $table->string('fuel')->nullable();
            $table->string('tnkb_colour')->nullable();
            $table->date('vehicle_registration_date')->nullable();
            $table->string('bpkb_number')->nullable();
            $table->string('ident')->nullable();
            $table->string('asset_name')->nullable();
            $table->string('driver_id')->nullable();
            $table->string('gps_installed')->nullable();
            $table->string('gps_tokens')->nullable();
            $table->date('procurement_date')->nullable();
            $table->integer('asset_price')->nullable();
            $table->text('description')->nullable();
            $table->string('branch_code');
            $table->string('branch_name');
            $table->string('bpkb_ready')->nullable();
            $table->string('receipt')->nullable();
            $table->string('company_code');
            $table->integer('ops_fleet');
            $table->string('asset_code')->nullable();

            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('vehicles');
    }
}
