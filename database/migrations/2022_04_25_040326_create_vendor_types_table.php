<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateVendorTypesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('vendor_types', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('address');
            $table->integer('supp_type');
            $table->integer('lat');
            $table->integer('lng');
            $table->integer('currency_id');
            $table->date('vendor_since');
            $table->integer('payment_terms');
            $table->float('last_paid');
            $table->timestamp('last_paid_date');
            $table->string('bank_act');
            $table->string('bank_ref');
            $table->string('bank_partics');
            $table->integer('remittance');
            $table->integer('tax_group_id');
            $table->integer('factor_company_id');
            $table->string('tax_ref');
            $table->string('phn');
            $table->string('port');
            $table->string('email');
            $table->string('fax');
            $table->string('telephone');
            $table->string('url');
            $table->string('pic_name');
            $table->string('pic_phone');
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('vendor_types');
    }
}
