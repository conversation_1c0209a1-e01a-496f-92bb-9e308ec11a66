<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAnswerQuestionDetailsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('answer_question_details', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('answer_question_user_id');
            $table->foreign('answer_question_user_id')->references('id')->on('answer_question_users')->onDelete('cascade');
            $table->unsignedBigInteger('question_id');
            $table->foreign('question_id')->references('id')->on('questions')->onDelete('cascade');
            $table->string('question');
            $table->unsignedBigInteger('answer_question_id');
            $table->foreign('answer_question_id')->references('id')->on('answer_questions')->onDelete('cascade');
            $table->string('answer');
            $table->float('point');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('answer_question_details');
    }
}
