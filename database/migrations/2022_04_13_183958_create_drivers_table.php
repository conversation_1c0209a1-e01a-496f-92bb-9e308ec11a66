<?php

use App\Models\User;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateDriversTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('drivers', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id')->index();
            $table->unsignedBigInteger("vehicle_id")->index();
            $table->enum("jk",["pria","wanita"])->default("pria");
            $table->string("employee_id",100)->nullable();
            $table->string("contract_number",100)->nullable();
            $table->string("license_number",100)->nullable();
            $table->string("join_date",50)->nullable();
            $table->string("leave_date",50)->nullable();
            $table->string("issue_date",50)->nullable();
            $table->string("expiration_date",50)->nullable();
            $table->text("emergency_contact_detail",50)->nullable();
            $table->text("address")->nullable(); 
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('drivers');
    }
}
