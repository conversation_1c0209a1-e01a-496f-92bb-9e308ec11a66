<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTaxAuthoritiesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('tax_authorities', function (Blueprint $table) {
            $table->id();
            $table->string('description');
            $table->string('bank');
            $table->string('bank_acc_type');
            $table->string('bank_acc');
            $table->string('bank_swift');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('tax_authorities');
    }
}
