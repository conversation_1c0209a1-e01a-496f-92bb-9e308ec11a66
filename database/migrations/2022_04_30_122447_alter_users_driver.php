<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterUsersDriver extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->enum("gender", ["pria", "wanita"])->default("pria")->after("role_id");
            $table->enum("type_account", ["admin", "driver"])->default("admin")->after("gender");
            $table->string("username", 100)->nullable()->after("type_account");
            $table->text("address")->nullable()->after("username");
            $table->string("phone", 100)->nullable()->after("address");
            $table->string("join_date", 100)->nullable()->after("phone");
            $table->string("photo")->default("uploads/image.jpg")->after("join_date");
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}
