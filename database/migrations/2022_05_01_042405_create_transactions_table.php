<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTransactionsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('transactions', function (Blueprint $table) {
            $table->id();
            $table->string("transaction_type",50)->default("workshop");
            $table->enum("workshop_type",["external","internal","null"])->default("null");
            $table->string("estimation_date",50)->nullable();
            $table->decimal("other_change",22,4)->default(0);
            $table->string("status_take",50)->nullable();
            $table->string("status",50)->nullable();
            $table->decimal("subtotal",22,4)->default(0);
            $table->decimal("final_total",22,4)->default(0);
            $table->unsignedBigInteger("workshop_id")->index()->nullable();
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('transactions');
    }
}
