<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateWorkshopsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('workshops', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger("vehicle_id")->index();
            $table->unsignedBigInteger("driver_id")->index();
            $table->text("detail")->nullable();
            $table->unsignedBigInteger("approval_by")->index()->nullable();
            $table->enum("status",["approval","rejected","pending"])->default("pending");
            $table->enum("type",["external","internal"])->default("external");
            $table->text("note")->nullable();
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('workshops');
    }
}
