# Nginx configuration for <PERSON><PERSON>
# Save this as: /etc/nginx/sites-available/ljr-checklist
# Then create symlink: sudo ln -s /etc/nginx/sites-available/ljr-checklist /etc/nginx/sites-enabled/

server {
    listen 8080;
    server_name ********** localhost;
    
    # GANTI PATH INI SESUAI LOKASI APLIKASI ANDA
    root /var/www/html/ljr-checklist/public;
    
    index index.php index.html index.htm;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;

    # Increase max body size for file uploads
    client_max_body_size 20M;

    # Main location block
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    # PHP-FPM configuration
    location ~ \.php$ {
        try_files $uri =404;
        fastcgi_split_path_info ^(.+\.php)(/.+)$;
        
        # Sesuaikan dengan PHP-FPM socket/port Anda
        fastcgi_pass unix:/var/run/php/php7.4-fpm.sock;
        # Atau jika menggunakan TCP: fastcgi_pass 127.0.0.1:9000;
        
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
        
        fastcgi_read_timeout 300;
        fastcgi_buffer_size 128k;
        fastcgi_buffers 4 256k;
        fastcgi_busy_buffers_size 256k;
    }

    # Deny access to hidden files
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }

    # Deny access to sensitive Laravel directories
    location ~ ^/(vendor|storage|bootstrap|config|database|resources|routes|tests|artisan)/ {
        deny all;
        access_log off;
        log_not_found off;
    }

    # Cache static assets
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }

    # Security: deny access to .env and other sensitive files
    location ~ /\.(env|git|svn) {
        deny all;
        access_log off;
        log_not_found off;
    }

    # Logging
    access_log /var/log/nginx/ljr-checklist-access.log;
    error_log /var/log/nginx/ljr-checklist-error.log;
}
