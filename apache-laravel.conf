# Apache Virtual Host configuration for <PERSON><PERSON>
# Save this as: /etc/apache2/sites-available/ljr-checklist.conf
# Then enable: sudo a2ensite ljr-checklist.conf

<VirtualHost *:8080>
    ServerName **********
    ServerAlias localhost
    
    # GANTI PATH INI SESUAI LOKASI APLIKASI ANDA
    DocumentRoot /var/www/html/ljr-checklist/public
    
    # Directory permissions
    <Directory /var/www/html/ljr-checklist/public>
        AllowOverride All
        Require all granted
        Options -Indexes +FollowSymLinks
        
        # Laravel pretty URLs
        RewriteEngine On
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteRule ^(.*)$ index.php [QSA,L]
    </Directory>

    # Deny access to sensitive directories
    <DirectoryMatch "/(vendor|storage|bootstrap|config|database|resources|routes|tests)/">
        Require all denied
    </DirectoryMatch>

    # Deny access to hidden files
    <FilesMatch "^\.">
        Require all denied
    </FilesMatch>

    # Deny access to sensitive files
    <FilesMatch "\.(env|git|svn)$">
        Require all denied
    </FilesMatch>

    # PHP configuration
    <FilesMatch \.php$>
        SetHandler "proxy:fcgi://127.0.0.1:9000"
        # Atau jika menggunakan socket: SetHandler "proxy:unix:/var/run/php/php7.4-fpm.sock|fcgi://localhost"
    </FilesMatch>

    # Security headers
    Header always set X-Frame-Options "SAMEORIGIN"
    Header always set X-XSS-Protection "1; mode=block"
    Header always set X-Content-Type-Options "nosniff"

    # Logging
    ErrorLog ${APACHE_LOG_DIR}/ljr-checklist-error.log
    CustomLog ${APACHE_LOG_DIR}/ljr-checklist-access.log combined
</VirtualHost>

# Make sure Apache listens on port 8080
# Add this to /etc/apache2/ports.conf if not already present:
# Listen 8080
