"use strict";
(self["webpackChunk"] = self["webpackChunk"] || []).push([["resources_js_views_fleet_purchase_order_receive_list_vue"],{

/***/ "./node_modules/babel-loader/lib/index.js??clonedRuleSet-5[0].rules[0].use[0]!./node_modules/vue-loader/lib/index.js??vue-loader-options!./resources/js/views/fleet/purchase_order/receive_list.vue?vue&type=script&lang=js&":
/*!***********************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib/index.js??clonedRuleSet-5[0].rules[0].use[0]!./node_modules/vue-loader/lib/index.js??vue-loader-options!./resources/js/views/fleet/purchase_order/receive_list.vue?vue&type=script&lang=js& ***!
  \***********************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _utils_notify_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../utils/notify.js */ "./resources/js/utils/notify.js");
/* harmony import */ var sweetalert2__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! sweetalert2 */ "./node_modules/sweetalert2/dist/sweetalert2.all.js");
/* harmony import */ var sweetalert2__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(sweetalert2__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! moment */ "./node_modules/moment/moment.js");
/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(moment__WEBPACK_IMPORTED_MODULE_2__);
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//



/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({
  data: function data() {
    return {
      search: '',
      error: "",
      headers: [{
        label: '#',
        align: 'start',
        sortable: false,
        field: 'no'
      }, {
        label: 'PO NUMBER',
        field: 'code'
      }, {
        label: 'PO DATE',
        field: 'ord_date'
      }, {
        label: 'REQUEST DATE',
        field: 'request_date'
      }, {
        label: 'REQUESTOR',
        field: 'user.first_name'
      }, {
        label: 'SUBJECT',
        field: 'comparative_quotation.purchase_request.narrative'
      }, {
        label: 'SUPPLIER',
        field: 'supplier.name'
      }, {
        label: 'STATUS PAYMENT',
        field: 'status'
      }, {
        label: 'TOTAL',
        field: 'total'
      }, {
        label: 'RECEIVE',
        field: 'receive'
      }, {
        label: 'ACTION',
        field: 'action',
        sortable: false,
        width: '150px'
      }],
      pageLength: 10,
      data: []
    };
  },
  created: function created() {
    var _this = this;

    var loader = this.$loading.show({
      canCancel: false
    });
    axios.get('purchase-order/receive/list').then(function (response) {
      console.log(response.data);
      _this.data = response.data.receive;
    })["catch"](function (error) {
      if (error.response.data.message) {
        _this.error = error.response.data.message;
      }

      _utils_notify_js__WEBPACK_IMPORTED_MODULE_0__.axiosError(error);
    })["finally"](function () {
      return loader.hide();
    });
  },
  methods: {
    ApprovePO: function ApprovePO() {
      var _this2 = this;

      sweetalert2__WEBPACK_IMPORTED_MODULE_1___default().fire({
        title: 'Are you sure Approve PO?',
        text: "You won't be able to revert this!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, Submit it!'
      }).then(function (result) {
        if (result.isConfirmed) {
          var loader = _this2.$loading.show({
            canCancel: false
          });

          axios.post("purchase-order/approve", _this2.data).then(function (response) {
            _this2.$router.go();

            _this2.flashMessage.success({
              message: response.data.message,
              time: 5000
            });
          })["catch"](function (error) {
            if (error.response.data.message) {
              _this2.error = error.response.data.message;
            }

            _utils_notify_js__WEBPACK_IMPORTED_MODULE_0__.axiosError(error);
          })["finally"](function () {
            return loader.hide();
          });
        } else {
          sweetalert2__WEBPACK_IMPORTED_MODULE_1___default().fire('Submit Cancel');
        }
      });
    },
    deleteProduct: function deleteProduct(id) {
      var _this3 = this;

      sweetalert2__WEBPACK_IMPORTED_MODULE_1___default().fire({
        title: 'Are you sure?',
        text: "You won't be able to revert this!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, delete it!'
      }).then(function (result) {
        if (result.isConfirmed) {
          var loader = _this3.$loading.show({
            canCancel: false
          });

          axios["delete"]("purchase-request/" + id).then(function (response) {
            _this3.$router.go();

            _this3.flashMessage.success({
              message: response.data.message,
              time: 5000
            });
          })["catch"](function (error) {
            if (error.response.data.message) {
              _this3.error = error.response.data.message;
            }

            _utils_notify_js__WEBPACK_IMPORTED_MODULE_0__.axiosError(error);
          })["finally"](function () {
            return loader.hide();
          });
        } else {
          sweetalert2__WEBPACK_IMPORTED_MODULE_1___default().fire('Deleted Cancel');
        }
      });
    }
  },
  filters: {
    moment: function moment(date) {
      return moment__WEBPACK_IMPORTED_MODULE_2___default()(date).format('DD/MM/YYYY');
    }
  }
});

/***/ }),

/***/ "./resources/js/views/fleet/purchase_order/receive_list.vue":
/*!******************************************************************!*\
  !*** ./resources/js/views/fleet/purchase_order/receive_list.vue ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _receive_list_vue_vue_type_template_id_3cad0f69___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./receive_list.vue?vue&type=template&id=3cad0f69& */ "./resources/js/views/fleet/purchase_order/receive_list.vue?vue&type=template&id=3cad0f69&");
/* harmony import */ var _receive_list_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./receive_list.vue?vue&type=script&lang=js& */ "./resources/js/views/fleet/purchase_order/receive_list.vue?vue&type=script&lang=js&");
/* harmony import */ var _node_modules_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! !../../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js */ "./node_modules/vue-loader/lib/runtime/componentNormalizer.js");





/* normalize component */
;
var component = (0,_node_modules_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_2__["default"])(
  _receive_list_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _receive_list_vue_vue_type_template_id_3cad0f69___WEBPACK_IMPORTED_MODULE_0__.render,
  _receive_list_vue_vue_type_template_id_3cad0f69___WEBPACK_IMPORTED_MODULE_0__.staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* hot reload */
if (false) { var api; }
component.options.__file = "resources/js/views/fleet/purchase_order/receive_list.vue"
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (component.exports);

/***/ }),

/***/ "./resources/js/views/fleet/purchase_order/receive_list.vue?vue&type=script&lang=js&":
/*!*******************************************************************************************!*\
  !*** ./resources/js/views/fleet/purchase_order/receive_list.vue?vue&type=script&lang=js& ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _node_modules_babel_loader_lib_index_js_clonedRuleSet_5_0_rules_0_use_0_node_modules_vue_loader_lib_index_js_vue_loader_options_receive_list_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-5[0].rules[0].use[0]!../../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./receive_list.vue?vue&type=script&lang=js& */ "./node_modules/babel-loader/lib/index.js??clonedRuleSet-5[0].rules[0].use[0]!./node_modules/vue-loader/lib/index.js??vue-loader-options!./resources/js/views/fleet/purchase_order/receive_list.vue?vue&type=script&lang=js&");
 /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_node_modules_babel_loader_lib_index_js_clonedRuleSet_5_0_rules_0_use_0_node_modules_vue_loader_lib_index_js_vue_loader_options_receive_list_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__["default"]); 

/***/ }),

/***/ "./resources/js/views/fleet/purchase_order/receive_list.vue?vue&type=template&id=3cad0f69&":
/*!*************************************************************************************************!*\
  !*** ./resources/js/views/fleet/purchase_order/receive_list.vue?vue&type=template&id=3cad0f69& ***!
  \*************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "render": () => (/* reexport safe */ _node_modules_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_node_modules_vue_loader_lib_index_js_vue_loader_options_receive_list_vue_vue_type_template_id_3cad0f69___WEBPACK_IMPORTED_MODULE_0__.render),
/* harmony export */   "staticRenderFns": () => (/* reexport safe */ _node_modules_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_node_modules_vue_loader_lib_index_js_vue_loader_options_receive_list_vue_vue_type_template_id_3cad0f69___WEBPACK_IMPORTED_MODULE_0__.staticRenderFns)
/* harmony export */ });
/* harmony import */ var _node_modules_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_node_modules_vue_loader_lib_index_js_vue_loader_options_receive_list_vue_vue_type_template_id_3cad0f69___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./receive_list.vue?vue&type=template&id=3cad0f69& */ "./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/vue-loader/lib/index.js??vue-loader-options!./resources/js/views/fleet/purchase_order/receive_list.vue?vue&type=template&id=3cad0f69&");


/***/ }),

/***/ "./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/vue-loader/lib/index.js??vue-loader-options!./resources/js/views/fleet/purchase_order/receive_list.vue?vue&type=template&id=3cad0f69&":
/*!****************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/vue-loader/lib/index.js??vue-loader-options!./resources/js/views/fleet/purchase_order/receive_list.vue?vue&type=template&id=3cad0f69& ***!
  \****************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "render": () => (/* binding */ render),
/* harmony export */   "staticRenderFns": () => (/* binding */ staticRenderFns)
/* harmony export */ });
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  return _c("div", [
    _c("div", { staticClass: "card shadow mb-4" }, [
      _vm._m(0),
      _vm._v(" "),
      _c("div", { staticClass: "card-body" }, [
        _c("div", { staticClass: "row" }, [
          _vm.error
            ? _c("div", { staticClass: "alert alert-danger w-100" }, [
                _vm._v(
                  "\n                    " +
                    _vm._s(_vm.error) +
                    "\n                "
                ),
              ])
            : _vm._e(),
          _vm._v(" "),
          _c(
            "div",
            { staticClass: "col-12" },
            [
              _c("v-text-field", {
                attrs: {
                  "append-icon": "mdi-magnify",
                  label: "Search",
                  "single-line": "",
                  "hide-details": "",
                },
                model: {
                  value: _vm.search,
                  callback: function ($$v) {
                    _vm.search = $$v
                  },
                  expression: "search",
                },
              }),
            ],
            1
          ),
          _vm._v(" "),
          _c(
            "div",
            { staticClass: "col-12" },
            [
              _c("vue-good-table", {
                attrs: {
                  columns: _vm.headers,
                  rows: _vm.data,
                  "search-options": {
                    enabled: true,
                    externalQuery: _vm.search,
                  },
                  "pagination-options": {
                    enabled: true,
                    perPage: 10,
                  },
                },
                scopedSlots: _vm._u([
                  {
                    key: "table-row",
                    fn: function (props) {
                      return [
                        props.column.field === "no"
                          ? _c("span", { staticClass: "text-nowrap" }, [
                              _c("span", { staticClass: "text-nowrap" }, [
                                _vm._v(_vm._s(props.row.originalIndex + 1)),
                              ]),
                            ])
                          : props.column.field === "ord_date"
                          ? _c("span", { staticClass: "text-nowrap" }, [
                              _vm._v(
                                "\n                                                " +
                                  _vm._s(_vm._f("moment")(props.row.ord_date)) +
                                  "\n                                            "
                              ),
                            ])
                          : props.column.field === "request_date"
                          ? _c("span", { staticClass: "text-nowrap" }, [
                              _vm._v(
                                "\n                                                " +
                                  _vm._s(
                                    _vm._f("moment")(
                                      props.row.comparative_quotation
                                        .purchase_request.despatch_date
                                    )
                                  ) +
                                  "\n                                            "
                              ),
                            ])
                          : props.column.field === "status"
                          ? _c("span", { staticClass: "text-nowrap" }, [
                              props.row.status_payment == 1
                                ? _c(
                                    "span",
                                    { staticClass: "badge badge-success" },
                                    [_vm._v("Paid")]
                                  )
                                : _vm._e(),
                              _vm._v(" "),
                              props.row.status_payment == 2
                                ? _c(
                                    "span",
                                    { staticClass: "badge badge-warning" },
                                    [_vm._v("Partial Paid")]
                                  )
                                : _vm._e(),
                              _vm._v(" "),
                              props.row.status_payment == 0
                                ? _c(
                                    "span",
                                    { staticClass: "badge badge-danger" },
                                    [_vm._v("Not yet paid")]
                                  )
                                : _vm._e(),
                            ])
                          : props.column.field === "total"
                          ? _c("span", { staticClass: "text-nowrap" }, [
                              _vm._v(
                                "\n                                                " +
                                  _vm._s(
                                    Number(props.row.total).toLocaleString()
                                  ) +
                                  "\n                                            "
                              ),
                            ])
                          : props.column.field === "receive"
                          ? _c("span", { staticClass: "text-nowrap" }, [
                              props.row.status != "Completed"
                                ? _c(
                                    "div",
                                    [
                                      props.row.authorise == 1
                                        ? _c(
                                            "router-link",
                                            {
                                              staticClass: "btn btn-success",
                                              attrs: {
                                                to:
                                                  "purchase-order-receive/store/" +
                                                  props.row.id,
                                              },
                                            },
                                            [
                                              _c("i", {
                                                staticClass: "fas fa-archive",
                                              }),
                                            ]
                                          )
                                        : _vm._e(),
                                    ],
                                    1
                                  )
                                : _c("div", [
                                    _c(
                                      "span",
                                      { staticClass: "badge badge-success" },
                                      [_vm._v("Completed Received")]
                                    ),
                                  ]),
                            ])
                          : props.column.field === "action"
                          ? _c(
                              "span",
                              { staticClass: "text-nowrap" },
                              [
                                _c(
                                  "router-link",
                                  {
                                    staticClass: "btn btn-info",
                                    attrs: {
                                      to:
                                        "purchase-order-receive/show/" +
                                        props.row.id,
                                    },
                                  },
                                  [_c("i", { staticClass: "fas fa-eye" })]
                                ),
                                _vm._v(" "),
                                props.row.comparative_quotation.purchase_request
                                  .category_id == 9
                                  ? _c(
                                      "router-link",
                                      {
                                        staticClass: "btn btn-primary",
                                        attrs: {
                                          to:
                                            "purchase-order-receive/penomoran/" +
                                            props.row.id,
                                        },
                                      },
                                      [
                                        _c("i", {
                                          staticClass: "fas fa-clipboard-list",
                                        }),
                                      ]
                                    )
                                  : _vm._e(),
                              ],
                              1
                            )
                          : _c("span", [
                              _vm._v(
                                "\n                                                " +
                                  _vm._s(
                                    props.formattedRow[props.column.field]
                                  ) +
                                  "\n                                            "
                              ),
                            ]),
                      ]
                    },
                  },
                  {
                    key: "pagination-bottom",
                    fn: function (props) {
                      return [
                        _c(
                          "div",
                          {
                            staticClass:
                              "d-flex justify-content-between flex-wrap",
                          },
                          [
                            _c(
                              "div",
                              {
                                staticClass:
                                  "d-flex align-items-center mb-0 mt-1",
                              },
                              [
                                _c("span", { staticClass: "text-nowrap " }, [
                                  _vm._v(
                                    "\n                                                    Showing 1 to\n                                                    "
                                  ),
                                ]),
                                _vm._v(" "),
                                _c("b-form-select", {
                                  staticClass: "mx-1",
                                  attrs: { options: ["3", "5", "10"] },
                                  on: {
                                    input: function (value) {
                                      return props.perPageChanged({
                                        currentPerPage: value,
                                      })
                                    },
                                  },
                                  model: {
                                    value: _vm.pageLength,
                                    callback: function ($$v) {
                                      _vm.pageLength = $$v
                                    },
                                    expression: "pageLength",
                                  },
                                }),
                                _vm._v(" "),
                                _c("span", { staticClass: "text-nowrap" }, [
                                  _vm._v(
                                    " of " + _vm._s(props.total) + " entries "
                                  ),
                                ]),
                              ],
                              1
                            ),
                            _vm._v(" "),
                            _c(
                              "div",
                              [
                                _c("b-pagination", {
                                  staticClass: "mt-1 mb-0",
                                  attrs: {
                                    value: 1,
                                    "total-rows": props.total,
                                    "per-page": _vm.pageLength,
                                    "first-number": "",
                                    "last-number": "",
                                    align: "right",
                                    "prev-class": "prev-item",
                                    "next-class": "next-item",
                                  },
                                  on: {
                                    input: function (value) {
                                      return props.pageChanged({
                                        currentPage: value,
                                      })
                                    },
                                  },
                                  scopedSlots: _vm._u(
                                    [
                                      {
                                        key: "prev-text",
                                        fn: function () {
                                          return [
                                            _c("i", {
                                              staticClass: "fa fa-arrow-left",
                                            }),
                                          ]
                                        },
                                        proxy: true,
                                      },
                                      {
                                        key: "next-text",
                                        fn: function () {
                                          return [
                                            _c("i", {
                                              staticClass: "fa fa-arrow-right",
                                            }),
                                          ]
                                        },
                                        proxy: true,
                                      },
                                    ],
                                    null,
                                    true
                                  ),
                                }),
                              ],
                              1
                            ),
                          ]
                        ),
                      ]
                    },
                  },
                ]),
              }),
            ],
            1
          ),
        ]),
      ]),
    ]),
  ])
}
var staticRenderFns = [
  function () {
    var _vm = this
    var _h = _vm.$createElement
    var _c = _vm._self._c || _h
    return _c("div", { staticClass: "card-header py-3" }, [
      _c("b", { staticClass: "m-0 font-weight-bold text-primary" }, [
        _vm._v("Receive PO"),
      ]),
    ])
  },
]
render._withStripped = true



/***/ })

}]);