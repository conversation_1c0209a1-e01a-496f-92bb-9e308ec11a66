"use strict";
(self["webpackChunk"] = self["webpackChunk"] || []).push([["resources_js_views_fleet_dashboard_vue"],{

/***/ "./node_modules/babel-loader/lib/index.js??clonedRuleSet-5[0].rules[0].use[0]!./node_modules/vue-loader/lib/index.js??vue-loader-options!./resources/js/views/fleet/dashboard.vue?vue&type=script&lang=js&":
/*!*****************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib/index.js??clonedRuleSet-5[0].rules[0].use[0]!./node_modules/vue-loader/lib/index.js??vue-loader-options!./resources/js/views/fleet/dashboard.vue?vue&type=script&lang=js& ***!
  \*****************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _chart_demo_chart_pie_demo__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../chart/demo/chart-pie-demo */ "./resources/js/chart/demo/chart-pie-demo.js");
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({
  name: "Dashboard",
  mounted: function mounted() {
    (0,_chart_demo_chart_pie_demo__WEBPACK_IMPORTED_MODULE_0__["default"])();
  } // created () {
  //     let loader = this.$loading.show({canCancel: false});
  //     axios.get('role-user-permission')
  //     .then((response) => {
  //         let datas = response.data;
  //         this.$store.dispatch("permission", datas);
  //     })
  //     .finally(() => (loader.hide()));
  // },

});

/***/ }),

/***/ "./resources/js/chart/demo/chart-pie-demo.js":
/*!***************************************************!*\
  !*** ./resources/js/chart/demo/chart-pie-demo.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ chartPieDemo)
/* harmony export */ });
function chartPieDemo() {
  // Set new default font family and font color to mimic Bootstrap's default styling
  Chart.defaults.global.defaultFontFamily = "Nunito", '-apple-system,system-ui,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Arial,sans-serif';
  Chart.defaults.global.defaultFontColor = "#858796"; // Pie Chart Example

  var ctx = document.getElementById("myPieChart");
  var myPieChart = new Chart(ctx, {
    type: "pie",
    data: {
      labels: ["Normal", "Kurang Normal", "Tidak Normal"],
      datasets: [{
        data: [50, 15, 2],
        backgroundColor: ["#1cc88a", "#f6c23e", "#e74a3b"],
        hoverBackgroundColor: ["#1cc88a", "#f6c23e", "#e74a3b"],
        hoverBorderColor: "rgba(234, 236, 244, 1)"
      }]
    },
    options: {
      maintainAspectRatio: false,
      tooltips: {
        backgroundColor: "rgb(255,255,255)",
        bodyFontColor: "#858796",
        borderColor: "#dddfeb",
        borderWidth: 1,
        xPadding: 15,
        yPadding: 15,
        displayColors: false,
        caretPadding: 10
      },
      legend: {
        display: false
      }
    }
  });
  var ctx = document.getElementById("myPieChart2");
  var myPieChart = new Chart(ctx, {
    type: "pie",
    data: {
      labels: ["Normal", "Kurang Normal", "Tidak Normal"],
      datasets: [{
        data: [50, 10, 7],
        backgroundColor: ["#1cc88a", "#f6c23e", "#e74a3b"],
        hoverBackgroundColor: ["#1cc88a", "#f6c23e", "#e74a3b"],
        hoverBorderColor: "rgba(234, 236, 244, 1)"
      }]
    },
    options: {
      maintainAspectRatio: false,
      tooltips: {
        backgroundColor: "rgb(255,255,255)",
        bodyFontColor: "#858796",
        borderColor: "#dddfeb",
        borderWidth: 1,
        xPadding: 15,
        yPadding: 15,
        displayColors: false,
        caretPadding: 10
      },
      legend: {
        display: false
      }
    }
  });
}

/***/ }),

/***/ "./resources/js/views/fleet/dashboard.vue":
/*!************************************************!*\
  !*** ./resources/js/views/fleet/dashboard.vue ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _dashboard_vue_vue_type_template_id_114b7638___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./dashboard.vue?vue&type=template&id=114b7638& */ "./resources/js/views/fleet/dashboard.vue?vue&type=template&id=114b7638&");
/* harmony import */ var _dashboard_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./dashboard.vue?vue&type=script&lang=js& */ "./resources/js/views/fleet/dashboard.vue?vue&type=script&lang=js&");
/* harmony import */ var _node_modules_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! !../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js */ "./node_modules/vue-loader/lib/runtime/componentNormalizer.js");





/* normalize component */
;
var component = (0,_node_modules_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_2__["default"])(
  _dashboard_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _dashboard_vue_vue_type_template_id_114b7638___WEBPACK_IMPORTED_MODULE_0__.render,
  _dashboard_vue_vue_type_template_id_114b7638___WEBPACK_IMPORTED_MODULE_0__.staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* hot reload */
if (false) { var api; }
component.options.__file = "resources/js/views/fleet/dashboard.vue"
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (component.exports);

/***/ }),

/***/ "./resources/js/views/fleet/dashboard.vue?vue&type=script&lang=js&":
/*!*************************************************************************!*\
  !*** ./resources/js/views/fleet/dashboard.vue?vue&type=script&lang=js& ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _node_modules_babel_loader_lib_index_js_clonedRuleSet_5_0_rules_0_use_0_node_modules_vue_loader_lib_index_js_vue_loader_options_dashboard_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-5[0].rules[0].use[0]!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./dashboard.vue?vue&type=script&lang=js& */ "./node_modules/babel-loader/lib/index.js??clonedRuleSet-5[0].rules[0].use[0]!./node_modules/vue-loader/lib/index.js??vue-loader-options!./resources/js/views/fleet/dashboard.vue?vue&type=script&lang=js&");
 /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_node_modules_babel_loader_lib_index_js_clonedRuleSet_5_0_rules_0_use_0_node_modules_vue_loader_lib_index_js_vue_loader_options_dashboard_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__["default"]); 

/***/ }),

/***/ "./resources/js/views/fleet/dashboard.vue?vue&type=template&id=114b7638&":
/*!*******************************************************************************!*\
  !*** ./resources/js/views/fleet/dashboard.vue?vue&type=template&id=114b7638& ***!
  \*******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "render": () => (/* reexport safe */ _node_modules_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_node_modules_vue_loader_lib_index_js_vue_loader_options_dashboard_vue_vue_type_template_id_114b7638___WEBPACK_IMPORTED_MODULE_0__.render),
/* harmony export */   "staticRenderFns": () => (/* reexport safe */ _node_modules_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_node_modules_vue_loader_lib_index_js_vue_loader_options_dashboard_vue_vue_type_template_id_114b7638___WEBPACK_IMPORTED_MODULE_0__.staticRenderFns)
/* harmony export */ });
/* harmony import */ var _node_modules_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_node_modules_vue_loader_lib_index_js_vue_loader_options_dashboard_vue_vue_type_template_id_114b7638___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./dashboard.vue?vue&type=template&id=114b7638& */ "./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/vue-loader/lib/index.js??vue-loader-options!./resources/js/views/fleet/dashboard.vue?vue&type=template&id=114b7638&");


/***/ }),

/***/ "./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/vue-loader/lib/index.js??vue-loader-options!./resources/js/views/fleet/dashboard.vue?vue&type=template&id=114b7638&":
/*!**********************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/vue-loader/lib/index.js??vue-loader-options!./resources/js/views/fleet/dashboard.vue?vue&type=template&id=114b7638& ***!
  \**********************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "render": () => (/* binding */ render),
/* harmony export */   "staticRenderFns": () => (/* binding */ staticRenderFns)
/* harmony export */ });
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  return _c("div", [
    _vm._m(0),
    _vm._v(" "),
    _vm.$can("dashboard-view")
      ? _c("div", { staticClass: "row" }, [
          _vm._m(1),
          _vm._v(" "),
          _vm._m(2),
          _vm._v(" "),
          _vm._m(3),
        ])
      : _vm._e(),
    _vm._v(" "),
    _vm.$can("dashboard-view")
      ? _c("div", { staticClass: "row" }, [_vm._m(4), _vm._v(" "), _vm._m(5)])
      : _vm._e(),
    _vm._v(" "),
    _vm.$can("dashboard-view")
      ? _c("div", { staticClass: "row" }, [_vm._m(6)])
      : _vm._e(),
  ])
}
var staticRenderFns = [
  function () {
    var _vm = this
    var _h = _vm.$createElement
    var _c = _vm._self._c || _h
    return _c(
      "div",
      {
        staticClass:
          "d-sm-flex align-items-center justify-content-between mb-4",
      },
      [
        _c("h1", { staticClass: "h3 mb-0 text-gray-800" }, [
          _vm._v("Dashboard"),
        ]),
      ]
    )
  },
  function () {
    var _vm = this
    var _h = _vm.$createElement
    var _c = _vm._self._c || _h
    return _c("div", { staticClass: "col-xl-4 col-md-6 mb-4" }, [
      _c("div", { staticClass: "card border-left-primary shadow h-100 py-2" }, [
        _c("div", { staticClass: "card-body" }, [
          _c("div", { staticClass: "row no-gutters align-items-center" }, [
            _c("div", { staticClass: "col mr-2" }, [
              _c(
                "div",
                {
                  staticClass:
                    "\n                  text-xs\n                  font-weight-bold\n                  text-primary text-uppercase\n                  mb-1\n                ",
                },
                [_vm._v("\n                Jumlah Kendaraan\n              ")]
              ),
              _vm._v(" "),
              _c(
                "div",
                { staticClass: "h5 mb-0 font-weight-bold text-gray-800" },
                [_vm._v("\n                67\n              ")]
              ),
            ]),
            _vm._v(" "),
            _c("div", { staticClass: "col-auto" }, [
              _c("i", { staticClass: "fas fa-truck fa-2x text-gray-300" }),
            ]),
          ]),
        ]),
      ]),
    ])
  },
  function () {
    var _vm = this
    var _h = _vm.$createElement
    var _c = _vm._self._c || _h
    return _c("div", { staticClass: "col-xl-4 col-md-6 mb-4" }, [
      _c("div", { staticClass: "card border-left-success shadow h-100 py-2" }, [
        _c("div", { staticClass: "card-body" }, [
          _c("div", { staticClass: "row no-gutters align-items-center" }, [
            _c("div", { staticClass: "col mr-2" }, [
              _c(
                "div",
                {
                  staticClass:
                    "\n                  text-xs\n                  font-weight-bold\n                  text-success text-uppercase\n                  mb-1\n                ",
                },
                [_vm._v("\n                Jumlah Driver\n              ")]
              ),
              _vm._v(" "),
              _c(
                "div",
                { staticClass: "h5 mb-0 font-weight-bold text-gray-800" },
                [_vm._v("\n                95\n              ")]
              ),
            ]),
            _vm._v(" "),
            _c("div", { staticClass: "col-auto" }, [
              _c("i", { staticClass: "fas fa-user fa-2x text-gray-300" }),
            ]),
          ]),
        ]),
      ]),
    ])
  },
  function () {
    var _vm = this
    var _h = _vm.$createElement
    var _c = _vm._self._c || _h
    return _c("div", { staticClass: "col-xl-4 col-md-6 mb-4" }, [
      _c("div", { staticClass: "card border-left-info shadow h-100 py-2" }, [
        _c("div", { staticClass: "card-body" }, [
          _c("div", { staticClass: "row no-gutters align-items-center" }, [
            _c("div", { staticClass: "col mr-2" }, [
              _c(
                "div",
                {
                  staticClass:
                    "text-xs font-weight-bold text-info text-uppercase mb-1",
                },
                [_vm._v("\n                Jumlah Kurir\n              ")]
              ),
              _vm._v(" "),
              _c("div", { staticClass: "row no-gutters align-items-center" }, [
                _c("div", { staticClass: "col-auto" }, [
                  _c(
                    "div",
                    {
                      staticClass:
                        "h5 mb-0 mr-3 font-weight-bold text-gray-800",
                    },
                    [_vm._v("\n                    50\n                  ")]
                  ),
                ]),
              ]),
            ]),
            _vm._v(" "),
            _c("div", { staticClass: "col-auto" }, [
              _c("i", { staticClass: "fas fa-life-ring fa-2x text-gray-300" }),
            ]),
          ]),
        ]),
      ]),
    ])
  },
  function () {
    var _vm = this
    var _h = _vm.$createElement
    var _c = _vm._self._c || _h
    return _c("div", { staticClass: "col-xl-6 col-lg-6" }, [
      _c("div", { staticClass: "card shadow mb-4" }, [
        _c(
          "div",
          {
            staticClass:
              "\n            card-header\n            py-3\n            d-flex\n            flex-row\n            align-items-center\n            justify-content-between\n          ",
          },
          [
            _c("h6", { staticClass: "m-0 font-weight-bold text-primary" }, [
              _vm._v("Persentase Cheklist Harian"),
            ]),
          ]
        ),
        _vm._v(" "),
        _c("div", { staticClass: "card-body" }, [
          _c("div", { staticClass: "chart-pie pt-4 pb-2" }, [
            _c("canvas", { attrs: { id: "myPieChart" } }),
          ]),
          _vm._v(" "),
          _c("div", { staticClass: "mt-4 text-center small" }, [
            _c("span", { staticClass: "mr-2" }, [
              _c("i", { staticClass: "fas fa-circle text-success" }),
              _vm._v(" Normal\n            "),
            ]),
            _vm._v(" "),
            _c("span", { staticClass: "mr-2" }, [
              _c("i", { staticClass: "fas fa-circle text-warning" }),
              _vm._v(" Kurang Normal\n            "),
            ]),
            _vm._v(" "),
            _c("span", { staticClass: "mr-2" }, [
              _c("i", { staticClass: "fas fa-circle text-danger" }),
              _vm._v(" Tidak Normal\n            "),
            ]),
          ]),
        ]),
      ]),
    ])
  },
  function () {
    var _vm = this
    var _h = _vm.$createElement
    var _c = _vm._self._c || _h
    return _c("div", { staticClass: "col-xl-6 col-lg-6" }, [
      _c("div", { staticClass: "card shadow mb-4" }, [
        _c(
          "div",
          {
            staticClass:
              "\n            card-header\n            py-3\n            d-flex\n            flex-row\n            align-items-center\n            justify-content-between\n          ",
          },
          [
            _c("h6", { staticClass: "m-0 font-weight-bold text-primary" }, [
              _vm._v("Persentase Kondisi Kendaraan"),
            ]),
          ]
        ),
        _vm._v(" "),
        _c("div", { staticClass: "card-body" }, [
          _c("div", { staticClass: "chart-pie pt-4 pb-2" }, [
            _c("canvas", { attrs: { id: "myPieChart2" } }),
          ]),
          _vm._v(" "),
          _c("div", { staticClass: "mt-4 text-center small" }, [
            _c("span", { staticClass: "mr-2" }, [
              _c("i", { staticClass: "fas fa-circle text-success" }),
              _vm._v(" Normal\n            "),
            ]),
            _vm._v(" "),
            _c("span", { staticClass: "mr-2" }, [
              _c("i", { staticClass: "fas fa-circle text-warning" }),
              _vm._v(" Kurang Normal\n            "),
            ]),
            _vm._v(" "),
            _c("span", { staticClass: "mr-2" }, [
              _c("i", { staticClass: "fas fa-circle text-danger" }),
              _vm._v(" Tidak Normal\n            "),
            ]),
          ]),
        ]),
      ]),
    ])
  },
  function () {
    var _vm = this
    var _h = _vm.$createElement
    var _c = _vm._self._c || _h
    return _c("div", { staticClass: "col-lg-12 mb-4" }, [
      _c("div", { staticClass: "card shadow mb-4" }, [
        _c("div", { staticClass: "card-header py-3" }, [
          _c("h6", { staticClass: "m-0 font-weight-bold text-primary" }, [
            _vm._v("Jumlah kendaraan luar kota dan dalam kota "),
          ]),
        ]),
        _vm._v(" "),
        _c("div", { staticClass: "card-body" }, [
          _c("h4", { staticClass: "small font-weight-bold" }, [
            _vm._v("\n            Kendaraan dalam kota "),
            _c("span", { staticClass: "float-right" }, [_vm._v("20%")]),
          ]),
          _vm._v(" "),
          _c("div", { staticClass: "progress mb-4" }, [
            _c("div", {
              staticClass: "progress-bar",
              staticStyle: { width: "20%" },
              attrs: {
                role: "progressbar",
                "aria-valuenow": "20",
                "aria-valuemin": "0",
                "aria-valuemax": "100",
              },
            }),
          ]),
          _vm._v(" "),
          _c("h4", { staticClass: "small font-weight-bold" }, [
            _vm._v("\n            Kendaraan Luar Kota "),
            _c("span", { staticClass: "float-right" }, [_vm._v("80%")]),
          ]),
          _vm._v(" "),
          _c("div", { staticClass: "progress mb-4" }, [
            _c("div", {
              staticClass: "progress-bar bg-info",
              staticStyle: { width: "80%" },
              attrs: {
                role: "progressbar",
                "aria-valuenow": "80",
                "aria-valuemin": "0",
                "aria-valuemax": "100",
              },
            }),
          ]),
        ]),
      ]),
    ])
  },
]
render._withStripped = true



/***/ })

}]);