"use strict";
(self["webpackChunk"] = self["webpackChunk"] || []).push([["resources_js_views_fleet_purchase_request_index_vue"],{

/***/ "./node_modules/babel-loader/lib/index.js??clonedRuleSet-5[0].rules[0].use[0]!./node_modules/vue-loader/lib/index.js??vue-loader-options!./resources/js/views/fleet/purchase_request/index.vue?vue&type=script&lang=js&":
/*!******************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib/index.js??clonedRuleSet-5[0].rules[0].use[0]!./node_modules/vue-loader/lib/index.js??vue-loader-options!./resources/js/views/fleet/purchase_request/index.vue?vue&type=script&lang=js& ***!
  \******************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _utils_notify_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../utils/notify.js */ "./resources/js/utils/notify.js");
/* harmony import */ var sweetalert2__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! sweetalert2 */ "./node_modules/sweetalert2/dist/sweetalert2.all.js");
/* harmony import */ var sweetalert2__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(sweetalert2__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! moment */ "./node_modules/moment/moment.js");
/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(moment__WEBPACK_IMPORTED_MODULE_2__);
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//



/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({
  data: function data() {
    return {
      search: '',
      error: "",
      headers: [{
        label: '#',
        align: 'start',
        sortable: false,
        field: 'no'
      }, {
        label: 'Location',
        field: 'location.name'
      }, {
        label: 'Department',
        field: 'department.description'
      }, {
        label: 'Despatch Date',
        field: 'despatch_date'
      }, {
        label: 'To Date',
        field: 'to_date'
      }, {
        label: 'Authorised',
        field: 'authorised'
      }, {
        label: 'Closed',
        field: 'closed'
      }, {
        label: 'Pick Up',
        field: 'pick_up'
      }, {
        label: 'Narrative',
        field: 'narrative'
      }, {
        label: 'Requester',
        field: 'requester'
      }, {
        label: 'code',
        field: 'code'
      }, {
        label: 'Category',
        field: 'category.category_description'
      }, {
        label: 'Created Date',
        field: 'created_date'
      }, {
        label: 'Authorised Date',
        field: 'authorised_date'
      }, //   { label: 'Budget Authorised', field: 'budget_authorised' },
      //   { label: 'Budget Authorised Date', field: 'budget_authorised_date' },
      {
        label: 'Action',
        field: 'action',
        width: '180px'
      }],
      pageLength: 10,
      data: []
    };
  },
  created: function created() {
    var _this = this;

    var loader = this.$loading.show({
      canCancel: false
    });
    axios.get('purchase-request').then(function (response) {
      console.log(response.data);
      _this.data = response.data;
    })["catch"](function (error) {
      if (error.response.data.message) {
        _this.error = error.response.data.message;
      }

      _utils_notify_js__WEBPACK_IMPORTED_MODULE_0__.axiosError(error);
    })["finally"](function () {
      return loader.hide();
    });
  },
  methods: {
    deleteProduct: function deleteProduct(id) {
      var _this2 = this;

      sweetalert2__WEBPACK_IMPORTED_MODULE_1___default().fire({
        title: 'Are you sure?',
        text: "You won't be able to revert this!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, delete it!'
      }).then(function (result) {
        if (result.isConfirmed) {
          var loader = _this2.$loading.show({
            canCancel: false
          });

          axios["delete"]("purchase-request/" + id).then(function (response) {
            _this2.$router.go();

            _this2.flashMessage.success({
              message: response.data.message,
              time: 5000
            });
          })["catch"](function (error) {
            _utils_notify_js__WEBPACK_IMPORTED_MODULE_0__.axiosError(error);
          })["finally"](function () {
            return loader.hide();
          });
        } else {
          sweetalert2__WEBPACK_IMPORTED_MODULE_1___default().fire('Deleted Cancel');
        }
      });
    }
  },
  filters: {
    moment: function moment(date) {
      return moment__WEBPACK_IMPORTED_MODULE_2___default()(date).format('DD/MM/YYYY');
    }
  }
});

/***/ }),

/***/ "./resources/js/views/fleet/purchase_request/index.vue":
/*!*************************************************************!*\
  !*** ./resources/js/views/fleet/purchase_request/index.vue ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _index_vue_vue_type_template_id_ae368718___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./index.vue?vue&type=template&id=ae368718& */ "./resources/js/views/fleet/purchase_request/index.vue?vue&type=template&id=ae368718&");
/* harmony import */ var _index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./index.vue?vue&type=script&lang=js& */ "./resources/js/views/fleet/purchase_request/index.vue?vue&type=script&lang=js&");
/* harmony import */ var _node_modules_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! !../../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js */ "./node_modules/vue-loader/lib/runtime/componentNormalizer.js");





/* normalize component */
;
var component = (0,_node_modules_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_2__["default"])(
  _index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _index_vue_vue_type_template_id_ae368718___WEBPACK_IMPORTED_MODULE_0__.render,
  _index_vue_vue_type_template_id_ae368718___WEBPACK_IMPORTED_MODULE_0__.staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* hot reload */
if (false) { var api; }
component.options.__file = "resources/js/views/fleet/purchase_request/index.vue"
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (component.exports);

/***/ }),

/***/ "./resources/js/views/fleet/purchase_request/index.vue?vue&type=script&lang=js&":
/*!**************************************************************************************!*\
  !*** ./resources/js/views/fleet/purchase_request/index.vue?vue&type=script&lang=js& ***!
  \**************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _node_modules_babel_loader_lib_index_js_clonedRuleSet_5_0_rules_0_use_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-5[0].rules[0].use[0]!../../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js& */ "./node_modules/babel-loader/lib/index.js??clonedRuleSet-5[0].rules[0].use[0]!./node_modules/vue-loader/lib/index.js??vue-loader-options!./resources/js/views/fleet/purchase_request/index.vue?vue&type=script&lang=js&");
 /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_node_modules_babel_loader_lib_index_js_clonedRuleSet_5_0_rules_0_use_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__["default"]); 

/***/ }),

/***/ "./resources/js/views/fleet/purchase_request/index.vue?vue&type=template&id=ae368718&":
/*!********************************************************************************************!*\
  !*** ./resources/js/views/fleet/purchase_request/index.vue?vue&type=template&id=ae368718& ***!
  \********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "render": () => (/* reexport safe */ _node_modules_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_template_id_ae368718___WEBPACK_IMPORTED_MODULE_0__.render),
/* harmony export */   "staticRenderFns": () => (/* reexport safe */ _node_modules_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_template_id_ae368718___WEBPACK_IMPORTED_MODULE_0__.staticRenderFns)
/* harmony export */ });
/* harmony import */ var _node_modules_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_template_id_ae368718___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=template&id=ae368718& */ "./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/vue-loader/lib/index.js??vue-loader-options!./resources/js/views/fleet/purchase_request/index.vue?vue&type=template&id=ae368718&");


/***/ }),

/***/ "./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/vue-loader/lib/index.js??vue-loader-options!./resources/js/views/fleet/purchase_request/index.vue?vue&type=template&id=ae368718&":
/*!***********************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/vue-loader/lib/index.js??vue-loader-options!./resources/js/views/fleet/purchase_request/index.vue?vue&type=template&id=ae368718& ***!
  \***********************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "render": () => (/* binding */ render),
/* harmony export */   "staticRenderFns": () => (/* binding */ staticRenderFns)
/* harmony export */ });
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  return _c("div", [
    _c("div", { staticClass: "card shadow mb-4" }, [
      _c(
        "div",
        { staticClass: "card-header py-3" },
        [
          _c("b", { staticClass: "m-0 font-weight-bold text-primary" }, [
            _vm._v("Master Purchase Request"),
          ]),
          _vm._v(" "),
          _c(
            "router-link",
            {
              staticClass: "btn btn-primary btn-sm btn-icon-split float-right",
              attrs: { to: "purchase-request/create" },
            },
            [
              _c("span", { staticClass: "icon text-white-50" }, [
                _c("i", { staticClass: "fas fa-plus" }),
              ]),
              _vm._v(" "),
              _c("span", { staticClass: "text" }, [
                _vm._v("Tambah Purchase Request"),
              ]),
            ]
          ),
        ],
        1
      ),
      _vm._v(" "),
      _c("div", { staticClass: "card-body" }, [
        _c("div", { staticClass: "row" }, [
          _c(
            "div",
            { staticClass: "col-12" },
            [
              _vm.error
                ? _c("div", { staticClass: "alert alert-danger" }, [
                    _vm._v(
                      "\n                        " +
                        _vm._s(_vm.error) +
                        "\n                    "
                    ),
                  ])
                : _vm._e(),
              _vm._v(" "),
              _c("v-text-field", {
                attrs: {
                  "append-icon": "mdi-magnify",
                  label: "Search",
                  "single-line": "",
                  "hide-details": "",
                },
                model: {
                  value: _vm.search,
                  callback: function ($$v) {
                    _vm.search = $$v
                  },
                  expression: "search",
                },
              }),
            ],
            1
          ),
          _vm._v(" "),
          _c(
            "div",
            { staticClass: "col-12" },
            [
              _c("vue-good-table", {
                attrs: {
                  columns: _vm.headers,
                  "fixed-header": true,
                  "max-height": "500px",
                  rows: _vm.data,
                  "search-options": {
                    enabled: true,
                    externalQuery: _vm.search,
                  },
                  "pagination-options": {
                    enabled: true,
                    perPage: 10,
                  },
                },
                scopedSlots: _vm._u([
                  {
                    key: "table-row",
                    fn: function (props) {
                      return [
                        props.column.field === "no"
                          ? _c("span", { staticClass: "text-nowrap" }, [
                              _c("span", { staticClass: "text-nowrap" }, [
                                _vm._v(_vm._s(props.row.originalIndex + 1)),
                              ]),
                            ])
                          : props.column.field === "despatch_date"
                          ? _c("span", { staticClass: "text-nowrap" }, [
                              _c("span", { staticClass: "text-nowrap" }, [
                                _vm._v(_vm._s(props.row.despatch_date)),
                              ]),
                            ])
                          : props.column.field === "to_date"
                          ? _c("span", { staticClass: "text-nowrap" }, [
                              _c("span", { staticClass: "text-nowrap" }, [
                                _vm._v(_vm._s(props.row.to_date)),
                              ]),
                            ])
                          : props.column.field === "created_date"
                          ? _c("span", { staticClass: "text-nowrap" }, [
                              _c("span", { staticClass: "text-nowrap" }, [
                                _vm._v(_vm._s(props.row.created_date)),
                              ]),
                            ])
                          : props.column.field === "authorised"
                          ? _c(
                              "span",
                              { staticClass: "text-nowrap" },
                              [
                                props.row.authorised == 0
                                  ? _c(
                                      "router-link",
                                      {
                                        attrs: {
                                          to: "approval-purchase-request",
                                        },
                                      },
                                      [
                                        _c(
                                          "span",
                                          {
                                            staticClass: "badge badge-warning",
                                          },
                                          [
                                            _vm._v(
                                              _vm._s(props.row.text_approve)
                                            ),
                                          ]
                                        ),
                                      ]
                                    )
                                  : props.row.authorised == 2
                                  ? _c(
                                      "span",
                                      { staticClass: "badge badge-danger" },
                                      [_vm._v("Not Approve")]
                                    )
                                  : _c(
                                      "span",
                                      { staticClass: "badge badge-success" },
                                      [_vm._v("Approved")]
                                    ),
                              ],
                              1
                            )
                          : props.column.field === "closed"
                          ? _c("span", { staticClass: "text-nowrap" }, [
                              props.row.closed == 0
                                ? _c("div", [
                                    _c(
                                      "span",
                                      { staticClass: "badge badge-info" },
                                      [_vm._v("Open")]
                                    ),
                                  ])
                                : _c("div", [
                                    _c(
                                      "span",
                                      { staticClass: "badge badge-danger" },
                                      [_vm._v("Close")]
                                    ),
                                  ]),
                            ])
                          : props.column.field === "pick_up"
                          ? _c("span", { staticClass: "text-nowrap" }, [
                              props.row.pick_up == 0
                                ? _c("div", [
                                    _c(
                                      "span",
                                      { staticClass: "badge badge-danger" },
                                      [_vm._v("Not Ready")]
                                    ),
                                  ])
                                : props.row.pick_up == 1
                                ? _c("div", [
                                    _c(
                                      "span",
                                      { staticClass: "badge badge-success" },
                                      [_vm._v("Ready")]
                                    ),
                                  ])
                                : props.row.pick_up == 2
                                ? _c("div", [
                                    _c(
                                      "span",
                                      { staticClass: "badge badge-warning" },
                                      [_vm._v("Partial Ready")]
                                    ),
                                  ])
                                : _c("div", [
                                    _c(
                                      "span",
                                      { staticClass: "badge badge-info" },
                                      [_vm._v("Already Pickup")]
                                    ),
                                  ]),
                            ])
                          : props.column.field === "authorised_date"
                          ? _c("span", { staticClass: "text-nowrap" }, [
                              props.row.authorised_date
                                ? _c("div", [
                                    _vm._v(
                                      "\n                                    " +
                                        _vm._s(
                                          _vm._f("moment")(
                                            props.row.authorised_date
                                          )
                                        ) +
                                        "\n                                "
                                    ),
                                  ])
                                : _vm._e(),
                            ])
                          : props.column.field === "action"
                          ? _c(
                              "span",
                              { staticClass: "text-nowrap" },
                              [
                                props.row.item_exist == 0
                                  ? _c(
                                      "router-link",
                                      {
                                        staticClass: "btn btn-success",
                                        attrs: {
                                          to:
                                            "/purchase-request/item-select/" +
                                            props.row.id,
                                        },
                                      },
                                      [_c("i", { staticClass: "fas fa-list" })]
                                    )
                                  : _vm._e(),
                                _vm._v(" "),
                                _c(
                                  "router-link",
                                  {
                                    staticClass: "btn btn-info",
                                    attrs: {
                                      to:
                                        "/purchase-request/edit/" +
                                        props.row.id,
                                    },
                                  },
                                  [
                                    _c("i", {
                                      staticClass: "fas fa-pencil-alt",
                                    }),
                                  ]
                                ),
                                _vm._v(" "),
                                _c(
                                  "button",
                                  {
                                    staticClass: "btn btn-danger",
                                    on: {
                                      click: function ($event) {
                                        return _vm.deleteProduct(props.row.id)
                                      },
                                    },
                                  },
                                  [_c("i", { staticClass: "fas fa-trash" })]
                                ),
                                _vm._v(" "),
                                props.row.pick_up == 1
                                  ? _c(
                                      "router-link",
                                      {
                                        staticClass: "btn btn-primary",
                                        attrs: {
                                          to:
                                            "/purchase-request/pickup/" +
                                            props.row.id,
                                        },
                                      },
                                      [
                                        _c("i", {
                                          staticClass: "fas fa-clipboard-check",
                                        }),
                                      ]
                                    )
                                  : _vm._e(),
                              ],
                              1
                            )
                          : _c("span", [
                              _vm._v(
                                "\n                                " +
                                  _vm._s(
                                    props.formattedRow[props.column.field]
                                  ) +
                                  "\n                            "
                              ),
                            ]),
                      ]
                    },
                  },
                  {
                    key: "pagination-bottom",
                    fn: function (props) {
                      return [
                        _c(
                          "div",
                          {
                            staticClass:
                              "d-flex justify-content-between flex-wrap",
                          },
                          [
                            _c(
                              "div",
                              {
                                staticClass:
                                  "d-flex align-items-center mb-0 mt-1",
                              },
                              [
                                _c("span", { staticClass: "text-nowrap " }, [
                                  _vm._v(
                                    "\n                                    Showing 1 to\n                                    "
                                  ),
                                ]),
                                _vm._v(" "),
                                _c("b-form-select", {
                                  staticClass: "mx-1",
                                  attrs: { options: ["3", "5", "10"] },
                                  on: {
                                    input: function (value) {
                                      return props.perPageChanged({
                                        currentPerPage: value,
                                      })
                                    },
                                  },
                                  model: {
                                    value: _vm.pageLength,
                                    callback: function ($$v) {
                                      _vm.pageLength = $$v
                                    },
                                    expression: "pageLength",
                                  },
                                }),
                                _vm._v(" "),
                                _c("span", { staticClass: "text-nowrap" }, [
                                  _vm._v(
                                    " of " + _vm._s(props.total) + " entries "
                                  ),
                                ]),
                              ],
                              1
                            ),
                          ]
                        ),
                      ]
                    },
                  },
                ]),
              }),
            ],
            1
          ),
        ]),
      ]),
    ]),
  ])
}
var staticRenderFns = []
render._withStripped = true



/***/ })

}]);