"use strict";
(self["webpackChunk"] = self["webpackChunk"] || []).push([["resources_js_views_fleet_destruction_income_index_vue"],{

/***/ "./node_modules/babel-loader/lib/index.js??clonedRuleSet-5[0].rules[0].use[0]!./node_modules/vue-loader/lib/index.js??vue-loader-options!./resources/js/views/fleet/destruction_income/index.vue?vue&type=script&lang=js&":
/*!********************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib/index.js??clonedRuleSet-5[0].rules[0].use[0]!./node_modules/vue-loader/lib/index.js??vue-loader-options!./resources/js/views/fleet/destruction_income/index.vue?vue&type=script&lang=js& ***!
  \********************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _utils_notify_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../utils/notify.js */ "./resources/js/utils/notify.js");
/* harmony import */ var sweetalert2__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! sweetalert2 */ "./node_modules/sweetalert2/dist/sweetalert2.all.js");
/* harmony import */ var sweetalert2__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(sweetalert2__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! moment */ "./node_modules/moment/moment.js");
/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(moment__WEBPACK_IMPORTED_MODULE_2__);
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//



/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({
  data: function data() {
    return {
      search: '',
      headers: [{
        text: '#',
        align: 'start',
        sortable: false,
        value: 'no'
      }, {
        text: 'No Dokumen Destruction',
        value: 'destruction.document_code'
      }, {
        text: 'Total',
        value: 'total'
      }, {
        text: 'Tanggal Input',
        value: 'created_at'
      }, {
        text: 'Dibuat Oleh',
        value: 'user.first_name'
      }, {
        text: 'Note',
        value: 'note'
      }, {
        text: 'Action',
        value: 'action'
      }],
      data: [],
      total: 0
    };
  },
  created: function created() {
    var _this = this;

    var loader = this.$loading.show({
      canCancel: false
    });
    axios.get('destruction-income').then(function (response) {
      console.log(response.data);
      _this.data = response.data.data;
      _this.total = response.data.total;
    })["catch"](function (error) {
      _utils_notify_js__WEBPACK_IMPORTED_MODULE_0__.axiosError(error);
    })["finally"](function () {
      return loader.hide();
    });
  },
  methods: {
    deleteProduct: function deleteProduct(id) {
      var _this2 = this;

      sweetalert2__WEBPACK_IMPORTED_MODULE_1___default().fire({
        title: 'Are you sure?',
        text: "You won't be able to revert this!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, delete it!'
      }).then(function (result) {
        if (result.isConfirmed) {
          var loader = _this2.$loading.show({
            canCancel: false
          });

          axios["delete"]("destruction-income/" + id).then(function (response) {
            _this2.$router.go();

            _this2.flashMessage.success({
              message: response.data.message,
              time: 5000
            });
          })["catch"](function (error) {
            _utils_notify_js__WEBPACK_IMPORTED_MODULE_0__.axiosError(error);
          })["finally"](function () {
            return loader.hide();
          });
        } else {
          sweetalert2__WEBPACK_IMPORTED_MODULE_1___default().fire('Deleted Cancel');
        }
      });
    }
  },
  filters: {
    moment: function moment(date) {
      return moment__WEBPACK_IMPORTED_MODULE_2___default()(date).format('DD/MM/YYYY');
    }
  }
});

/***/ }),

/***/ "./resources/js/views/fleet/destruction_income/index.vue":
/*!***************************************************************!*\
  !*** ./resources/js/views/fleet/destruction_income/index.vue ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _index_vue_vue_type_template_id_6e558e8e___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./index.vue?vue&type=template&id=6e558e8e& */ "./resources/js/views/fleet/destruction_income/index.vue?vue&type=template&id=6e558e8e&");
/* harmony import */ var _index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./index.vue?vue&type=script&lang=js& */ "./resources/js/views/fleet/destruction_income/index.vue?vue&type=script&lang=js&");
/* harmony import */ var _node_modules_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! !../../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js */ "./node_modules/vue-loader/lib/runtime/componentNormalizer.js");





/* normalize component */
;
var component = (0,_node_modules_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_2__["default"])(
  _index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _index_vue_vue_type_template_id_6e558e8e___WEBPACK_IMPORTED_MODULE_0__.render,
  _index_vue_vue_type_template_id_6e558e8e___WEBPACK_IMPORTED_MODULE_0__.staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* hot reload */
if (false) { var api; }
component.options.__file = "resources/js/views/fleet/destruction_income/index.vue"
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (component.exports);

/***/ }),

/***/ "./resources/js/views/fleet/destruction_income/index.vue?vue&type=script&lang=js&":
/*!****************************************************************************************!*\
  !*** ./resources/js/views/fleet/destruction_income/index.vue?vue&type=script&lang=js& ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _node_modules_babel_loader_lib_index_js_clonedRuleSet_5_0_rules_0_use_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-5[0].rules[0].use[0]!../../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js& */ "./node_modules/babel-loader/lib/index.js??clonedRuleSet-5[0].rules[0].use[0]!./node_modules/vue-loader/lib/index.js??vue-loader-options!./resources/js/views/fleet/destruction_income/index.vue?vue&type=script&lang=js&");
 /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_node_modules_babel_loader_lib_index_js_clonedRuleSet_5_0_rules_0_use_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__["default"]); 

/***/ }),

/***/ "./resources/js/views/fleet/destruction_income/index.vue?vue&type=template&id=6e558e8e&":
/*!**********************************************************************************************!*\
  !*** ./resources/js/views/fleet/destruction_income/index.vue?vue&type=template&id=6e558e8e& ***!
  \**********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "render": () => (/* reexport safe */ _node_modules_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_template_id_6e558e8e___WEBPACK_IMPORTED_MODULE_0__.render),
/* harmony export */   "staticRenderFns": () => (/* reexport safe */ _node_modules_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_template_id_6e558e8e___WEBPACK_IMPORTED_MODULE_0__.staticRenderFns)
/* harmony export */ });
/* harmony import */ var _node_modules_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_template_id_6e558e8e___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=template&id=6e558e8e& */ "./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/vue-loader/lib/index.js??vue-loader-options!./resources/js/views/fleet/destruction_income/index.vue?vue&type=template&id=6e558e8e&");


/***/ }),

/***/ "./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/vue-loader/lib/index.js??vue-loader-options!./resources/js/views/fleet/destruction_income/index.vue?vue&type=template&id=6e558e8e&":
/*!*************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/vue-loader/lib/index.js??vue-loader-options!./resources/js/views/fleet/destruction_income/index.vue?vue&type=template&id=6e558e8e& ***!
  \*************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "render": () => (/* binding */ render),
/* harmony export */   "staticRenderFns": () => (/* binding */ staticRenderFns)
/* harmony export */ });
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  return _c("div", [
    _c("div", { staticClass: "row" }, [
      _c("div", { staticClass: "col-xl-12 col-md-12 mb-4" }, [
        _c(
          "div",
          { staticClass: "card border-left-success shadow h-100 py-2" },
          [
            _c("div", { staticClass: "card-body" }, [
              _c("div", { staticClass: "row no-gutters align-items-center" }, [
                _c("div", { staticClass: "col mr-2" }, [
                  _c(
                    "div",
                    {
                      staticClass:
                        "\n                        text-xs\n                        font-weight-bold\n                        text-success text-uppercase\n                        mb-1\n                    ",
                    },
                    [
                      _vm._v(
                        "\n                    Total Pemasukan\n                    "
                      ),
                    ]
                  ),
                  _vm._v(" "),
                  _c(
                    "div",
                    { staticClass: "h5 mb-0 font-weight-bold text-gray-800" },
                    [
                      _vm._v(
                        "\n                    " +
                          _vm._s(Number(_vm.total).toLocaleString()) +
                          "\n                    "
                      ),
                    ]
                  ),
                ]),
                _vm._v(" "),
                _vm._m(0),
              ]),
            ]),
          ]
        ),
      ]),
    ]),
    _vm._v(" "),
    _c("div", { staticClass: "card shadow mb-4" }, [
      _c(
        "div",
        { staticClass: "card-header py-3" },
        [
          _c("b", { staticClass: "m-0 font-weight-bold text-primary" }, [
            _vm._v("Pemasukan Asset Destruction"),
          ]),
          _vm._v(" "),
          _c(
            "router-link",
            {
              staticClass: "btn btn-primary btn-sm btn-icon-split float-right",
              attrs: { to: "asset-destruction-income/create" },
            },
            [
              _c("span", { staticClass: "icon text-white-50" }, [
                _c("i", { staticClass: "fas fa-plus" }),
              ]),
              _vm._v(" "),
              _c("span", { staticClass: "text" }, [_vm._v("Tambah Pemasukan")]),
            ]
          ),
        ],
        1
      ),
      _vm._v(" "),
      _c("div", { staticClass: "card-body" }, [
        _c("div", { staticClass: "row" }, [
          _c(
            "div",
            { staticClass: "col-12" },
            [
              _c("v-text-field", {
                attrs: {
                  "append-icon": "mdi-magnify",
                  label: "Search",
                  "single-line": "",
                  "hide-details": "",
                },
                model: {
                  value: _vm.search,
                  callback: function ($$v) {
                    _vm.search = $$v
                  },
                  expression: "search",
                },
              }),
            ],
            1
          ),
          _vm._v(" "),
          _c(
            "div",
            { staticClass: "col-12" },
            [
              [
                _c("v-data-table", {
                  staticClass: "elevation-1",
                  attrs: {
                    headers: _vm.headers,
                    "fixed-header": true,
                    "max-height": "500px",
                    items: _vm.data,
                    "items-per-page": 10,
                    search: _vm.search,
                    "item-key": "code",
                    "footer-props": {
                      showFirstLastPage: true,
                      firstIcon: "mdi-arrow-collapse-left",
                      lastIcon: "mdi-arrow-collapse-right",
                      prevIcon: "mdi-minus",
                      nextIcon: "mdi-plus",
                    },
                  },
                  scopedSlots: _vm._u(
                    [
                      {
                        key: "item.no",
                        fn: function (ref) {
                          var index = ref.index
                          return [
                            _vm._v(
                              "\n                                " +
                                _vm._s(index + 1) +
                                "\n                            "
                            ),
                          ]
                        },
                      },
                      {
                        key: "item.total",
                        fn: function (ref) {
                          var item = ref.item
                          return [
                            _vm._v(
                              "\n                                " +
                                _vm._s(Number(item.total).toLocaleString()) +
                                "\n                            "
                            ),
                          ]
                        },
                      },
                      {
                        key: "item.created_at",
                        fn: function (ref) {
                          var item = ref.item
                          return [
                            _vm._v(
                              "\n                                " +
                                _vm._s(_vm._f("moment")(item.created_at)) +
                                "\n                            "
                            ),
                          ]
                        },
                      },
                      {
                        key: "item.action",
                        fn: function (ref) {
                          var item = ref.item
                          return [
                            _c(
                              "router-link",
                              {
                                staticClass: "btn btn-info",
                                attrs: {
                                  to:
                                    "/asset-destruction-income/edit/" + item.id,
                                },
                              },
                              [_c("i", { staticClass: "fas fa-pencil-alt" })]
                            ),
                            _vm._v(" "),
                            _c(
                              "button",
                              {
                                staticClass: "btn btn-danger",
                                on: {
                                  click: function ($event) {
                                    return _vm.deleteProduct(item.id)
                                  },
                                },
                              },
                              [_c("i", { staticClass: "fas fa-trash" })]
                            ),
                          ]
                        },
                      },
                    ],
                    null,
                    true
                  ),
                }),
              ],
            ],
            2
          ),
        ]),
      ]),
    ]),
  ])
}
var staticRenderFns = [
  function () {
    var _vm = this
    var _h = _vm.$createElement
    var _c = _vm._self._c || _h
    return _c("div", { staticClass: "col-auto" }, [
      _c("i", { staticClass: "fas fa-money-bill fa-2x text-gray-300" }),
    ])
  },
]
render._withStripped = true



/***/ })

}]);