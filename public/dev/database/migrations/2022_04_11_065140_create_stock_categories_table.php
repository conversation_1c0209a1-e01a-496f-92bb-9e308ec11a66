<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateStockCategoriesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('stock_categories', function (Blueprint $table) {
            $table->id();
            $table->string('code');
            $table->string('category_description');
            $table->string('stock_type');
            $table->string('stock_act');
            $table->string('adj_glact');
            $table->string('issue_glact');
            $table->string('purch_price_varact');
            $table->string('material_useage_varac');
            $table->string('wipatch');
            $table->integer('default_tax_id');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('stock_categories');
    }
}
