<?php

namespace App\Imports;

use App\Models\Fleet\Asset;
use App\Models\Fleet\AssetDetail;
use App\Models\Fleet\AssetMasterAttribute;
use App\Models\Fleet\CategoryAsset;
use App\Models\Fleet\Company;
use App\Models\Fleet\Department;
use App\Models\Fleet\Location;
use App\Models\Fleet\MasterLocation;
use App\Models\Fleet\TypeAsset;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class ImportAsset implements ToModel, WithHeadingRow
{
    /**
    * @param Collection $collection
    */
    public function model(array $row)
    {
        $dataAsset = Asset::where('code', $row['code'])->first() ?? null;
        $data['code'] = $row['code'];
        $data['company_id'] = Company::where('name', 'ilike', "%".$row['company']."%")->first()->id ?? null;
        $data['department_id'] = Department::where('name', 'ilike', "%".$row['departement']."%")->first()->id ?? null;
        $data['location_id'] = MasterLocation::where('name', 'ilike', "%".$row['location']."%")->first()->id ?? null;
        if ($data['location_id'] == null) {
            $dataLokasi['name'] = $row['location'];
            $dataLokasi['code'] = 'custom';
            $location = MasterLocation::create($dataLokasi);
            $data['location_id'] = $location->id;
        }
        $data['company'] = $row['company'];
        $data['created_asset'] = now()->format('Y-m-d');
        $data['pickup_date'] = now()->format('Y-m-d');
        $data['status'] = 1;
        $data['in_stock'] = 1;
        $category_asset = CategoryAsset::where('name', $row['category_asset'])->first();
        $data['category_asset_id'] = $category_asset->id ?? null;
        $data['type_asset_id'] = TypeAsset::where('name', 'ilike', "%".$row['type_asset']."%")->first()->id ?? null;
        if ($dataAsset) {
            $dataAsset->update($data);

            $assetMasterAttribute = AssetMasterAttribute::where('category_id', $category_asset->id)->get();
            foreach ($assetMasterAttribute as $key => $value) {
                $assetDetails['attribute_code'] = $value->code;
                $assetDetails['asset_id'] = $dataAsset->id;
                $assetDetails['value'] = $row[$value->code] ?? null;
                
                $findAssetDetail = AssetDetail::where('asset_id', $dataAsset->id)->where('attribute_code', $value->code)->first();
                if ($findAssetDetail) {
                    $assetDetail = $findAssetDetail->update($assetDetails);
                }else{
                    $assetDetail = AssetDetail::create($assetDetails);
                }
            }
        }else{
            $asset = Asset::create($data);

            $assetMasterAttribute = AssetMasterAttribute::where('category_id', $category_asset->id)->get();
            foreach ($assetMasterAttribute as $key => $value) {
                $assetDetails['id'] = AssetDetail::max('id')+1;
                $assetDetails['attribute_code'] = $value->code;
                $assetDetails['asset_id'] = $asset->id;
                $assetDetails['value'] = $row[$value->code] ?? null;
                $assetDetail = AssetDetail::create($assetDetails);
            }
        }
    }
}
