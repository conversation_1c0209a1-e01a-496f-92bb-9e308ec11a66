<?php

namespace App\Models;

use App\Models\Fleet\AnswerQuestionUser;
use App\Models\Fleet\Department;
use App\Models\Fleet\Company;
use App\Models\Fleet\MasterLocation;
use App\Models\Fleet\Role;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Passport\HasApiTokens;
use Spatie\Permission\Traits\HasRoles;
use Spatie\Permission\Models\Permission;
use Illuminate\Support\Facades\Auth;

class User extends Authenticatable implements MustVerifyEmail
{
    use Notifiable, HasApiTokens, HasRoles;

    protected $appends = ['must_verify_email'];
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'code'
        ,'email'
        ,'nip'
        ,'nik'
        ,'full_name'
        ,'first_name'
        ,'last_name'
        ,'company_id'
        ,'company_name'
        ,'department_id'
        ,'department_name'
        ,'location_type_id'
        ,'location_type_name'
        ,'location_id'
        ,'location_name'
        ,'job_id'
        ,'jabatan_name'
        ,'section_id'
        ,'section_name'
        ,'sub_section_id'
        ,'sub_section_name'
        ,'blood_type'
        ,'emergency_number'
        ,'birth'
        ,'type_driving_license'
        ,'driving_license_number'
        ,'validity_driving_license'
        ,'avatar'
        ,'age'
        ,'employee_status'
        ,'ket_update'
        ,'role_id'
        ,'role'
        ,'username'
        ,'password'
    ];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
    ];

    /**
     * Determine if the user is an administrator.
     *
     * @return bool
     */
    public function getMustVerifyEmailAttribute()
    {
        return config('auth.must_verify_email');
    }

    public function department()
    {
        return $this->belongsTo(Department::class, 'department_id', 'code')->withDefault();
    }

    public function Company()
    {
        return $this->belongsTo(Company::class, 'company_id', 'code')->withDefault();
    }

    public function branch()
    {
        return $this->belongsTo('App\Models\Fleet\Branch', 'branch_id', 'id')->withDefault();
    }

    public function role()
    {
        return $this->belongsTo(Role::class, 'role_id', 'id')->withDefault();
    }

    public function driver()
    {
        return $this->belongsTo('App\Models\Fleet\Driver', 'id', 'user_id')->withDefault();
    }

    public function section()
    {
        return $this->belongsTo('App\Models\Fleet\Section', 'section_id', 'code')->withDefault();
    }

    public function subSection()
    {
        return $this->belongsTo('App\Models\Fleet\SubSection', 'sub_section_id', 'code')->withDefault();
    }

    public function getAllPermissionsAttribute() {
        $permissions = [];
        foreach (Permission::all() as $permission) {
            if (Auth::user()->can($permission->name)) {
              $permissions[] = $permission->name;
            }
        }
        return $permissions;
    }

    public function answerQuestionUser()
    {
        return $this->hasMany(AnswerQuestionUser::class);
    }

    public function location()
    {
        return $this->belongsTo(MasterLocation::class, 'location_id', 'code');
    }
}
