<?php

namespace App\Models\GA;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class StockRequestItems extends Model
{
    use SoftDeletes;

    protected $table = 'ga.stock_request_items';
    protected $guarded = [];

    public function purchase()
    {
        return $this->belongsTo('App\Models\GA\StockRequest', 'dispatch_id', 'id')->withDefault();
    }

    public function item()
    {
        return $this->belongsTo('App\Models\Fleet\StockMaster', 'stock_id', 'id')->withDefault();
    }

    public function group()
    {
        return $this->belongsTo('App\Models\Fleet\StockMasterGroup', 'group_id', 'id')->withDefault();
    }

    public function class()
    {
        return $this->belongsTo('App\Models\Fleet\StockMasterClass', 'class_id', 'id')->withDefault();
    }
}
