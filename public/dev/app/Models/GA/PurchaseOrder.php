<?php

namespace App\Models\GA;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class PurchaseOrder extends Model
{
    use SoftDeletes;

    protected $table = 'ga.purchase_orders';
    protected $guarded = [];

    public function ComparativeQuotation()
    {
        return $this->belongsTo('App\Models\GA\ComparativeQuotation', 'comparative_quotation_id', 'id')->withDefault();
    }

    public function detail()
    {
        return $this->hasMany('App\Models\GA\PurchaseOrderDetail', 'po_id', 'id');
    }

    public function payment()
    {
        return $this->hasMany('App\Models\GA\PurchaseOrderPayment', 'po_id', 'id');
    }

    public function shipper()
    {
        return $this->belongsTo('App\Models\Fleet\Shipper', 'delivery_by', 'id')->withDefault();
    }

    public function user()
    {
        return $this->belongsTo('App\Models\User', 'initiator', 'id')->withDefault();
    }
    
    public function supplier()
    {
        return $this->belongsTo('App\Models\Fleet\Supplier', 'supplier_no', 'id');
    }

    public function location()
    {
        return $this->belongsTo('App\Models\Fleet\Location', 'into_stock_location', 'id');
    }

    public function PaymentTerm()
    {
        return $this->belongsTo('App\Models\Fleet\PaymentTerm', 'payment_terms', 'id');
    }

    public function history()
    {
        return $this->belongsTo('App\Models\GA\PurchaseOrderApprovalHistory', 'id', 'purchase_order_id');
    }
}
