<?php

namespace App\Models\GA;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class StockMove extends Model
{
    use SoftDeletes;

    protected $table = 'ga.stock_moves';
    protected $guarded = [];

    public function type()
    {
        return $this->belongsTo('App\Models\Fleet\SysType', 'type', 'type_id')->withDefault();
    }

    public function stock()
    {
        return $this->belongsTo('App\Models\Fleet\StockMaster', 'stock_id', 'id');
    }

    public function location()
    {
        return $this->belongsTo('App\Models\Fleet\Location', 'loc_code', 'id');
    }
}
