<?php

namespace App\Models\GA;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ComparativeQuotation extends Model
{
    use SoftDeletes;

    protected $table = 'ga.comparative_quotations';
    protected $guarded = [];

    public function purchaseRequest()
    {
        return $this->belongsTo('App\Models\GA\StockRequest', 'stock_request_id', 'id')->withDefault();
    }

    public function user()
    {
        return $this->belongsTo('App\Models\User', 'requester', 'id')->withDefault();
    }

    public function items()
    {
        return $this->hasMany('App\Models\GA\ComparativeQuotationItem', 'comparative_quotation_id', 'id');
    }

    public function details()
    {
        return $this->hasMany('App\Models\GA\ComparativeQuotationDetail', 'comparative_quotation_id', 'id');
    }

    public function termin()
    {
        return $this->belongsTo('App\Models\GA\ComparativeQuotationTermins', 'id', 'comparative_quotation_id')->withDefault();
    }
}
