<?php

namespace App\Models\GA;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class PurchaseOrderDetail extends Model
{
    use SoftDeletes;

    protected $table = 'ga.purchase_order_details';
    protected $guarded = [];

    public function stock()
    {
        return $this->belongsTo('App\Models\Fleet\StockMaster', 'item_code', 'id');
    }

    public function chartMaster()
    {
        return $this->belongsTo('App\Models\Fleet\ChartMaster', 'gl_code', 'accountcode');
    }

    public function fixedAsset()
    {
        return $this->belongsTo('App\Models\Fleet\FixedAsset', 'asset_id', 'id');
    }
}
