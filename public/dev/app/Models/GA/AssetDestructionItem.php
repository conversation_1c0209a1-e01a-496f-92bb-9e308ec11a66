<?php

namespace App\Models\GA;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class AssetDestructionItem extends Model
{
    use SoftDeletes;

    protected $table = 'ga.asset_destruction_items';
    protected $guarded = [];

    public function locStock()
    {
        return $this->belongsTo('App\Models\Fleet\LocStock', 'locstock_id', 'id');
    }

    public function user()
    {
        return $this->belongsTo('App\Models\User', 'user_responsible', 'id')->withDefault();
    }
}
