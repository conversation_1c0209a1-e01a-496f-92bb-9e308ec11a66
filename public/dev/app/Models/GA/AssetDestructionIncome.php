<?php

namespace App\Models\GA;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class AssetDestructionIncome extends Model
{
    use SoftDeletes;

    protected $table = 'ga.asset_destruction_incomes';
    protected $guarded = [];

    public function destruction()
    {
        return $this->belongsTo('App\Models\GA\AssetDestruction', 'asset_destruction_id', 'id');
    }

    public function user()
    {
        return $this->belongsTo('App\Models\User', 'created_by', 'id')->withDefault();
    }
}
