<?php

namespace App\Models\Fleet;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Section extends Model
{
    use SoftDeletes;

    protected $table = 'public.sections';
    protected $guarded = [];

    public function department()
    {
        return $this->belongsTo('App\Models\Fleet\Department', 'department_id', 'id')->withDefault();
    }

    public function subSection()
    {
        return $this->hasMany('App\Models\Fleet\SubSection', 'section_id', 'id');
    }
}
