<?php

namespace App\Models\Fleet;

use App\Models\User;
use Illuminate\Database\Eloquent\Model;
use App\FleetMaster\Vehicle;

class Driver extends Model
{
    protected $table = 'drivers';
    protected $fillable = ['user_id',
                            'vehicle_id',
                            'join_date',
                            'status',
                            'available',
                            'emergency_contact',
                            'emergency_name',
                            'birth',
                            'type_sim_id',
                            'master_location_id'
                            ,'sim_validity_period'
                            ,'driving_license'];

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id')->withDefault();
    }

    public function vehicle()
    {
        return $this->belongsTo(Vehicle::class, 'vehicle_id', 'id')->withDefault();
    }

    public function type_sim()
    {
        return $this->belongsTo(TypeSim::class, 'type_sim_id', 'id')->withDefault();
    }

    public function masterLocation()
    {
        return $this->belongsTo(MasterLocation::class, 'master_location_id', 'id')->withDefault();
    }
}
