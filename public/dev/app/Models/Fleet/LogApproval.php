<?php

namespace App\Models\Fleet;

use App\Models\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;

class LogApproval extends Model
{
    protected $fillable = ['ref_id', 'user_id', 'slug', 'user_role', 'status_approval'];

    public static function createLogApproval($status_approval, $slug = null, $user_role = null, $ref_id = null, $user_id = null)
    {
        try {
            if ($user_id == null) {
                $user_id = Auth::user()->id;
            }
            LogApproval::create([
                'ref_id' => $ref_id,
                'user_id' => $user_id,
                'slug' => $slug,
                'user_role' => $user_role,
                'status_approval' => $status_approval
            ]);
            
            return true;
        } catch (\Throwable $th) {
            return false;
        }
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }
}
