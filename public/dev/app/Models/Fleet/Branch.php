<?php

namespace App\Models\Fleet;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Branch extends Model
{
    use SoftDeletes;
    
    protected $table = 'public.branchs';
    protected $guarded = [];

    public function company()
    {
        return $this->belongsTo('App\Models\Fleet\Company', 'company_id', 'id');
    }

    public function divisi()
    {
        return $this->hasMany('App\Models\Fleet\Divisi', 'branch_id', 'id');
    }
}
