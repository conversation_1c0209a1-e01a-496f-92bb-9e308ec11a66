<?php

namespace App\Models\Fleet;

use Illuminate\Database\Eloquent\Model;

class RequestFormDetail extends Model
{
    protected $fillable = ['request_form_id',
                            'category_item_id', 
                            'type_item_id', 
                            'spesifikasi', 
                            'qty', 
                            'uom', 
                            'estimate_price', 
                            'budget', 
                            'reminder_budget', 
                            'user_id', 
                            'note', 
                            'type_user',
                            'qty_item_approve',
                            'note_manager',
                            'qty_in_stock',
                            'qty_reject'.
                            'qty_request_buy',
                            'note_ga',
                            'qty_create',
                            'po_id',
                            'qty_gr',
                            'qty_in',
                            'outstanding'];

    public function categoryItem()
    {
        return $this->belongsTo(CategoryItem::class, 'category_item_id', 'id');
    }

    public function typeItem()
    {
        return $this->belongsTo(TypeItem::class, 'type_item_id', 'id');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public function requestForm()
    {
        return $this->belongsTo(RequestForm::class, 'request_form_id', 'id');
    }
}
