<?php

namespace App\Models\Fleet;

use Illuminate\Database\Eloquent\Model;

class Company extends Model
{
    protected $table = 'public.companies';
    protected $guarded = [];

    public function currency()
    {
        return $this->belongsTo('App\Models\Fleet\Currency', 'currency_id', 'id');
    }

    public function branch()
    {
        return $this->hasMany('App\Models\Fleet\Branch', 'company_id', 'id');
    }

    public function department()
    {
        return $this->hasMany('App\Models\Fleet\Department', 'company_id', 'id');
    }
}
