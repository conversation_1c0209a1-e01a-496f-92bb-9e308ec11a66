<?php

namespace App\Models\Fleet;

use App\FleetMaster\Vehicle;
use Illuminate\Database\Eloquent\Model;

class QuestionMark extends Model
{
    protected $table = 'public.question_marks';
    protected $guarded = [];
    protected $fillable = ['id', 'user_id', 'point', 'status_vehicle_id', 'slug'];

    public function statusVehicle()
    {
        return $this->belongsTo(StatusVehicle::class, 'status_vehicle_id', 'id');
    }

    public function kendaraan()
    {
        return $this->belongsTo(Vehicle::class, 'kendaraan_id', 'id');
    }
}
