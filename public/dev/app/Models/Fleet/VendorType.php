<?php

namespace App\Models\Fleet;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class VendorType extends Model
{
    use SoftDeletes;
    protected $table = 'public.vendor_types';
    protected $guarded = [];

    public function type()
    {
        return $this->belongsTo('App\Models\Fleet\SupplierType', 'supp_type', 'id')->withDefault();
    }

    public function currency()
    {
        return $this->belongsTo('App\Models\Fleet\Currency', 'currency_id', 'id')->withDefault();
    }

    public function taxGroup()
    {
        return $this->belongsTo('App\Models\Fleet\TaxGroup', 'tax_group_id', 'id')->withDefault();
    }

    public function factorCompany()
    {
        return $this->belongsTo('App\Models\Fleet\FactorCompany', 'factor_company_id', 'id')->withDefault();
    }
}
