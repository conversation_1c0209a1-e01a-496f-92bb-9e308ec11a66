<?php

namespace App\Models\Fleet;

use Illuminate\Database\Eloquent\Model;

class Question extends Model
{
    protected $table = 'public.questions';
    protected $guarded = [];

    public function answer()
    {
        return $this->hasMany(AnswerQuestion::class);
    }

    public function categoryQuestion()
    {
        return $this->belongsTo(CategoryQuestion::class, 'category_question_id', 'id');
    }
}
