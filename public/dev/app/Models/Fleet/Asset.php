<?php

namespace App\Models\Fleet;

use App\Workshop\Insurance;
use Illuminate\Database\Eloquent\Model;

class Asset extends Model
{   
    protected $table = 'public.assets';
    protected $guarded = [];
    protected $fillable = ['code', 'company', 'created_asset', 'pickup_date', 'category_asset_id', 'type_asset_id', 'km_actual', 'company_id', 'department_id', 'location_id', 'maintenance', 'maintenance_odometer', 'trip', 'user_id', 'insurance_id'];

    public function po()
    {
        return $this->belongsTo('App\Models\GA\PurchaseOrder', 'po_id', 'id');
    }

    public function pr()
    {
        return $this->belongsTo('App\Models\GA\StockRequest', 'pr_id', 'id');
    }

    public function stock()
    {
        return $this->belongsTo('App\Models\Fleet\StockMaster', 'stock_id', 'id');
    }

    public function createdBy()
    {
        return $this->belongsTo('App\Models\User', 'created_by', 'id')->withDefault();
    }

    public function requester()
    {
        return $this->belongsTo('App\Models\User', 'requester', 'id')->withDefault();
    }

    public function onHand()
    {
        return $this->belongsTo('App\Models\User', 'on_hand_id', 'id')->withDefault();
    }

    public function group()
    {
        return $this->belongsTo('App\Models\Fleet\AssetMasterGroup', 'group_id', 'id');
    }

    public function class()
    {
        return $this->belongsTo('App\Models\Fleet\AssetMasterClass', 'class_id', 'id');
    }
    
    public function categoryAsset()
    {
        return $this->belongsTo(CategoryAsset::class, 'category_asset_id', 'id');
    }

    public function typeAsset()
    {
        return $this->belongsTo(TypeAsset::class, 'type_asset_id', 'id');
    }

    public function assetDetail()
    {
        return $this->hasMany(AssetDetail::class);
    }

    public function companies()
    {
        return $this->belongsTo(Company::class, 'company_id', 'id');
    }

    public function department()
    {
        return $this->belongsTo(Department::class, 'department_id', 'id');
    }

    public function location()
    {
        return $this->belongsTo(MasterLocation::class, 'location_id', 'id');
    }

    public function insurance()
    {
        return $this->belongsTo(Insurance::class, 'insurance_id', 'id');
    }
}
