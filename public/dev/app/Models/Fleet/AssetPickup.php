<?php

namespace App\Models\Fleet;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class AssetPickup extends Model
{
    use SoftDeletes;
    
    protected $table = 'public.asset_pickups';
    protected $guarded = [];

    public function purchaseRequest()
    {
        return $this->belongsTo('App\Models\GA\StockRequest', 'stock_request_id', 'id')->withDefault();
    }

    public function receiver()
    {
        return $this->belongsTo('App\Models\User', 'receive_by', 'id')->withDefault();
    }

    public function handover()
    {
        return $this->belongsTo('App\Models\User', 'handover_by', 'id')->withDefault();
    }
}
