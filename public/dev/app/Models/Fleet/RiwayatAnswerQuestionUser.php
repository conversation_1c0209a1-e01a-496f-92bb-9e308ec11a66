<?php

namespace App\Models\Fleet;

use App\Models\User;
use Illuminate\Database\Eloquent\Model;

class RiwayatAnswerQuestionUser extends Model
{
    protected $fillable = ['user_id', 'answer_question_user_id', 'status_koordinator', 'note', 'status_k3'];

    public function approveBy()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public function answerQuestionUser()
    {
        return $this->belongsTo(AnswerQuestionUser::class, 'answer_question_user_id', 'id');
    }
}
