<?php

namespace App\Models\Fleet;

use App\Models\User;
use Illuminate\Database\Eloquent\Model;

class PurchaseOrder extends Model
{
    protected $fillable = [
        'user_id',
        'type',
        'tnc',
        'no_po',
        'department_id',
        'location_id',
        'company_id',
        'supplier_id',
        'approve_po',
        'approve_pr',
        'type_submission',
        'deliver_id',
        'invoice_address_id',
        'top_id',
        'tax_id',
        'total',
        'sub_total',
        'ppn',
        'va_number',
        'price_ongkir',
        'remark',
        'no_rek',
        'type_bank',
        'rek_name',
        'user_pjd_id',
        'doc_gr',
        'no_va',
        'no_tf',
        'no_ca',
        'no_gr',
    ];

    public function company()
    {
        return $this->belongsTo(Company::class, 'company_id', 'id');
    }

    public function department()
    {
        return $this->belongsTo(Department::class, 'department_id', 'id');
    }

    public function location()
    {
        return $this->belongsTo(MasterLocation::class, 'location_id', 'id');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public function userPenanggungJawab()
    {
        return $this->belongsTo(User::class, 'user_pjd_id', 'id');
    }

    public function supplier()
    {
        return $this->belongsTo(Supplier::class, 'supplier_id', 'id');
    }

    public function items()
    {
        return $this->hasMany(PurchaseOrderDetail::class);
    }

    public function deliver()
    {
        return $this->belongsTo(Deliver::class, 'deliver_id', 'id');
    }

    public function invoiceAddress()
    {
        return $this->belongsTo(InvoiceAddress::class, 'invoice_address_id', 'id');
    }
}
