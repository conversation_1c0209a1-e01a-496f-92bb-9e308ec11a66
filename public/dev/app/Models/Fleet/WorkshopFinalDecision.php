<?php

namespace App\Models\Fleet;

use App\Workshop\Workshop;
use Illuminate\Database\Eloquent\Model;

class WorkshopFinalDecision extends Model
{
    protected $fillable = ['gaho_id', 'note_gaho', 'status_gaho', 'workshop_id', 'koordinator_id', 'note_koordinator', 'status_koordinator'];

    public function workshop()
    {
        return $this->belongsTo(Workshop::class, 'workshop_id', 'id');
    }
}
