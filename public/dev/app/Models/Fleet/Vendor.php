<?php

namespace App\Models\Fleet;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Vendor extends Model
{
    use SoftDeletes;
    
    protected $table = 'public.vendors';
    protected $guarded = [];

    public function type()
    {
        return $this->belongsTo(VendorType::class, 'vendor_type', 'id')->withDefault();
    }

    public function currency()
    {
        return $this->belongsTo(Currency::class, 'currency_id', 'id')->withDefault();
    }

    public function taxGroup()
    {
        return $this->belongsTo(TaxGroup::class, 'tax_group_id', 'id')->withDefault();
    }

    public function factorCompany()
    {
        return $this->belongsTo(FactorCompany::class, 'factor_company_id', 'id')->withDefault();
    }
}
