<?php

namespace App\Models\Fleet;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class StockOpname extends Model
{
    use SoftDeletes;
    
    protected $table = 'public.stock_opnames';
    protected $guarded = [];

    public function details()
    {
        return $this->hasMany('App\Models\Fleet\StockOpnameDetail', 'stock_opname_id', 'id');
    }

    public function location()
    {
        return $this->belongsTo('App\Models\Fleet\Location', 'location_id', 'id')->withDefault();
    }

    public function user()
    {
        return $this->belongsTo('App\Models\User', 'user_id', 'id')->withDefault();
    }
}
