<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\WithDrawings;
use PhpOffice\PhpSpreadsheet\Worksheet\Drawing;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Events\AfterSheet;

class ExportPerawatanKendaraan implements FromView, WithDrawings, WithStyles, WithColumnWidths, WithEvents
{
    /**
    * @return \Illuminate\Support\Collection
    */
    public function view(): View
    {
        return view('export.formPerawatanKendaraan');
    }

    public function drawings()
    {
        $drawing = new Drawing();
        // $drawing->setName('Logo');
        // $drawing->setDescription('This is my logo');
        $drawing->setPath(public_path('/assets/images/logoljr.png'));
        $drawing->setHeight(90);
        $drawing->setCoordinates('A1');
        
        $drawing2 = new Drawing();
        $drawing2->setPath(public_path('/assets/images/perawatan.png'));
        $drawing2->setHeight(25);
        $drawing2->setCoordinates('C11');
        
        $drawing3 = new Drawing();
        $drawing3->setPath(public_path('/assets/images/perbaikan.png'));
        $drawing3->setHeight(25);
        $drawing3->setCoordinates('E11');

        return [$drawing, $drawing2, $drawing3];
    }

    public function styles(Worksheet $sheet)
    {
        $sheet->getStyle('C1')->getFont()->setBold(true);
        $sheet->getStyle('A12')->getFont()->setBold(true);
        $sheet->getStyle('A21')->getFont()->setBold(true);
        $sheet->getStyle('A27')->getFont()->setBold(true);
        $sheet->getStyle('G27')->getFont()->setBold(true);
        $sheet->getStyle('H27')->getFont()->setBold(true);
        $sheet->getStyle('H1:H31')->getFont()->setSize(10);
        $sheet->getStyle('G1:G31')->getFont()->setSize(10);
        $sheet->getStyle('F1:F31')->getFont()->setSize(10);
        $sheet->getStyle('E1:E31')->getFont()->setSize(10);
        $sheet->getStyle('D1:D31')->getFont()->setSize(10);
        $sheet->getStyle('C6:C31')->getFont()->setSize(10);
        $sheet->getStyle('B1:B31')->getFont()->setSize(10);
        $sheet->getStyle('A1:A31')->getFont()->setSize(10);
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function(AfterSheet $event) {
                $event->sheet->getDelegate()->getStyle('H6')
                        ->getFill()
                        ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
                        ->getStartColor()
                        ->setARGB('bfbfbf');
                $event->sheet->getDelegate()->getStyle('H12')
                        ->getFill()
                        ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
                        ->getStartColor()
                        ->setARGB('bfbfbf');
                $event->sheet->getDelegate()->getStyle('H17')
                        ->getFill()
                        ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
                        ->getStartColor()
                        ->setARGB('bfbfbf');
                $event->sheet->getDelegate()->getStyle('H21')
                        ->getFill()
                        ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
                        ->getStartColor()
                        ->setARGB('bfbfbf');
                $event->sheet->getDelegate()->getStyle('G27')
                        ->getFill()
                        ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
                        ->getStartColor()
                        ->setARGB('bfbfbf');
                $event->sheet->getDelegate()->getStyle('H27')
                        ->getFill()
                        ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
                        ->getStartColor()
                        ->setARGB('bfbfbf');
            },
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 20,
            'B' => 1,            
            'C' => 20,            
            'D' => 20,            
            'E' => 20,            
            'F' => 20,            
            'G' => 20,            
            'H' => 20,            
            'I' => 20,            
        ];
    }
}
