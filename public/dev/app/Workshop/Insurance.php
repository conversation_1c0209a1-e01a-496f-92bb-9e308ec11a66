<?php

namespace App\Workshop;

use App\Models\Fleet\Company;
use App\Models\Fleet\Department;
use App\Models\Fleet\Location;
use App\Models\Fleet\MasterLocation;
use Illuminate\Database\Eloquent\Model;

class Insurance extends Model
{
    protected $fillable = ['name', 'address', 'phone_insurance', 'phone_pic', 'limit_claim', 'coverage_type'];

    // public function company()
    // {
    //     return $this->belongsTo(Company::class, 'company_id', 'id');
    // }

    // public function location()
    // {
    //     return $this->belongsTo(MasterLocation::class, 'location_id', 'id');
    // }

    // public function department()
    // {
    //     return $this->belongsTo(Department::class, 'department_id', 'id');
    // }
}
