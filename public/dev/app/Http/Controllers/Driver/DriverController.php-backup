<?php

namespace App\Http\Controllers\Driver;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Fleet\Vehicle;
use App\Models\Fleet\Driver;
use App\Models\Fleet\Company;
use App\Models\Fleet\Department;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;

class DriverController extends Controller
{
    public function index()
    {
        $data = User::with('driver')->where("role", "Driver")->orderBy("first_name", "asc")->get();
        return response()->json($data);
    }

    public function create()
    {
        $company = Company::all();
        $department = Department::all();
        $vehicle = Vehicle::all();

        return response()->json(['company' => $company, 'department' => $department, 'vehicle' => $vehicle]);
    }

    public function component()
    {
        $data = User::where("type_account", "driver")->orderBy("first_name", "asc")->get(['id','username']);
        return response()->json($data);
    }

    public function edit($id)
    {
        $user = User::with('driver')->findOrFail($id);
        if (!isset($user->driver->user_id)) {
            $driver = new Driver;
            $driver->user_id = $user->id;
            $driver->vehicle_id = Vehicle::orderBy('id','desc')->first()->id;
            $driver->save();

            $user = User::with('driver')->findOrFail($id);
        }
        $company = Company::all();
        $department = Department::all();
        $vehicle = Vehicle::all();

        return response()->json(['company' => $company, 'department' => $department, 'vehicle' => $vehicle, 'user' => $user]);
    }

    public function store(Request $request)
    {
        try {

            $user = new User();
            $user->first_name = $request->get('first_name');
            $user->last_name = $request->get('last_name');
            $user->email = $request->get('email');
            $user->company_id = $request->get('company_id');
            $user->branch_id = $request->get('branch_id');
            $user->department_id = $request->get('department_id');
            $user->section_id = $request->get('section_id');
            $user->sub_section_id = $request->get('sub_section_id');
            $role = 'Driver';
            $user->role = $role;
            $password = $request->get('password');
            if ($password) {
                $user->password = Hash::make($password);
            }
            
            if($user->save()){
                $user->assignRole($role);

                $input_driver = $request->input('driver');

                $driver = new Driver;
                $driver->user_id = $user->id;
                $driver->vehicle_id = $input_driver['vehicle_id'];
                $driver->join_date = $input_driver['join_date'];
                $driver->emergency_contact = $input_driver['emergency_contact'];
                $driver->status = $input_driver['status'];
                $driver->available = $input_driver['available'];
                $driver->emergency_name = $input_driver['emergency_name'];
                $driver->sim_validity_period = $input_driver['sim_validity_period'];
                $driver->driver_height = $input_driver['driver_height'];
                $driver->driving_license = $input_driver['driving_license'];
                $driver->save();
            }
            
            return response()->json(['success' => true, 'message' => 'berhasil tambah data driver']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    public function update(Request $request, $id)
    {
        try {
            $user = User::find($id);
            $user->first_name = $request->get('first_name');
            $user->last_name = $request->get('last_name');
            $user->email = $request->get('email');
            $user->company_id = $request->get('company_id');
            $user->branch_id = $request->get('branch_id');
            $user->department_id = $request->get('department_id');
            $user->section_id = $request->get('section_id');
            $user->sub_section_id = $request->get('sub_section_id');
            $password = $request->get('password');
            if ($password) {
                $user->password = Hash::make($password);
            }
            
            if($user->save()){

                $input_driver = $request->input('driver');

                $driver = Driver::where('user_id',$user->id)->first();
                $driver->vehicle_id = $input_driver['vehicle_id'];
                $driver->join_date = $input_driver['join_date'];
                $driver->emergency_contact = $input_driver['emergency_contact'];
                $driver->status = $input_driver['status'];
                $driver->available = $input_driver['available'];
                $driver->emergency_name = $input_driver['emergency_name'];
                $driver->sim_validity_period = $input_driver['sim_validity_period'];
                $driver->driver_height = $input_driver['driver_height'];
                $driver->driving_license = $input_driver['driving_license'];
                $driver->save();
            }
            
            return response()->json(['success' => true, 'message' => 'berhasil update data driver']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    public function delete($id)
    {
        try {
            User::findOrFail($id)->delete();
            return response()->json(['success' => true, 'message' => 'Berhasil delete data']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }
}
