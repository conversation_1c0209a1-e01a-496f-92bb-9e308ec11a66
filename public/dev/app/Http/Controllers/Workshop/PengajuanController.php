<?php

namespace App\Http\Controllers\Workshop;

use App\Exports\ExportPerawatanKendaraan;
use App\Exports\ExportWorkshop;
use App\FleetMaster\Vehicle;
use App\Http\Controllers\Controller;
use App\Http\Helper;
use App\Models\Fleet\AnswerQuestion;
use App\Models\Fleet\AnswerQuestionDetail;
use App\Models\Fleet\AnswerQuestionUser;
use App\Models\Fleet\Asset;
use App\Models\Fleet\AssetDetail;
use App\Models\Fleet\CategoryQuestion;
use App\Models\Fleet\Company;
use App\Models\Fleet\Department;
use App\Models\Fleet\HistoryWorkshop;
use App\Models\Fleet\KategoriKerusakan;
use App\Models\Fleet\Location;
use App\Models\Fleet\MasterLocation;
use App\Models\Fleet\Question;
use App\Models\Fleet\RequestForm;
use App\Models\Fleet\RiwayatAnswerQuestionUser;
use App\Models\Fleet\SparepartUse;
use App\Models\Fleet\StatusPerbaikan;
use App\Models\Fleet\StatusVehicle;
use App\Models\Fleet\TypeAsset;
use App\Models\Fleet\WorkEstimate;
use App\Models\Fleet\WorkshopDecision;
use App\Models\Fleet\WorkshopDetail;
use App\Models\Fleet\WorkshopFinalDecision;
use App\Models\Fleet\WorkshopInspection;
use App\Models\Fleet\WorkshopServiceDetail;
use App\Models\Fleet\WorkshopSla;
use App\Models\User;
use App\Transaction\PurchaseItem;
use App\Transaction\Transaction;
use App\Workshop\Workshop;
use App\Workshop\WorkshopType;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Facades\Excel;
use phpDocumentor\Reflection\PseudoTypes\LowercaseString;

class PengajuanController extends Controller
{
    public function index()
    {
        $list_pengajuan = Workshop::where('keputusan_ga_ho', 1)->with("vehicle", "driver", "technical", "answerQuestionUser")->orderBy("id", "desc")->get();
        // return $list_pengajuan;
        $technical =  User::where("type_account", "technical")->orderBy("first_name", "asc")->get(['id', 'username']);

        $list = array();
        foreach ($list_pengajuan as $k) {
            $type = $k->vehicle->category->name ?? '';
            $color = $k->vehicle->color->name ?? '';
            $license_no = $k->vehicle->license_no ?? '';

            $item['id'] = $k->id;
            $item['no_seri'] = $k->no_seri;
            $item['kendaraan_name']   = $type . ' Warna ' . $color . '- Platno ' . $license_no;
            $item['driver_name']  = ($k->driver->first_name ?? null).' '. ($k->driver->last_name ?? null);
            $item['detail'] = $k->detail;
            $item['kategori_kerusakan_id'] = $k->kategoriKerusakan->name ?? '-';
            $item['status'] = $k->status;
            $item['approval_by'] = $k->technical->username ?? '-';
            $item['status_kendaraan'] = $k->answerQuestionUser->statusVehicle->color ?? '-';
            $list[]     = $item;
        }
        // dd($list);
        $data = [
            'list'  => $list,
            'user'  => $technical
        ];

        return response()->json($data, 200);
    }

    public function create()
    {
        $kendaraan = Vehicle::all();
        $list = array();
        foreach ($kendaraan as $k) {
            $item['id'] = $k->id;
            $item['type']   = $k->category->name ?? '';
            $item['color']  = $k->color->name ?? '';
            $item['platno'] = $k->license_no;
            $list[]     = $item;
        }
        $data = [
            'vehicle'  => $list,
        ];

        return response()->json($data, 200);
    }

    public function elements($id)
    {
        $pengajuan = Workshop::findOrFail($id);
        $technical =  User::where("type_account", "technical")->orderBy("first_name", "asc")->get(['id', 'first_name', 'last_name']);
        $katagoriKerusakan = KategoriKerusakan::get();
        $status_perbaikan = StatusPerbaikan::get();
        $jenis_service = WorkshopType::get();
        $data = [
            'pengajuan'  => $pengajuan,
            'technical' => $technical,
            'kategoriKerusakan' => $katagoriKerusakan,
            'status_perbaikan' => $status_perbaikan,
            'jenis_service' => $jenis_service
        ];

        return response()->json($data, 200);
    }

    public function approval($id)
    {
        $kendaraan = Vehicle::all();
        $list = array();
        foreach ($kendaraan as $k) {
            $item['id'] = $k->id;
            $item['type']   = $k->category->name ?? '';
            $item['color']  = $k->color->name ?? '';
            $item['platno'] = $k->license_no;
            $list[]     = $item;
        }
        $data = [
            'vehicle'  => $list,
        ];

        return response()->json($data, 200);
    }

    public function edit($id)
    {
        $data['workshop'] = Workshop::find($id);
        $detail = Transaction::where('workshop_id', $id)->first();
        $data['workshop']['estimation_date'] = $detail->estimation_date;
        $data['workshop']['status'] = $detail->status;
        $data['workshop']['status_take'] = $detail->status_take;
        $data['workshop']['other_change'] = $detail->other_change;
        $data['transaction'] = Transaction::where('workshop_id', $id)->get();

        return response()->json($data);
    }

    public function getDriver($id)
    {
        $kendaraan = Vehicle::findOrFail($id);
        $list = array();
        foreach ($kendaraan->driver as $k) {
            $item['id'] = $k->driver_id;
            $item['name']   = $k->driver->username ?? '';
            $list[]     = $item;
        }
        return response()->json(['supir' => $list], 200);
    }

    public function store(Request $request, $condition)
    {
        try {
            $validator = Validator::make($request->all(), [
                'driver_id' => 'required',
                'vehicle_id' => 'required',
            ]);

            if ($validator->fails()) {
                return response()->json($validator->errors(), 200);
            }

            $condition == 'create' ? $data = new Workshop() : $data = Workshop::findOrFail($condition);
            $data->no_seri = 'LJR-'.now()->format('YmdHis').rand(10, 99);
            $data->vehicle_id = $request->vehicle_id;
            $data->jenis_perbaikan = $request->jenis_perbaikan;
            $data->driver_id = $request->driver_id;
            $data->kategori_kerusakan_id = $request->kategori_kerusakan_id;
            $data->answer_question_user_id = $request->answer_question_user_id;
            $request->detail ? $data->detail = $request->detail : true;
            $data->save();

            try {
                $create_history['status_perbaikan_id'] = StatusPerbaikan::where('name', 'pending')->first()->id;
                $create_history['note'] = $request->note ?? '';
                $create_history['workshop_id'] = $data->id;
                $history_workshop = HistoryWorkshop::create($create_history);
            } catch (\Throwable $th) {
                $th;
            }

            $vehilce = Vehicle::findOrFail($request->vehicle_id);
            $vehilce->maintenance = 1;
            $vehilce->save();

            return response()->json(['success' => true, 'message' => 'Berhasil input data', 'id' => $data->id]);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 200);
        }
    }

    public function approval_store(Request $request)
    {
        try {
            // return $request->all();
            $validator = Validator::make($request->all(), [
                'workshop_id' => 'required',
                'technical_id' => 'required',
                'type'          => 'required',
                // 'status_take'       => 'required',
                // 'status'        => 'required'
            ]);

            if ($validator->fails()) {
                return response()->json($validator->errors(), 200);
            }

            $data = Workshop::findOrFail($request->workshop_id);
            $data->approval_by = $request->technical_id;
            $data->status = 'approval';
            $data->status_perbaikan_id = $request->status_perbaikan_id;
            $request->note ? $data->note = $request->note : true;
            $data->save();
            
            $create_history['status_perbaikan_id'] = $request->status_perbaikan_id;
            $create_history['note'] = $request->note ?? '';
            $create_history['workshop_id'] = $data->id;
            $history_workshop = HistoryWorkshop::create($create_history);
            
            foreach ($request->jenis_service_id as $jenis_service_id) {
                $workshop_service['workshop_id'] = $data->id;
                $workshop_service['jenis_service_id'] = $jenis_service_id;
                $workshop_service_detail = WorkshopServiceDetail::create($workshop_service);
            }

            $transaction = new Transaction();
            $transaction->transaction_type = 'workshop';
            $transaction->workshop_type = $request->type;
            $request->estimation_date ? $transaction->estimation_date = $request->estimation_date : true;
            $request->other_change ? $transaction->other_change = Helper::fresh_aprice($request->other_change) : true;
            $transaction->status_take = $request->status_take;
            $transaction->status = $request->status;
            $transaction->workshop_id = $data->id;
            $transaction->save();

            $subtotal = 0;

            // dd($request->all());
            if (isset($request->item_name)) {
                $num = count($request->item_name);
                for ($x = 0; $x < $num; $x++) {
                    $detail = new PurchaseItem();
                    $detail->transaction_id = $transaction->id;
                    if($request->item_price) {
                        $subtotal += $request->item_price[$x];
                    }
                    $request->item_name[$x] ? $detail->name = $request->item_name[$x] : true;
                    $request->item_note[$x] ? $detail->note = $request->item_note[$x] : true;
                    $request->item_price[$x] ? $detail->price = Helper::fresh_aprice($request->item_price[$x]) : true;
                    $request->item_qty[$x] ? $detail->qty = $request->item_qty[$x] : true;
                    $detail->save();
                }
            }

            $update = Transaction::findOrFail($transaction->id);
            $update->subtotal = $subtotal;
            $update->final_total = $subtotal + $transaction->other_change;
            $update->save();

            return response()->json(['success' => true, 'message' => 'Berhasil input data']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 200);
        }
    }

    public function rejected(Request $request)
    {
        try {

            $validator = Validator::make($request->all(), [
                'id' => 'required',
                // 'technical_id' => 'required',
            ]);

            if ($validator->fails()) {
                return response()->json($validator->errors(), 200);
            }

            $data = Workshop::findOrFail($request->id);
            $data->approval_by = $request->technical_id;
            $data->status = "rejected";
            $request->note ? $data->note = $request->note : true;
            $data->save();

            try {
                $create_history['status_perbaikan_id'] = StatusPerbaikan::where('name', 'rejected')->first()->id;
                $create_history['note'] = $request->note ?? '';
                $create_history['workshop_id'] = $data->id;
                $history_workshop = HistoryWorkshop::create($create_history);
            } catch (\Throwable $th) {
                $th;
            }

            $vehicle = Vehicle::findOrFail($data->vehicle_id);
            $vehicle->service_status = 0;
            $vehicle->save();

            return response()->json(['success' => true, 'message' => 'Berhasil input data']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 200);
        }
    }

    public function show($id)
    {
        $k = Workshop::findOrFail($id);

        $type = $k->vehicle->category->name ?? '';
        $color = $k->vehicle->color->name ?? '';

        if ($k->status == 'pending') {
            $status = "Menunggu Pengecekan Technical";
        } else if ($k->status == 'approval') {
            $status = "Disetujui Dan Masuk Ke Perbaikan";
        } else {
            $status = "Pengajuan Ditolak";
        }

        $data['workshop']['driver_name']    = $k->driver->username ?? '';
        $data['workshop']['vehicle_name']   = $type . ' Warna ' . $color . '- Platno ' . $k->vehicle->license_no;
        $data['workshop']['status']    = $status;
        $data['workshop']['detail']    = $k->detail;
        $data['workshop']['request_form_id'] = RequestForm::where('workshop_id', $id)->pluck('id');
        $data['workshop']['approvalby']    = $k->technical->username ?? '';
        $data['workshop']['note']    = $k->note;
        $data['history_workshop'] = HistoryWorkshop::where('workshop_id', $id)->with('statusPerbaikan')->get();
        $transaction_id = Transaction::where('workshop_id', $id)->first()->id ?? null;
        $data['purchase_item'] = PurchaseItem::where('transaction_id', $transaction_id)->get();
        return response()->json($data, 200);
    }

    public function delete($id)
    {
        try {
            Workshop::findOrFail($id)->delete();
            return response()->json(['success' => true, 'message' => 'Berhasil delete data']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 200);
        }
    }

    public function perawatanKendaraan()
    {
        return Excel::download(new ExportPerawatanKendaraan(), 'perawatan kendaraan.xlsx');
        // try {
            return view('export.formPerawatanKendaraan');
        //     // return response()->json(['success' => true, 'message' => 'Berhasil delete data', 'template' => $template]);
        // } catch (\Exception $e) {
        //     return response()->json(['success' => false, 'message' => $e->getMessage()], 200);
        // }
    }

    public function indexGahoAll(Request $request)
    {
        try {
            $data = Workshop::with('statusPerbaikan', 'user')->get();
            if ($request->get('pending')) {
                $data = Workshop::with('statusPerbaikan', 'user')
                ->where(function($query) use($request){
                    if ($request->get('company_id')) {
                        $asset_id_company = Asset::where('company_id', $request->get('company_id'))->pluck('id');
                        $query->whereIn('asset_id', $asset_id_company);
                    }
                    if ($request->get('department_id')) {
                        $asset_id_company = Asset::where('department_id', $request->get('department_id'))->pluck('id');
                        $query->whereIn('asset_id', $asset_id_company);
                    }
                })
                ->where('status_perbaikan_id', '!=', 7)
                ->get();
            }
            foreach ($data as $k) {
                $license_no = $k->asset->assetDetail->where('attribute_code', 'nomor_polisi')->first()->value ?? '';
    
                $k['id'] = $k->id;
                $k['no_seri'] = $k->no_seri;
                $k['license_no'] = $license_no;
                $k['driver_name']  = ($k->driver->first_name ?? null).' '. ($k->driver->last_name ?? null);
                $k['detail'] = $k->detail;
                $k['kategori_kerusakan_id'] = $k->kategoriKerusakan->name ?? '-';
                $k['status'] = $k->status;
                $k['status_kendaraan'] = $k->answerQuestionUser->statusVehicle->color ?? '-';
                $k['color'] = $k->answerQuestionUser->statusVehicle->name ?? '-';
                $k['status_perbaikan_id'] = $k->status_perbaikan_id ?? null;
                $k['type'] = $k->type ?? null;
            }

            return response()->json($data);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 200);
        }
    }

    public function dashboardGahoAll(Request $request)
    {
        try {
            if ($request->has('start_date')) {
                $start_date = Carbon::parse($request->get('start_date'))->format('Y-m-d').' 00:00:00';
            }else{
                $start_date = Carbon::today()->format('Y-m-d').' 00:00:00';
            }
            if ($request->has('end_date')) {
                $end_date = Carbon::parse($request->get('end_date'))->format('Y-m-d').' 23:59:00';
            }else{
                $end_date = Carbon::today()->format('Y-m-d').' 23:59:00';
            }

            $data['start_date'] = Carbon::parse($start_date)->format('Y-m-d');
            $data['end_date'] = Carbon::parse($end_date)->format('Y-m-d');

            $data = Workshop::whereBetween('updated_at', [$start_date, $end_date])->with(['statusPerbaikan', 'user'])->get();
            foreach ($data as $k) {
                $license_no = AssetDetail::where('asset_id', $k->asset->id)->where('attribute_code', 'nomor_polisi')->first()->value ?? '';
                
                $k['id'] = $k->id;
                $workshop_sla =  WorkshopSla::where('workshop_id', $k->id)->orderBy('id', 'desc')->first();
                if (isset($workshop_sla->workEstimate)) {
                    $k['deadline'] = ($workshop_sla->created_at)->addDays($workshop_sla->workEstimate->day);
                }
                $k['waktu_pengajuan'] = $k->created_at;
                $k['case_closed'] = ($k->status_perbaikan_id == "7") ? true : false;
                $k['license_no'] = $license_no;
                $k['case_closed'] = false;
                $k['no_seri'] = $k->no_seri;
                $k['driver_name']  = ($k->driver->first_name ?? null).' '. ($k->driver->last_name ?? null);
                $k['detail'] = $k->detail;
                $k['kategori_kerusakan_id'] = $k->kategoriKerusakan->name ?? '-';
                $k['status'] = $k->status;
                $k['status_kendaraan'] = $k->answerQuestionUser->statusVehicle->color ?? '-';
            }

            return response()->json($data);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 200);
        }
    }

    public function keputusanGaho(Request $request)
    {
        try {
            $data = $request->all();
            $data['date_log'] = now();
            $data['user_id'] = Auth::user()->id;
            $wd = WorkshopDecision::create($data);

            $workshop = Workshop::findOrFail($data['workshop_id']);
            
            if ($data['category_workshop'] == 0) {
                $asset = Asset::findOrFail($workshop->asset_id);
                $asset->maintenance = null;
                $asset->save();
            }else{
                $workshop->keputusan_ga_ho = $data['category_workshop'];
                $workshop->gaho_id = Auth::user()->id;
                $workshop->status_perbaikan_id = 2;
                $workshop->save();
            }

            return response()->json(['success' => true, 'message' => 'Berhasil update data']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 200);
        }
    }

    public function indexGaho()
    {
        try {
            $data = Workshop::with('statusPerbaikan')->where('status_perbaikan_id', 1)->whereNull('keputusan_ga_ho')->get();
            foreach ($data as $k) {
                $license_no = $k->asset->assetDetail->where('attribute_code', 'nomor_polisi')->first()->value ?? '';
    
                $k['id'] = $k->id;
                $k['no_seri'] = $k->no_seri;
                $k['license_no'] = $license_no;
                $k['driver_name']  = ($k->driver->first_name ?? null).' '. ($k->driver->last_name ?? null);
                $k['detail'] = $k->detail;
                $k['kategori_kerusakan_id'] = $k->kategoriKerusakan->name ?? '-';
                $k['status'] = $k->status;
                $k['status_kendaraan'] = $k->answerQuestionUser->statusVehicle->color ?? '-';
            }

            return response()->json($data);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 200);
        }
    }

    public function detailGaho($id)
    {
        // try {
            $data = Workshop::with('statusPerbaikan', 'workshopDecision', 'statusPerbaikan')->findOrFail($id);
            $license_no = AssetDetail::where('asset_id', $data->asset_id)->where('attribute_code', 'nomor_polisi')->first()->value ?? '';
            $odometer_service = Workshop::where('asset_id', $data->asset_id)->orderBy('id', 'desc')->first()->km_service ?? 0;
            $asset = Asset::findOrFail($data->asset_id);
            $odometer_latest = $asset->km_actual ?? null;
            $maintenance_odometer = $asset->maintenance_odometer ?? null;

            $data['id'] = $data->id;
            $data['request_form_id'] = RequestForm::where('workshop_id', $id)->orderBy('id', 'desc')->first()->id ?? null;
            $data['no_seri'] = $data->no_seri;
            $data['license_no'] = $license_no;
            $data['driver_name']  = ($data->driver->first_name ?? null).' '. ($data->driver->last_name ?? null);
            if ($data->gaho_id) {
                $data['pengarah'] = User::findOrFail($data->gaho_id)->full_name ?? null;
            }else{
                $data['pengarah'] = null;
            }
            $data['kategori_kerusakan_id'] = $data->kategoriKerusakan->name ?? '-';
            $data['status'] = $data->status;
            $data['alasan_pengajuan'] = RiwayatAnswerQuestionUser::where('answer_question_user_id', $data->answer_question_user_id)->first()->note ?? null;
            $data['status_kendaraan'] = $data->answerQuestionUser->statusVehicle->color ?? '-';
            $data['keputusan_pengarah'] = Helper::keputusan_ga_ho($data->keputusan_ga_ho) ?? null;
            $data['odometer_service'] = $odometer_service;
            $data['odometer_latest'] = $odometer_latest;
            $data['workshop_sla'] =  WorkshopSla::where('workshop_id', $id)->orderBy('id', 'desc')->first();
            if (isset($data['workshop_sla']->workEstimate)) {
                $data['workshop_sla']['day'] = $data['workshop_sla']->workEstimate->day;
                $data['workshop_sla']['deadline'] = ($data['workshop_sla']->created_at)->addDays($data['workshop_sla']->workEstimate->day);
            }
            
            foreach ($data->workshopDetail as $wd) {
                $wd['nama_pemeriksa'] = $wd->user->full_name ?? null;
                $wd['waktu_submit'] = $wd->created_at ?? null;
            }

            // KATEGORI CHECKLIST MERAH
            $i = 0;
            $jenis_kerusakan = '';
            $question_danger = array();
            $cq = CategoryQuestion::where('slug', 'checklist-vehicle')->orderBy('id', 'asc')->select(['id', 'name'])->get();
            foreach ($cq as $key => $value) {
                $total_point = 0;
                $danger = false;
                $cq[$key]['question'] = $value->question()->with('answer')->select(['id', 'question'])->get();
                foreach ($cq[$key]['question'] as $bkey => $cvalue) {
                    $aqd = AnswerQuestionDetail::where('answer_question_user_id', $data->answer_question_user_id)->where('question_id', $cvalue->id)->first();
                    if ($aqd) {
                        $cq[$key]['question'][$bkey]['answer_id'] = $aqd->answer_question_id;
                        $total_point += $aqd->point ?? 0;
                        
                        if ($aqd->answerQuestion->danger == 1) {
                            $danger = true;
                            $question_danger[$i] = $aqd;
                            $workshopDetailByItem = WorkshopDetail::where('workshop_id', $data->id)->where('item_id', $aqd->question_id)->orderBy('id', 'desc')->first();
                            $question_danger[$i]['workshop_detail_id'] = $workshopDetailByItem->id ?? null;
                            $question_danger[$i]['jenis_perbaikan'] = $workshopDetailByItem->jenis_perbaikan ?? null;
                            $question_danger[$i]['sparepart'] = $workshopDetailByItem->sparepart ?? null;
                            $question_danger[$i]['nama_pemeriksa'] = $workshopDetailByItem->user->full_name ?? null;
                            $question_danger[$i]['waktu_submit_periksa'] = $workshopDetailByItem->created_at ?? null;

                            if ($workshopDetailByItem) {
                                $sparepart_use = SparepartUse::where('status_perbaikan_id', 2)->where('workshop_detail_id', $workshopDetailByItem->id)->orderBy('id', 'desc') ?? null;
                                $question_danger[$i]['sparepart'] = $sparepart_use->select('sparepart_id', 'qty')->get() ?? null;
                                $question_danger[$i]['sparepart_name'] = $sparepart_use->pluck('sparepart_id') ?? [];
                                $question_danger[$i]['qty'] = $sparepart_use->pluck('qty') ?? [];

                                $sparepart_use_tambahan = SparepartUse::where('status_perbaikan_id', 4)->where('workshop_detail_id', $workshopDetailByItem->id)->orderBy('id', 'desc') ?? null;
                                $question_danger[$i]['sparepart_tambahan'] = $sparepart_use_tambahan->select('sparepart_id', 'qty')->get() ?? null;
                            }

                            $mekanik = [];
                            $workshopInspection = WorkshopInspection::where('workshop_id', $data->id)->where('item_id', $aqd->question_id)->orderBy('id', 'desc')->first();
                            if (isset($workshopInspection->mekanik_id)) {
                                foreach (json_decode($workshopInspection->mekanik_id) as $mkey => $mvalue) {
                                    if (User::find($mvalue)) {
                                        $mekanik[$mkey]['id'] = User::find($mvalue)->id ?? null;
                                        $mekanik[$mkey]['full_name'] = User::find($mvalue)->full_name ?? null;
                                    }else{
                                        $mekanik[$mkey]['id'] = null;
                                        $mekanik[$mkey]['full_name'] = null;
                                    }
                                }
                            }
                            $question_danger[$i]['catatan_pengerjaan'] = $workshopInspection->note ?? null;
                            $question_danger[$i]['submit_pengerjaan'] = $workshopInspection->created_at ?? null;
                            $question_danger[$i]['mekanik'] = $mekanik;
                            
                            $question_danger[$i]['answer_question_detail'] = $aqd->questions->answer;
                            $i++;
                        }
                    }else {
                        $cq[$key]['question'][$bkey]['answer_id'] = null;
                        $cq[$key]['question'][$bkey]['image'] = null;
                    }
                }

                $cq[$key]['total_point'] = $total_point;
                if ($danger == true) {
                    $cq[$key]['color'] = 'red';
                    $jenis_kerusakan .= $cq[$key]['name'].", " ?? null;
                }else{
                    $cq[$key]['color'] = 'green';
                }
            }
            
            // MAINTENANCE/OPTIONAL SERVICE
            // $optional['question_id'] = null;
            // $optional['question'] = "SERVICE (optional)";
            // $optional['answer_question_id'] = null;
            // $optional['answer_question_id']['answer_question_detail']['id'] = null;
            // if ($maintenance_odometer) {
            //     $optional['answer_question_id']['answer_question_detail']['answer'] = "Odometer kendaraan sudah melewati batas untuk diservice";
            // }else{
            //     $optional['answer_question_id']['answer_question_detail']['answer'] = "Kendaraan harus diservice";
            // }

            // array_push($question_danger, $optional);
            
            $workshopOptionalService = WorkshopDetail::where('workshop_id', $data->id)->whereNull('item_id')->orderBy('id', 'desc')->first();
            $service_optional['sparepart'] = $workshopOptionalService['sparepart'] ?? null;
            $service_optional['workshop_detail_id'] = $workshopOptionalService->id ?? null;
            $service_optional['jenis_perbaikan'] = $workshopOptionalService['jenis_perbaikan'] ?? null;
            $service_optional['item_name'] = $workshopOptionalService['item_name'] ?? null;
            $service_optional['nama_pemeriksa'] = $workshopOptionalService->user->full_name ?? null;
            $service_optional['waktu_submit_periksa'] = $workshopOptionalService->created_at ?? null;
            
            if ($workshopOptionalService) {
                $sparepart_use_optional = SparepartUse::where('status_perbaikan_id', 2)->where('workshop_detail_id', $workshopOptionalService->id)->orderBy('id', 'desc');
                $service_optional['sparepart'] = $sparepart_use_optional->select('sparepart_id', 'qty')->get() ?? null;
                $service_optional['sparepart_name'] = $sparepart_use_optional->pluck('sparepart_id') ?? [];
                $service_optional['qty'] = $sparepart_use_optional->pluck('qty') ?? [];

                $sparepart_use_tambahan_optional = SparepartUse::where('status_perbaikan_id', 4)->where('workshop_detail_id', $workshopOptionalService->id)->orderBy('id', 'desc') ?? null;
                $service_optional['sparepart_tambahan'] = $sparepart_use_tambahan_optional->select('sparepart_id', 'qty')->get() ?? null;
            }

            $mekanikServiceOptional = [];
            $workshopInspectionSo = WorkshopInspection::where('workshop_id', $data->id)->whereNull('item_id')->orderBy('id', 'desc')->first();
            if (isset($workshopInspectionSo->mekanik_id)) {
                foreach (json_decode($workshopInspectionSo->mekanik_id) as $mkey => $mvalue) {
                    if (User::find($mvalue)) {
                        $mekanikServiceOptional[$mkey]['id'] = User::find($mvalue)->id ?? null;
                        $mekanikServiceOptional[$mkey]['full_name'] = User::find($mvalue)->full_name ?? null;
                    }else{
                        $mekanikServiceOptional[$mkey]['id'] = null;
                        $mekanikServiceOptional[$mkey]['full_name'] = null;
                    }
                }
            }
            $service_optional['catatan_pengerjaan'] = $workshopInspectionSo->note ?? null;
            $service_optional['submit_pengerjaan'] = $workshopInspectionSo->created_at ?? null;
            $service_optional['mekanik'] = $mekanikServiceOptional;

            $data['service_optional'] = $service_optional;
            $data['jenis_kerusakan'] = $jenis_kerusakan;
            $data['question_danger'] = $question_danger;
            $data['maintenance_odometer'] = (Asset::findOrFail($data->asset_id)->maintenance_odometer == 1) ? true : false;
            $data['workshop_final_decision_gaho'] = WorkshopFinalDecision::whereNull('koordinator_id')->where('workshop_id', $data->id)->orderBy('id', 'desc')->first();
            $data['workshop_final_decision_koordinator'] = WorkshopFinalDecision::whereNull('gaho_id')->where('workshop_id', $data->id)->orderBy('id', 'desc')->first();
            $data['workshop_detail'] = $data->workshopDetail;
            foreach ($data->workshopDetail as $key => $value) {
                $sparepart = SparepartUse::where('workshop_detail_id', $value->id)->orderBy('id', 'desc');
                $data['workshop_detail'][$key]['sparepart'] = $sparepart->select('sparepart_id', 'qty')->get() ?? null;
                $data['workshop_detail'][$key]['sparepart_name'] = $sparepart->pluck('sparepart_id');
                $data['workshop_detail'][$key]['qty'] = $sparepart->pluck('qty');

                $sparepart_tambahan = SparepartUse::where('workshop_detail_id', $value->id)->orderBy('id', 'desc') ?? null;
                $data['workshop_detail'][$key]['sparepart_tambahan'] = $sparepart_tambahan->select('sparepart_id', 'qty')->get() ?? null;
                // foreach ($sparepart as $ckey => $cvalue) {
                //     array_push($data['workshop_detail'][$key]['sparepart_name'][$ckey], $cvalue['sparepart_id'] ?? '-');
                //     array_push($data['workshop_detail'][$key]['qty'][$ckey], $cvalue['qty'] ?? '-');
                // }
            }

            return response()->json($data);
        // } catch (\Exception $e) {
        //     return response()->json(['success' => false, 'message' => $e->getMessage()], 200);
        // }
    }

    public function penetapanSla(Request $request, $id)
    {
        try {
            $data = $request->all();
            $dataWorkshop['penetapan_sla'] = 1;
            $dataWorkshop['status_perbaikan_id'] = 4;
            $workshop = Workshop::findOrFail($id)->update($dataWorkshop);
            
            
            $tingkat_kerusakan = WorkEstimate::findOrFail($data['tingkat_kerusakan']);
            $data['tingkat_kerusakan'] = $tingkat_kerusakan->name;
            $data['estimasi_pengerjaan'] = now()->addDay($tingkat_kerusakan->day);
            $data['work_estimate_id'] = $tingkat_kerusakan->id;
            // $data['mekanik_id'] = $data['mekanik_id'];
            $data['workshop_id'] = $id;
            $data['status'] = 0;
            $data['approve_id'] = Auth::user()->id;
            $data['status_perbaikan_id'] = 4;
            $sla = WorkshopSla::create($data);
            // foreach ($data['mekanik_id'] as $key => $mekanik_id) {
            // }
            
            return response()->json(['success' => true, 'message' => 'SLA Sudah Ditetapkan Tunggu Tindakan Dari Mekanik']);
        } catch (\Throwable $th) {
            
            return response()->json(['success' => false, 'message' => $th->getMessage()], 200);
        }
    }

    public function statusSla(Request $request, $id)
    {
        try {
            $data = $request->all();
            $workshop = WorkshopSla::where('workshop_id', $id)->orderBy('id', 'desc')->first();
            $workshop->status = $data['status'];
            $workshop->note = $data['note'];
            $workshop->save();

            $workshopData = Workshop::findOrFail($id);
            $workshopData->status_perbaikan_id = 4;
            $workshopData->save();
            
            return response()->json(['success' => true, 'message' => 'Berhasil update data']);
        } catch (\Throwable $th) {
            
            return response()->json(['success' => false, 'message' => $th->getMessage()], 200);
        }
    }

    public function indexSla(Request $request)
    {
        try {
            if ($request->start_date) {
                $start_date = Carbon::parse($request->start_date)->format('Y-m-d').' 00:00:00';
            }else{
                $start_date = Carbon::today()->format('Y-m-d').' 00:00:00';
            }
            if ($request->to_date) {
                $to_date = Carbon::parse($request->to_date)->format('Y-m-d').' 23:59:00';
            }else{
                $to_date = Carbon::today()->format('Y-m-d').' 23:59:00';
            }

            $data = Workshop::with('statusPerbaikan', 'user')
            // ->where(function($query) use($request){
            //     if ($request->get('company')) {
            //         $asset_id_company = Asset::where('company_id', $request->get('company'))->pluck('id');
            //         $query->whereIn('asset_id', $asset_id_company);
            //     }
            // })
            // ->whereBetween('created_at', [$start_date,$to_date])
            // ->whereIn('status_perbaikan_id', [1, 3])
            ->orderBy('id', 'desc')
            ->get();

            foreach ($data as $k) {
                $license_no = $k->asset->assetDetail->where('attribute_code', 'nomor_polisi')->first()->value ?? '';
    
                $k['id'] = $k->id;
                $k['no_seri'] = $k->no_seri;
                $k['driver_name']  = ($k->driver->first_name ?? null).' '. ($k->driver->last_name ?? null);
                $k['license_no'] = $license_no;
                $k['waktu_pengajuan'] = $k->created_at;
                $k['pengarah'] = User::find($k->user_id)->full_name ?? null;
                $k['alasan_pengajuan'] = $k->workshopDecision->note ?? null;
                $k['keputusan_pengarah'] = Helper::keputusan_ga_ho($k->keputusan_ga_ho);
                $status_kendaraan = null;
                if ($k->status_perbaikan_id == 1 || $k->status_perbaikan_id == 2) {
                    $status_kendaraan = '#FF0000';    
                }else if($k->status_perbaikan_id == 3 || $k->status_perbaikan_id == 4){
                    $status_kendaraan = '#FFFF00';
                }else if($k->status_perbaikan_id == 5 || $k->status_perbaikan_id == 6){
                    $status_kendaraan = '#008000';
                }else if($k->status_perbaikan_id == 7){
                    $status_kendaraan = '#0000ff';
                }else{
                    $status_kendaraan = '#000000';
                }
                $k['status_kendaraan'] = $status_kendaraan;

                $asset = Asset::findOrFail($k->asset_id);
                $k['type_asset'] = TypeAsset::find($asset->type_asset_id)->name ?? '-';
                $k['workshop_inspection_note'] = '';
                $k['workshop_inspection_finish_repair'] = '';
                $mekanik = [];
                
                // if (!empty($k->workshopInspection)) {
                //     foreach ($k->workshopInspection as $key => $value) {
                //         $k['workshop_inspection_note'] .= $value->note.", ";
                //         $k['workshop_inspection_finish_repair'] = $value->created_at;
                        
                //         if (!empty($value->mekanik_id)) {
                //             foreach (json_decode($value->mekanik_id) as $cvalue) {
                //                 array_push($mekanik, $cvalue);
                //             }
                //         }
                //     }
                // }
                
                // if (!empty($mekanik)) {
                //     $mekanik = User::whereIn('id', array_unique($mekanik))->pluck('full_name');
                //     $k['workshop_inspection_mekanik'] = '';
                //     foreach ($mekanik as $key => $value) {
                //         $k['workshop_inspection_mekanik'] .= $value.", ";
                //     }
                // }
            }

            return response()->json($data);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 200);
        }
    }

    public function workEstimate()
    {
        try {
            $data = WorkEstimate::get();

            return response()->json($data);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 200);
        }
    }

    public function pemeriksaanPengerjaan(Request $request, $id)
    {
        try {
            $data = $request->all();
            $workshop['date_log'] = now();
            $workshop['workshop_id'] = $id;
            $workshop['note'] = $data['note'];
            $workshop['user_id'] = Auth::user()->id;
            // $workshop['mekanik_id'] = json_encode($data['mekanik_id']);
            if (isset($data['item_id'])) {
                $workshop['item_id'] = $data['item_id'];
                $workshop['item_name'] = Question::findOrFail($data['item_id'])->question ?? null;
            }else{
                $workshop['item_id'] = null;
                $workshop['item_name'] = 'SERVICE (optional)';
            }
            $workshopCreate = WorkshopInspection::create($workshop);

            for ($i=0; $i < count($data['sparepart']); $i++) { 
                $dataSparepart['workshop_detail_id'] = $data['workshop_detail_id'][$i];
                $dataSparepart['sparepart_id'] = $data['sparepart'][$i];
                $dataSparepart['qty'] = $data['qty'][$i];
                $dataSparepart['status_perbaikan_id'] = 4;
                if ($data['sparepart'][$i] && $data['qty'][$i]) {
                    SparepartUse::create($dataSparepart);
                }
            }
            
            return response()->json(['success' => true, 'message' => 'Input data berhasil']);
        } catch (\Throwable $th) {
            
            return response()->json(['success' => false, 'message' => $th->getMessage()], 200);
        }
    }

    public function submitPemeriksaanPengerjaan(Request $request, $id)
    {
        try {
            $data = $request->all();

            $workshopData = Workshop::findOrFail($id);
            $workshopData->pemeriksaan_selesai = 1;
            $workshopData->status_perbaikan_id = 6;
            $workshopData->save();

            $workshopInspection = WorkshopInspection::where('workshop_id', $workshopData->id)->get();
            foreach ($workshopInspection as $key => $value) {
                $value->mekanik_id = json_encode($data['mekanik_id']);
                $value->save();
            }
            
            return response()->json(['success' => true, 'message' => 'Pengerjaan Selesai Pada '.$workshopData->updated_at->format('d M Y')]);
        } catch (\Throwable $th) {
            
            return response()->json(['success' => false, 'message' => $th->getMessage()], 200);
        }
    }

    public function storePemeriksaan(Request $request, $id)
    {
        try {
            $data = $request->all();
            foreach ($data as $key => $value) {
                $workshop['date_log'] = now();
                $workshop['workshop_id'] = $id;
                $workshop['note'] = $value['note'];
                $workshop['user_id'] = Auth::user()->id;
                $workshop['mekanik_id'] = json_encode($value['mekanik_id']);
                if (isset($value['item_id'])) {
                    $workshop['item_id'] = $value['item_id'];
                    $workshop['item_name'] = Question::findOrFail($value['item_id'])->question ?? null;
                }else{
                    $workshop['item_id'] = null;
                    $workshop['item_name'] = 'SERVICE (optional)';
                }
                $workshopCreate = WorkshopInspection::create($workshop);

                for ($i=0; $i < count($value['sparepart']); $i++) { 
                    $dataSparepart['workshop_detail_id'] = $value['workshop_detail_id'][$i];
                    $dataSparepart['sparepart_id'] = $value['sparepart'][$i];
                    $dataSparepart['qty'] = $value['qty'][$i];
                    $dataSparepart['status_perbaikan_id'] = 4;
                    if ($value['sparepart'][$i] && $value['qty'][$i]) {
                        SparepartUse::create($dataSparepart);
                    }
                }
            }
            

            $workshopData = Workshop::findOrFail($id);
            $workshopData->pemeriksaan_selesai = 1;
            $workshopData->status_perbaikan_id = 6;
            $workshopData->save();

            return response()->json(['success' => true, 'message' => 'Input data berhasil']);
        } catch (\Throwable $th) {
            
            return response()->json(['success' => false, 'message' => $th->getMessage()], 200);
        }
    }

    public function indexPemeriksaanPengerjaan()
    {
        try {
            $data = Workshop::with('statusPerbaikan')
            ->where('status_perbaikan_id', 4)
            ->where('keputusan_ga_ho', 1)
            ->where('tindakan_mekanik', 1)
            ->where('penetapan_sla', 1)
            ->whereNull('pemeriksaan_selesai')
            ->get();

            foreach ($data as $k) {
                $license_no = $k->asset->assetDetail->where('attribute_code', 'nomor_polisi')->first()->value ?? '';
    
                $k['id'] = $k->id;
                $k['no_seri'] = $k->no_seri;
                $k['license_no'] = $license_no;
                $k['waktu_pengajuan'] = $k->created_at;
                $k['pengarah'] = User::findOrFail($k->user_id)->full_name ?? null;
                $k['alasan_pengajuan'] = $k->workshopDecision->note ?? null;
                $k['keputusan_pengarah'] = Helper::keputusan_ga_ho($k->keputusan_ga_ho);
                $k['status_kendaraan'] = $k->answerQuestionUser->statusVehicle->color ?? '-';
            }

            return response()->json($data);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 200);
        }
    }

    public function keputusanFinalGaho(Request $request, $id)
    {
        try {
            $data = $request->all();
            $workshopData = Workshop::findOrFail($id);
            if ($data['status'] == 1) {
                $workshopData->status_perbaikan_id = 6;
                $workshopData->pemeriksaan_final = 1;
            }elseif ($data['status'] == 2) {
                $workshopData->status_perbaikan_id = 1;
                $workshopData->keputusan_ga_ho = null;
                $workshopData->tindakan_mekanik = null;
                $workshopData->penetapan_sla = null;
                $workshopData->pemeriksaan_selesai = null;
            }

            $dataFinalDecision['workshop_id'] = $id;
            $dataFinalDecision['gaho_id'] = Auth::user()->id;
            $dataFinalDecision['note_gaho'] = $data['note'];
            $dataFinalDecision['status_gaho'] = $data['status'];
            $report = WorkshopFinalDecision::create($dataFinalDecision);
            
            $workshopData->save();
            
            return response()->json(['success' => true, 'message' => 'Keputusan Akhir GA HO Telah Dilakukan Kasus Berhasil Ditutup']);
        } catch (\Throwable $th) {
            
            return response()->json(['success' => false, 'message' => $th->getMessage()], 200);
        }
    }

    public function indexKeputusanFinalGaho()
    {
        try {
            $data = Workshop::with('statusPerbaikan', 'user')
            ->where('status_perbaikan_id', 5)
            ->where('keputusan_ga_ho', 1)
            ->where('penetapan_sla', 1)
            ->where('tindakan_mekanik', 1)
            ->where('pemeriksaan_selesai', 1)
            ->where('pemeriksaan_final', 1)
            ->whereNull('keputusan_koordinator')
            ->get();
            foreach ($data as $k) {
                $license_no = $k->asset->assetDetail->where('attribute_code', 'nomor_polisi')->first()->value ?? '';
    
                $k['id'] = $k->id;
                $k['no_seri'] = $k->no_seri;
                $k['driver_name']  = ($k->driver->first_name ?? null).' '. ($k->driver->last_name ?? null);
                $k['license_no'] = $license_no;
                $k['waktu_pengajuan'] = $k->created_at;
                $k['pengarah'] = User::findOrFail($k->user_id)->full_name ?? null;
                $k['alasan_pengajuan'] = $k->workshopDecision->note ?? null;
                $k['keputusan_pengarah'] = Helper::keputusan_ga_ho($k->keputusan_ga_ho);
                $k['status_kendaraan'] = $k->answerQuestionUser->statusVehicle->color ?? '-';
            }

            return response()->json($data);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 200);
        }
    }


    public function detailGaho2($id)
    {
        try {
            $data = Workshop::with('statusPerbaikan', 'workshopDetail', 'workshopSla', 'workshopDecision', 'statusPerbaikan')->findOrFail($id);
            $license_no = collect($data->assetDetail)->where('attribute_code', 'nomor_polisi')->first()->value ?? '';
            $data['account_type'] = Auth::user()->role;

            $data['id'] = $data->id;
            $data['arrangement']['plat_kendaraan'] = $license_no;
            $data['arrangement']['waktu_pengajuan'] = $data->created_at;
            $data['arrangement']['alasan_pengajuan'] = $data->jenis_perbaikan;
            $data['arrangement']['jenis_kerusakan'] = $data->jenis_perbaikan;
            $data['arrangement']['pengarah'] = $data->jenis_perbaikan;
            $data['arrangement']['status'] = $data->jenis_perbaikan;
            $data['arrangement']['keputusan_pengarah'] = $data->jenis_perbaikan;


            $data['no_seri'] = $data->no_seri;
            $data['driver_name']  = ($data->driver->first_name ?? null).' '. ($data->driver->last_name ?? null);
            $data['detail'] = $data->detail;
            $data['kategori_kerusakan_id'] = $data->kategoriKerusakan->name ?? '-';
            $data['status'] = $data->status;
            $data['status_kendaraan'] = $data->answerQuestionUser->statusVehicle->color ?? '-';

            return response()->json($data);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 200);
        }
    }

    public function keputusanFinalKoordniator(Request $request, $id)
    {
        try {
            $data = $request->all();
            $workshopData = Workshop::findOrFail($id);
            if ($data['status'] == 1) {
                $workshopData->status_perbaikan_id = 7;
                $workshopData->pemeriksaan_final = 1;

                // UPDATE STATUS KENDARAAN JADI BISA DIPAKAI
                $asset = Asset::findOrFail($workshopData->asset_id);
                $asset->maintenance = null;
                $asset->maintenance_odometer = 0;
                $asset->trip = 0;
                $asset->save();
            }elseif ($data['status'] == 2) {
                $workshopData->status_perbaikan_id = 1;
                $workshopData->keputusan_ga_ho = null;
                $workshopData->tindakan_mekanik = null;
                $workshopData->penetapan_sla = null;
                $workshopData->pemeriksaan_selesai = null;
                $workshopData->service = $workshopData->service + 1;
            }

            $dataFinalDecision['workshop_id'] = $id;
            $dataFinalDecision['koordinator_id'] = Auth::user()->id;
            $dataFinalDecision['note_koordinator'] = $data['note'];
            $dataFinalDecision['status_koordinator'] = $data['status'];
            $report = WorkshopFinalDecision::create($dataFinalDecision);
            
            $workshopData->save();
            
            return response()->json(['success' => true, 'message' => 'Keputusan Akhir Koordniator Telah Dilakukan Kasus Berhasil Ditutup']);
        } catch (\Throwable $th) {
            
            return response()->json(['success' => false, 'message' => $th->getMessage()], 200);
        }
    }

    public function indexKeputusanFinalKoordniator()
    {
        try {
            $data = Workshop::with('statusPerbaikan', 'user')
            ->where('status_perbaikan_id', 6)
            ->where('keputusan_ga_ho', 1)
            ->get();

            foreach ($data as $k) {
                $license_no = $k->asset->assetDetail->where('attribute_code', 'nomor_polisi')->first()->value ?? '';
    
                $k['id'] = $k->id;
                $k['no_seri'] = $k->no_seri;
                $k['driver_name']  = ($k->driver->first_name ?? null).' '. ($k->driver->last_name ?? null);
                $k['license_no'] = $license_no;
                $k['waktu_pengajuan'] = $k->created_at;
                $k['pengarah'] = User::findOrFail($k->user_id)->full_name ?? null;
                $k['alasan_pengajuan'] = $k->workshopDecision->note ?? null;
                $k['keputusan_pengarah'] = Helper::keputusan_ga_ho($k->keputusan_ga_ho);
                $k['status_kendaraan'] = $k->answerQuestionUser->statusVehicle->color ?? '-';

                $asset = Asset::findOrFail($k->asset_id);
                $k['type_asset'] = TypeAsset::findOrFail($asset->type_asset_id)->name ?? '-';

                $k['jenis_kerusakan'] = '';
                $k['jenis_perbaikan'] = '';
                foreach ($k->workshopDetail as $value) {
                    $k['jenis_kerusakan'] .= $value->jenis_kerusakan .', ';
                    $k['jenis_perbaikan'] .= $value->jenis_perbaikan .', ';
                }
                $k['btn_jenis_kerusakan'] = false;
                $k['btn_jenis_perbaikan'] = false;

                $k['finish_workshop_inspection'] = WorkshopInspection::where('workshop_id', $k->id)->orderBy('id', 'desc')->first()->created_at ?? '-';
                $sla = WorkshopSla::where('workshop_id', $k->id)->orderBy('id', 'desc')->first();
                if (!empty($sla)) {
                    $k['sla_created_at'] = $sla->created_at->format('Y-m-d');
                    $k['sla_deadline'] = $sla->estimasi_pengerjaan;
                    try {
                        $to = Carbon::parse($sla->estimasi_pengerjaan);
                        $from = Carbon::parse($k['finish_workshop_inspection']);
                        $k['sla_status_time'] = ($to->diffInDays($from) > 0) ? 'Late Time' : 'On Time';
                    } catch (\Throwable $e) {
                        $k['sla_status_time'] = '-';
                    }
                }
            }

            return response()->json($data);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 200);
        }
    }

    public function tingkatKerusakan()
    {
        try {
            $data = WorkEstimate::get();

            return response()->json($data);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 200);
        }
    }

    public function detailPemeriksaanPengerjaan($id)
    {
        try {
            $data = Workshop::with('statusPerbaikan', 'workshopDecision', 'statusPerbaikan')->findOrFail($id);
            $license_no = AssetDetail::where('asset_id', $data->asset_id)->where('attribute_code', 'nomor_polisi')->first()->value ?? '';
            $odometer_service = Workshop::where('asset_id', $data->asset_id)->orderBy('id', 'desc')->first()->km_service ?? 0;
            $asset = Asset::findOrFail($data->asset_id);
            $odometer_latest = $asset->km_actual ?? null;
            $maintenance_odometer = $asset->maintenance_odometer ?? null;

            $data['id'] = $data->id;
            $data['no_seri'] = $data->no_seri;
            $data['license_no'] = $license_no;
            $data['driver_name']  = ($data->driver->first_name ?? null).' '. ($data->driver->last_name ?? null);
            if ($data->gaho_id) {
                $data['pengarah'] = User::findOrFail($data->gaho_id)->full_name ?? null;
            }else{
                $data['pengarah'] = null;
            }
            $data['kategori_kerusakan_id'] = $data->kategoriKerusakan->name ?? '-';
            $data['status'] = $data->status;
            $data['alasan_pengajuan'] = RiwayatAnswerQuestionUser::where('answer_question_user_id', $data->answer_question_user_id)->first()->note ?? null;
            $data['status_kendaraan'] = $data->answerQuestionUser->statusVehicle->color ?? '-';
            $data['keputusan_pengarah'] = Helper::keputusan_ga_ho($data->keputusan_ga_ho) ?? null;
            $data['odometer_service'] = $odometer_service;
            $data['odometer_latest'] = $odometer_latest;
            $data['workshop_sla'] =  WorkshopSla::where('workshop_id', $id)->orderBy('id', 'desc')->first();
            if (isset($data['workshop_sla']->workEstimate)) {
                $data['workshop_sla']['day'] = $data['workshop_sla']->workEstimate->day;
                $data['workshop_sla']['deadline'] = ($data['workshop_sla']->created_at)->addDays($data['workshop_sla']->workEstimate->day);
            }
            
            foreach ($data->workshopDetail as $wd) {
                $wd['nama_pemeriksa'] = $wd->user->full_name ?? null;
                $wd['waktu_submit'] = $wd->created_at ?? null;
            }

            // KATEGORI CHECKLIST MERAH
            $i = 0;
            $jenis_kerusakan = '';
            $question_danger = array();
            $cq = CategoryQuestion::where('slug', 'checklist-vehicle')->orderBy('id', 'asc')->select(['id', 'name'])->get();
            foreach ($cq as $key => $value) {
                $total_point = 0;
                $danger = false;
                $cq[$key]['question'] = $value->question()->with('answer')->select(['id', 'question'])->get();
                foreach ($cq[$key]['question'] as $bkey => $cvalue) {
                    $aqd = AnswerQuestionDetail::where('answer_question_user_id', $data->answer_question_user_id)->where('question_id', $cvalue->id)->first();
                    if ($aqd) {
                        $cq[$key]['question'][$bkey]['answer_id'] = $aqd->answer_question_id;
                        $total_point += $aqd->point ?? 0;
                        
                        if ($aqd->answerQuestion->danger == 1) {
                            $danger = true;
                            $question_danger[$i] = $aqd;
                            $workshopDetailByItem = WorkshopDetail::where('workshop_id', $data->id)->where('item_id', $aqd->question_id)->orderBy('id', 'desc')->first();
                            $question_danger[$i]['workshop_detail_id'] = $workshopDetailByItem->id ?? null;
                            $question_danger[$i]['jenis_perbaikan'] = $workshopDetailByItem->jenis_perbaikan ?? null;
                            $question_danger[$i]['sparepart'] = $workshopDetailByItem->sparepart ?? null;
                            $question_danger[$i]['nama_pemeriksa'] = $workshopDetailByItem->user->full_name ?? null;
                            $question_danger[$i]['waktu_submit_periksa'] = $workshopDetailByItem->created_at ?? null;

                            if ($workshopDetailByItem) {
                                $sparepart_use = SparepartUse::where('status_perbaikan_id', 4)->where('workshop_detail_id', $workshopDetailByItem->id)->orderBy('id', 'desc') ?? null;
                                $question_danger[$i]['sparepart'] = $sparepart_use->select('sparepart_id', 'qty')->get() ?? null;
                                $question_danger[$i]['sparepart_name'] = $sparepart_use->pluck('sparepart_id') ?? [];
                                $question_danger[$i]['qty'] = $sparepart_use->pluck('qty') ?? [];
                            }

                            $mekanik = [];
                            $workshopInspection = WorkshopInspection::where('workshop_id', $data->id)->where('item_id', $aqd->question_id)->orderBy('id', 'desc')->first();
                            if (isset($workshopInspection->mekanik_id)) {
                                foreach (json_decode($workshopInspection->mekanik_id) as $mkey => $mvalue) {
                                    $mekanik[$mkey]['id'] = User::findOrFail($mvalue)->id ?? null;
                                    $mekanik[$mkey]['full_name'] = User::findOrFail($mvalue)->full_name ?? null;
                                }
                            }
                            $question_danger[$i]['catatan_pengerjaan'] = $workshopInspection->note ?? null;
                            $question_danger[$i]['submit_pengerjaan'] = $workshopInspection->created_at ?? null;
                            $question_danger[$i]['mekanik'] = $mekanik;
                            
                            $question_danger[$i]['answer_question_detail'] = $aqd->questions->answer;
                            $i++;
                        }
                    }else {
                        $cq[$key]['question'][$bkey]['answer_id'] = null;
                        $cq[$key]['question'][$bkey]['image'] = null;
                    }
                }

                $cq[$key]['total_point'] = $total_point;
                if ($danger == true) {
                    $cq[$key]['color'] = 'red';
                    $jenis_kerusakan .= $cq[$key]['name'].", " ?? null;
                }else{
                    $cq[$key]['color'] = 'green';
                }
            }
            
            $workshopOptionalService = WorkshopDetail::where('workshop_id', $data->id)->whereNull('item_id')->orderBy('id', 'desc')->first();
            $service_optional['sparepart'] = $workshopOptionalService['sparepart'] ?? null;
            $service_optional['workshop_detail_id'] = $workshopOptionalService->id ?? null;
            $service_optional['jenis_perbaikan'] = $workshopOptionalService['jenis_perbaikan'] ?? null;
            $service_optional['item_name'] = $workshopOptionalService['item_name'] ?? null;
            $service_optional['nama_pemeriksa'] = $workshopOptionalService->user->full_name ?? null;
            $service_optional['waktu_submit_periksa'] = $workshopOptionalService->created_at ?? null;
            
            if ($workshopOptionalService) {
                $sparepart_use_optional = SparepartUse::where('status_perbaikan_id', 4)->where('workshop_detail_id', $workshopOptionalService->id)->orderBy('id', 'desc');
                $service_optional['sparepart'] = $sparepart_use_optional->select('sparepart_id', 'qty')->get() ?? null;
                $service_optional['sparepart_name'] = $sparepart_use_optional->pluck('sparepart_id') ?? [];
                $service_optional['qty'] = $sparepart_use_optional->pluck('qty') ?? [];
            }

            $mekanikServiceOptional = [];
            $workshopInspectionSo = WorkshopInspection::where('workshop_id', $data->id)->whereNull('item_id')->orderBy('id', 'desc')->first();
            if (isset($workshopInspectionSo->mekanik_id)) {
                foreach (json_decode($workshopInspectionSo->mekanik_id) as $mkey => $mvalue) {
                    $mekanikServiceOptional[$mkey]['id'] = User::findOrFail($mvalue)->id ?? null;
                    $mekanikServiceOptional[$mkey]['full_name'] = User::findOrFail($mvalue)->full_name ?? null;
                }
            }
            $service_optional['catatan_pengerjaan'] = $workshopInspectionSo->note ?? null;
            $service_optional['submit_pengerjaan'] = $workshopInspectionSo->created_at ?? null;
            $service_optional['mekanik'] = $mekanikServiceOptional;

            $data['service_optional'] = $service_optional;
            $data['jenis_kerusakan'] = $jenis_kerusakan;
            $data['question_danger'] = $question_danger;
            $data['maintenance_odometer'] = (Asset::findOrFail($data->asset_id)->maintenance_odometer == 1) ? true : false;
            $data['workshop_final_decision_gaho'] = WorkshopFinalDecision::whereNull('koordinator_id')->where('workshop_id', $data->id)->orderBy('id', 'desc')->first();
            $data['workshop_final_decision_koordinator'] = WorkshopFinalDecision::whereNull('gaho_id')->where('workshop_id', $data->id)->orderBy('id', 'desc')->first();
            $data['workshop_detail'] = $data->workshopDetail;
            foreach ($data->workshopDetail as $key => $value) {
                $sparepart = SparepartUse::where('status_perbaikan_id', 4)->where('workshop_detail_id', $value->id)->orderBy('id', 'desc');
                $data['workshop_detail'][$key]['sparepart'] = $sparepart->select('sparepart_id', 'qty')->get() ?? null;
                $data['workshop_detail'][$key]['sparepart_name'] = $sparepart->pluck('sparepart_id');
                $data['workshop_detail'][$key]['qty'] = $sparepart->pluck('qty');                
            }

            return response()->json($data);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 200);
        }
    }

    public function detailPengajuan($id)
    {
        // try {
            $data = Workshop::with('statusPerbaikan', 'workshopDecision', 'statusPerbaikan')->findOrFail($id);
            $license_no = AssetDetail::where('asset_id', $data->asset_id)->where('attribute_code', 'nomor_polisi')->first()->value ?? '';
            $odometer_service = Workshop::where('asset_id', $data->asset_id)->orderBy('id', 'desc')->first()->km_service ?? 0;
            $asset = Asset::findOrFail($data->asset_id);
            $odometer_latest = $asset->km_actual ?? null;
            $maintenance_odometer = $asset->maintenance_odometer ?? null;

            $data['id'] = $data->id;
            $data['no_seri'] = $data->no_seri;
            $data['license_no'] = $license_no;
            $data['driver_name']  = ($data->driver->first_name ?? null).' '. ($data->driver->last_name ?? null);
            if ($data->gaho_id) {
                $data['pengarah'] = User::findOrFail($data->gaho_id)->full_name ?? null;
            }else{
                $data['pengarah'] = null;
            }
            $data['kategori_kerusakan_id'] = $data->kategoriKerusakan->name ?? '-';
            $data['status'] = $data->status;
            $data['alasan_pengajuan'] = RiwayatAnswerQuestionUser::where('answer_question_user_id', $data->answer_question_user_id)->first()->note ?? null;
            $data['status_kendaraan'] = $data->answerQuestionUser->statusVehicle->color ?? '-';
            $data['keputusan_pengarah'] = Helper::keputusan_ga_ho($data->keputusan_ga_ho) ?? null;
            $data['odometer_service'] = $odometer_service;
            $data['odometer_latest'] = $odometer_latest;
            $data['workshop_sla'] =  WorkshopSla::where('workshop_id', $id)->orderBy('id', 'desc')->first();
            if (isset($data['workshop_sla']->workEstimate)) {
                $data['workshop_sla']['day'] = $data['workshop_sla']->workEstimate->day;
                $data['workshop_sla']['deadline'] = ($data['workshop_sla']->created_at)->addDays($data['workshop_sla']->workEstimate->day);
            }
            
            foreach ($data->workshopDetail as $wd) {
                $wd['nama_pemeriksa'] = $wd->user->full_name ?? null;
                $wd['waktu_submit'] = $wd->created_at ?? null;
            }

            // KATEGORI CHECKLIST MERAH
            $i = 0;
            $jenis_kerusakan = '';
            $question_danger = array();
            $cq = CategoryQuestion::where('slug', 'checklist-vehicle')->orderBy('id', 'asc')->select(['id', 'name'])->get();
            foreach ($cq as $key => $value) {
                $total_point = 0;
                $danger = false;
                $cq[$key]['question'] = $value->question()->with('answer')->select(['id', 'question'])->get();
                foreach ($cq[$key]['question'] as $bkey => $cvalue) {
                    $aqd = AnswerQuestionDetail::where('answer_question_user_id', $data->answer_question_user_id)->where('question_id', $cvalue->id)->first();
                    if ($aqd) {
                        $cq[$key]['question'][$bkey]['answer_id'] = $aqd->answer_question_id;
                        $total_point += $aqd->point ?? 0;
                        
                        if ($aqd->answerQuestion->danger == 1) {
                            $danger = true;
                            $question_danger[$i] = $aqd;
                            $workshopDetailByItem = WorkshopDetail::where('workshop_id', $data->id)->where('item_id', $aqd->question_id)->orderBy('id', 'desc')->first();
                            $question_danger[$i]['workshop_detail_id'] = $workshopDetailByItem->id ?? null;
                            $question_danger[$i]['jenis_perbaikan'] = $workshopDetailByItem->jenis_perbaikan ?? null;
                            $question_danger[$i]['sparepart'] = $workshopDetailByItem->sparepart ?? null;
                            $question_danger[$i]['nama_pemeriksa'] = $workshopDetailByItem->user->full_name ?? null;
                            $question_danger[$i]['waktu_submit_periksa'] = $workshopDetailByItem->created_at ?? null;

                            if ($workshopDetailByItem) {
                                $sparepart_use = SparepartUse::where('status_perbaikan_id', 2)->where('workshop_detail_id', $workshopDetailByItem->id)->orderBy('id', 'desc') ?? null;
                                $question_danger[$i]['sparepart'] = $sparepart_use->select('sparepart_id', 'qty')->get() ?? null;
                                $question_danger[$i]['sparepart_name'] = $sparepart_use->pluck('sparepart_id') ?? [];
                                $question_danger[$i]['qty'] = $sparepart_use->pluck('qty') ?? [];
                            }

                            $mekanik = [];
                            $workshopInspection = WorkshopInspection::where('workshop_id', $data->id)->where('item_id', $aqd->question_id)->orderBy('id', 'desc')->first();
                            if (isset($workshopInspection->mekanik_id)) {
                                foreach (json_decode($workshopInspection->mekanik_id) as $mkey => $mvalue) {
                                    $mekanik[$mkey]['id'] = User::findOrFail($mvalue)->id ?? null;
                                    $mekanik[$mkey]['full_name'] = User::findOrFail($mvalue)->full_name ?? null;
                                }
                            }
                            $question_danger[$i]['catatan_pengerjaan'] = $workshopInspection->note ?? null;
                            $question_danger[$i]['submit_pengerjaan'] = $workshopInspection->created_at ?? null;
                            $question_danger[$i]['mekanik'] = $mekanik;
                            
                            $question_danger[$i]['answer_question_detail'] = $aqd->questions->answer;
                            $i++;
                        }
                    }else {
                        $cq[$key]['question'][$bkey]['answer_id'] = null;
                        $cq[$key]['question'][$bkey]['image'] = null;
                    }
                }

                $cq[$key]['total_point'] = $total_point;
                if ($danger == true) {
                    $cq[$key]['color'] = 'red';
                    $jenis_kerusakan .= $cq[$key]['name'].", " ?? null;
                }else{
                    $cq[$key]['color'] = 'green';
                }
            
            
            $workshopOptionalService = WorkshopDetail::where('workshop_id', $data->id)->whereNull('item_id')->orderBy('id', 'desc')->first();
            $service_optional['sparepart'] = $workshopOptionalService['sparepart'] ?? null;
            $service_optional['workshop_detail_id'] = $workshopOptionalService->id ?? null;
            $service_optional['jenis_perbaikan'] = $workshopOptionalService['jenis_perbaikan'] ?? null;
            $service_optional['item_name'] = $workshopOptionalService['item_name'] ?? null;
            $service_optional['nama_pemeriksa'] = $workshopOptionalService->user->full_name ?? null;
            $service_optional['waktu_submit_periksa'] = $workshopOptionalService->created_at ?? null;
            
            if ($workshopOptionalService) {
                $sparepart_use_optional = SparepartUse::where('status_perbaikan_id', 2)->where('workshop_detail_id', $workshopOptionalService->id)->orderBy('id', 'desc');
                $service_optional['sparepart'] = $sparepart_use_optional->select('sparepart_id', 'qty')->get() ?? null;
                $service_optional['sparepart_name'] = $sparepart_use_optional->pluck('sparepart_id') ?? [];
                $service_optional['qty'] = $sparepart_use_optional->pluck('qty') ?? [];
            }

            $mekanikServiceOptional = [];
            $workshopInspectionSo = WorkshopInspection::where('workshop_id', $data->id)->whereNull('item_id')->orderBy('id', 'desc')->first();
            if (isset($workshopInspectionSo->mekanik_id)) {
                foreach (json_decode($workshopInspectionSo->mekanik_id) as $mkey => $mvalue) {
                    $mekanikServiceOptional[$mkey]['id'] = User::findOrFail($mvalue)->id ?? null;
                    $mekanikServiceOptional[$mkey]['full_name'] = User::findOrFail($mvalue)->full_name ?? null;
                }
            }
            $service_optional['catatan_pengerjaan'] = $workshopInspectionSo->note ?? null;
            $service_optional['submit_pengerjaan'] = $workshopInspectionSo->created_at ?? null;
            $service_optional['mekanik'] = $mekanikServiceOptional;

            $data['service_optional'] = $service_optional;
            $data['jenis_kerusakan'] = $jenis_kerusakan;
            $data['question_danger'] = $question_danger;
            $data['maintenance_odometer'] = (Asset::findOrFail($data->asset_id)->maintenance_odometer == 1) ? true : false;
            $data['workshop_final_decision_gaho'] = WorkshopFinalDecision::whereNull('koordinator_id')->where('workshop_id', $data->id)->orderBy('id', 'desc')->first();
            $data['workshop_final_decision_koordinator'] = WorkshopFinalDecision::whereNull('gaho_id')->where('workshop_id', $data->id)->orderBy('id', 'desc')->first();
            $data['workshop_detail'] = $data->workshopDetail;
            foreach ($data->workshopDetail as $key => $value) {
                $sparepart = SparepartUse::where('status_perbaikan_id', 2)->where('workshop_detail_id', $value->id)->orderBy('id', 'desc');
                $data['workshop_detail'][$key]['sparepart'] = $sparepart->select('sparepart_id', 'qty')->get() ?? null;
                $data['workshop_detail'][$key]['sparepart_name'] = $sparepart->pluck('sparepart_id');
                $data['workshop_detail'][$key]['qty'] = $sparepart->pluck('qty');
            }

            return response()->json($data);
        // } catch (\Exception $e) {
        //     return response()->json(['success' => false, 'message' => $e->getMessage()], 200);
        // }
        }
    }

    public function report(Request $request)
    {
        try {
            if ($request->search) {
                $search = $request->search;
            }else{
                $search = ["all"];
            }

            if ($request->category) {
                $category = $request->category;
            }else{
                $category = ["all"];
            }

            if ($request->start_date) {
                $start_date = Carbon::parse($request->start_date)->format('Y-m-d').' 00:00:00';
            }else{
                $start_date = Carbon::today()->format('Y-m-d').' 00:00:00';
            }
            if ($request->to_date) {
                $to_date = Carbon::parse($request->to_date)->format('Y-m-d').' 23:59:00';
            }else{
                $to_date = Carbon::today()->format('Y-m-d').' 23:59:00';
            }
            
            $data = Workshop::with(['driver', 'kategoriKerusakan', 'answerQuestionUser', 'user', 'workshopDetail', 'workshopSla', 'asset', 'workshopDecision', 'statusPerbaikan', 'WorkshopFinalDecision', 'workshopInspection'])
                ->where(function($query) use($request){
                    if ($request->get('category') == 'all2') {
                        $query->where('service', '>', 1);
                    }

                    if ($request->get('company')) {
                        $asset_id_company = Asset::where('company_id', $request->get('company'))->pluck('id');
                        $query->whereIn('asset_id', $asset_id_company);
                    }

                    if ($request->get('location')) {
                        $asset_id_location = Asset::where('location_id', $request->get('location'))->pluck('id');
                        $query->whereIn('asset_id', $asset_id_location);
                    }
                })
                ->whereBetween('created_at', [$start_date,$to_date])
                ->whereNotNull('asset_id')
                ->orderBy('id', 'desc')
                ->get();
            
            foreach ($data as $key => $value) {
                $data[$key]['driver_name']  = ($value->driver->first_name ?? null).' '. ($value->driver->last_name ?? null);
                $data[$key]['license_no'] = collect($value->asset->assetDetail)->where('attribute_code', 'nomor_polisi')->first()->value ?? null;
                $data[$key]['riwayat_tindakan'] = AnswerQuestionUser::findOrFail($value->answer_question_user_id)->riwayatTindakan;
                $asset = Asset::findOrFail($value->asset_id);

                $data[$key]['company'] = Company::findOrFail($asset->company_id)->name ?? '-';
                $data[$key]['department'] = Department::findOrFail($asset->department_id)->name ?? '-';
                $data[$key]['location'] = MasterLocation::findOrFail($asset->location_id)->name ?? '-';
                
                $data[$key]['type_asset'] = TypeAsset::findOrFail($asset->type_asset_id)->name ?? '-';
                $data[$key]['workshop_inspection_note'] = '';
                $data[$key]['workshop_inspection_finish_repair'] = '';
                $mekanik = [];
                
                if (!empty($value->workshopInspection)) {
                    foreach ($value->workshopInspection as $cvalue) {
                        $data[$key]['workshop_inspection_note'] .= $cvalue->note.", ";
                        $data[$key]['workshop_inspection_finish_repair'] = $cvalue->created_at;
                        
                        if (!empty($cvalue->mekanik_id)) {
                            foreach (json_decode($cvalue->mekanik_id) as $xvalue) {
                                array_push($mekanik, $xvalue);
                            }
                        }
                    }
                }
                
                $data[$key]['workshop_inspection_mekanik'] = '';
                if (!empty($mekanik)) {
                    $mekanik = User::whereIn('id', array_unique($mekanik))->pluck('full_name');
                    foreach ($mekanik as $cvalue) {
                        $data[$key]['workshop_inspection_mekanik'] .= $cvalue.", ";
                    }
                }

                $sla = WorkshopSla::where('workshop_id', $value->id)->orderBy('id', 'desc')->first();
                if (!empty($sla)) {
                    $data[$key]['sla_created_at'] = $sla->created_at->format('Y-m-d');
                    $data[$key]['sla_deadline'] = $sla->estimasi_pengerjaan;
                    $to = Carbon::parse($sla->estimasi_pengerjaan);
                    $from = Carbon::today();
                    $data[$key]['sla_status_time'] = ($to->diffInDays($from) > 0) ? 'Late Time' : 'On Time';
                }else{
                    $data[$key]['sla_created_at'] = '';
                    $data[$key]['sla_deadline'] = '';
                    $data[$key]['sla_status_time'] = '';
                }

                $data[$key]['jenis_kerusakan'] = '';
                $data[$key]['jenis_perbaikan'] = '';
                foreach ($value->workshopDetail as $cvalue) {
                    $data[$key]['jenis_kerusakan'] .= $cvalue->jenis_kerusakan .', ';
                    $data[$key]['jenis_perbaikan'] .= $cvalue->jenis_perbaikan .', ';
                }

                $data[$key]['finish_workshop_inspection'] = WorkshopInspection::where('workshop_id', $value->id)->orderBy('id', 'desc')->first()->created_at ?? '-';
                $sla = WorkshopSla::where('workshop_id', $value->id)->orderBy('id', 'desc')->first();
                if (!empty($sla)) {
                    $data[$key]['sla_created_at'] = $sla->created_at->format('Y-m-d');
                    $data[$key]['sla_deadline'] = $sla->estimasi_pengerjaan;
                    try {
                        $to = Carbon::parse($sla->estimasi_pengerjaan);
                        $from = Carbon::parse($value['finish_workshop_inspection']);
                        $data[$key]['sla_status_time'] = ($to->diffInDays($from) > 0) ? 'Late Time' : 'On Time';
                    } catch (\Throwable $e) {
                        $data[$key]['sla_status_time'] = '-';
                    }
                }

                $data[$key]['sparepart'] = [];
                foreach ($value->workshopDetail as $sparepart) {
                    $data[$key]['sparepart'] = $sparepart->sparepartUse;
                }
            }
            
            return view('export.workshop', ['data' => $data, 'search' => $search, 'category' => $category]);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    public function reportExport(Request $request)
    {
        try {
            if ($request->search) {
                $search = $request->search;
            }else{
                $search = ["all"];
            }

            if ($request->category) {
                $category = $request->category;
            }else{
                $category = ["all"];
            }

            if ($request->start_date) {
                $start_date = Carbon::parse($request->start_date)->format('Y-m-d').' 00:00:00';
            }else{
                $start_date = Carbon::today()->format('Y-m-d').' 00:00:00';
            }
            if ($request->to_date) {
                $to_date = Carbon::parse($request->to_date)->format('Y-m-d').' 23:59:00';
            }else{
                $to_date = Carbon::today()->format('Y-m-d').' 23:59:00';
            }
            
            $data = Workshop::with(['driver', 'kategoriKerusakan', 'answerQuestionUser', 'user', 'workshopDetail', 'workshopSla', 'asset', 'workshopDecision', 'statusPerbaikan', 'WorkshopFinalDecision', 'workshopInspection'])
                ->where(function($query) use($request){
                    if ($request->get('category') == 'all2') {
                        $query->where('service', '>', 1);
                    }

                    if ($request->get('company')) {
                        $asset_id_company = Asset::where('company_id', $request->get('company'))->pluck('id');
                        $query->whereIn('asset_id', $asset_id_company);
                    }

                    if ($request->get('location')) {
                        $asset_id_location = Asset::where('location_id', $request->get('location'))->pluck('id');
                        $query->whereIn('asset_id', $asset_id_location);
                    }
                })
                ->whereBetween('created_at', [$start_date,$to_date])
                ->whereNotNull('asset_id')
                ->orderBy('id', 'desc')
                ->get();
            
            foreach ($data as $key => $value) {
                $data[$key]['driver_name']  = ($value->driver->first_name ?? null).' '. ($value->driver->last_name ?? null);
                $data[$key]['license_no'] = collect($value->asset->assetDetail)->where('attribute_code', 'nomor_polisi')->first()->value ?? null;
                $data[$key]['riwayat_tindakan'] = AnswerQuestionUser::findOrFail($value->answer_question_user_id)->riwayatTindakan;
                $asset = Asset::findOrFail($value->asset_id);

                $data[$key]['company'] = Company::findOrFail($asset->company_id)->name ?? '-';
                $data[$key]['department'] = Department::findOrFail($asset->department_id)->name ?? '-';
                $data[$key]['location'] = MasterLocation::findOrFail($asset->location_id)->name ?? '-';
                
                $data[$key]['type_asset'] = TypeAsset::findOrFail($asset->type_asset_id)->name ?? '-';
                $data[$key]['workshop_inspection_note'] = '';
                $data[$key]['workshop_inspection_finish_repair'] = '';
                $mekanik = [];
                
                if (!empty($value->workshopInspection)) {
                    foreach ($value->workshopInspection as $cvalue) {
                        $data[$key]['workshop_inspection_note'] .= $cvalue->note.", ";
                        $data[$key]['workshop_inspection_finish_repair'] = $cvalue->created_at;
                        
                        if (!empty($cvalue->mekanik_id)) {
                            foreach (json_decode($cvalue->mekanik_id) as $xvalue) {
                                array_push($mekanik, $xvalue);
                            }
                        }
                    }
                }
                
                $data[$key]['workshop_inspection_mekanik'] = '';
                if (!empty($mekanik)) {
                    $mekanik = User::whereIn('id', array_unique($mekanik))->pluck('full_name');
                    foreach ($mekanik as $cvalue) {
                        $data[$key]['workshop_inspection_mekanik'] .= $cvalue.", ";
                    }
                }

                $sla = WorkshopSla::where('workshop_id', $value->id)->orderBy('id', 'desc')->first();
                if (!empty($sla)) {
                    $data[$key]['sla_created_at'] = $sla->created_at->format('Y-m-d');
                    $data[$key]['sla_deadline'] = $sla->estimasi_pengerjaan;
                    $to = Carbon::parse($sla->estimasi_pengerjaan);
                    $from = Carbon::today();
                    $data[$key]['sla_status_time'] = ($to->diffInDays($from) > 0) ? 'Late Time' : 'On Time';
                }else{
                    $data[$key]['sla_created_at'] = '';
                    $data[$key]['sla_deadline'] = '';
                    $data[$key]['sla_status_time'] = '';
                }

                $data[$key]['jenis_kerusakan'] = '';
                $data[$key]['jenis_perbaikan'] = '';
                foreach ($value->workshopDetail as $cvalue) {
                    $data[$key]['jenis_kerusakan'] .= $cvalue->jenis_kerusakan .', ';
                    $data[$key]['jenis_perbaikan'] .= $cvalue->jenis_perbaikan .', ';
                }

                $data[$key]['finish_workshop_inspection'] = WorkshopInspection::where('workshop_id', $value->id)->orderBy('id', 'desc')->first()->created_at ?? '-';
                $sla = WorkshopSla::where('workshop_id', $value->id)->orderBy('id', 'desc')->first();
                if (!empty($sla)) {
                    $data[$key]['sla_created_at'] = $sla->created_at->format('Y-m-d');
                    $data[$key]['sla_deadline'] = $sla->estimasi_pengerjaan;
                    try {
                        $to = Carbon::parse($sla->estimasi_pengerjaan);
                        $from = Carbon::parse($value['finish_workshop_inspection']);
                        $data[$key]['sla_status_time'] = ($to->diffInDays($from) > 0) ? 'Late Time' : 'On Time';
                    } catch (\Throwable $e) {
                        $data[$key]['sla_status_time'] = '-';
                    }
                }

                $data[$key]['sparepart'] = [];
                foreach ($value->workshopDetail as $sparepart) {
                    $data[$key]['sparepart'] = $sparepart->sparepartUse;
                }
            }
            
            return Excel::download(new ExportWorkshop($data, $search, $category), 'laporan bengkel & workshop '.$start_date.' - '.$to_date.'.xlsx');
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }
}