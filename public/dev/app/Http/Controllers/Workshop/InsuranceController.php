<?php

namespace App\Http\Controllers\Workshop;

use App\Http\Controllers\Controller;
use App\Imports\ImportInsurance;
use App\Models\Fleet\Asset;
use App\Models\Fleet\Company;
use App\Models\Fleet\Department;
use App\Models\Fleet\MasterLocation;
use App\Workshop\Insurance;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Facades\Excel;

class InsuranceController extends Controller
{
    public function index()
    {
        $data = Insurance::orderBy("id", "desc")->get();

        return response()->json($data);
    }

    public function store(Request $request)
    {
        try { 
 
            $validator = Validator::make($request->all(), [
                'name' => 'required', 
            ]);

            if ($validator->fails()) {
                return response()->json($validator->errors(), 500);
            }
            $data = $request->all();
            $create = Insurance::create($data);

            return response()->json(['success' => true, 'message' => 'Berhasil input data']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    public function destroy($id)
    {
        try {
            Insurance::findOrFail($id)->delete();
            return response()->json(['success' => true, 'message' => 'Berhasil delete data']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }
 
    public function edit($id)
    {
        $data = Insurance::find($id);
        return response()->json($data);
    }

    public function update(Request $request, $id)
    {
        try {
            $data = $request->all();
            Insurance::findOrFail($id)->update($data);

            return response()->json(['success' => true, 'message' => 'Berhasil edit data']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    public function import(Request $request)
    {
        $file_parts = explode(";base64,", $request->file);
        if ($file_parts) {
            $file_type_aux = explode("application/", $file_parts[0]);
            $file_type = 'xlsx';
            $file_base64 = base64_decode($file_parts[1]);
            $folderPath = 'storage/insurance/';
            $fileName = uniqid();
            $fileFullPath = $folderPath.$fileName.".".$file_type;
            file_put_contents($fileFullPath, $file_base64);
            $insurance = $fileFullPath;
            // return public_path($insurance);
            Excel::import(new ImportInsurance, $insurance);
            return response()->json(['status' =>'Berhasil Import Data']);
        }else {
            return response()->json(['status' =>'excel tidak tersedia']);
        }
        // try {
    
        // } catch (\Throwable $th) {
        //     return response()->json(['status' => $th->getMessage()]);
        // }
    }
}
