<?php

namespace App\Http\Controllers\Api;

use App\FleetMaster\Driver;
use App\Models\User;
use Illuminate\Http\Request;
use App\Http\Requests\LoginRequest;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use App\Http\Requests\RegisterRequest;
use App\Models\Fleet\HistoryLocation;
use Illuminate\Auth\Events\Registered;
use App\Models\Fleet\AnswerQuestionUser;
use App\Models\Fleet\AssetDetail;
use App\Models\Fleet\UserLocationLog;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class AuthController extends Controller
{

    public function login(LoginRequest $request)
    {
        try {
            $input = $request->all();

            $fieldType = filter_var($request->email, FILTER_VALIDATE_EMAIL) ? 'email' : 'username';
            if (Auth::attempt(array($fieldType => $input['email'], 'password' => $input['password']))) {
                /** @var User $user */
                $user = Auth::user();

                if ($request->has('latitude') && $request->has('longitude')) {
                    $location_log = new UserLocationLog;
                    $location_log->date_log = Carbon::today();
                    $location_log->user_id = $user->id;
                    $location_log->latitude = $request->get('latitude');
                    $location_log->longitude = $request->get('longitude');
                    $location_log->save();
                }

                $data = User::with(['Company','department','branch','section','subSection','driver.vehicle'])->findOrFail($user->id);
                $photo = $data->photo;
                $data->photo = url('').'/'.$photo;
                $driver = [];
                if ($data->role == 'Driver' && isset($data->driver->user_id)) {
                    $driver['sim'] = $data->driver->driving_license;
                    $driver['sim_expired_date'] = $data->driver->sim_validity_period;

                    if (isset($data->driver->sim_validity_period)) {
                        $start_time = \Carbon\Carbon::now();
                        $finish_time = \Carbon\Carbon::parse($data->driver->sim_validity_period);

                        $result = $start_time->diffInDays($finish_time, false);
                        $driver['day_expired'] = $result;
                        if ($result < 30) {
                            $driver['sim_expired_warning'] = true;
                        }else{
                            $driver['sim_expired_warning'] = false;
                        }

                        if ($result < 0) {
                            $driver['sim_expired'] = true;
                        }else{
                            $driver['sim_expired'] = false;
                        }
                    }else{
                        $driver['day_expired'] = 0;
                    }

                    $driver['plate_no'] = $data->driver->vehicle->license_no;

                    //check checlist kesehatan
                    $kesehatan = AnswerQuestionUser::with('statusVehicle')->where('user_id',Auth::user()->id)->where('slug','checklist-health')->whereDate('input_date',Carbon::today())->whereNotNull('status_vehicle_id')->orderBy('id','desc')->first();

                    //check checlist kendaraan

                    $kendaraan = AnswerQuestionUser::with('statusVehicle')->where('user_id',Auth::user()->id)->where('slug','checklist-vehicle')->whereNotNull('status_vehicle_id')->where('type',1)->orderBy('id','desc')->first();

                    $kendaraan_stop = AnswerQuestionUser::with('statusVehicle')->where('user_id',Auth::user()->id)->where('slug','checklist-vehicle')->whereNotNull('status_vehicle_id')->where('type',2)->orderBy('id','desc')->first();

                    $kendaraan_last = AnswerQuestionUser::with('statusVehicle')->where('user_id',Auth::user()->id)->where('slug','checklist-vehicle')->whereNotNull('status_vehicle_id')->orderBy('id','desc')->first();

                    //check k3
                    $k3 = AnswerQuestionUser::with('statusVehicle')->where('user_id',Auth::user()->id)->where('slug','quiz')->whereDate('input_date',Carbon::today())->whereNotNull('status_vehicle_id')->where('type',1)->first();

                    $driver['reset_time'] = null;
                    if ($kesehatan) {
                        $kesehatan_last = $kesehatan->id;
                        if ($kesehatan->waiting) {
                            $waiting = Carbon::parse($kesehatan->waiting)->addMinutes(30);
                            if ($waiting->lte(Carbon::now())) {
                                $driver['done_check_kesehatan'] = false;
                                $driver['status_check_kesehatan'] = null;
                            }else{
                                $driver['done_check_kesehatan'] = true;
                                if (isset($kesehatan->statusVehicle->name)) {
                                    $driver['status_check_kesehatan'] = $kesehatan->statusVehicle->name;
                                }else{
                                    $driver['status_check_kesehatan'] = null;
                                }
                            }
                        }else{
                            $driver['done_check_kesehatan'] = true;
                            if (isset($kesehatan->statusVehicle->name)) {
                                $driver['status_check_kesehatan'] = $kesehatan->statusVehicle->name;
                            }else{
                                $driver['status_check_kesehatan'] = null;
                            }
                        }

                        if (is_null($kesehatan->waiting)) {
                            $driver['is_waiting_kesehatan'] = null;
                        }else{
                            $driver['is_waiting_kesehatan'] = $waiting->format('Y-m-d H:i:s');
                        }

                        if (is_null($kesehatan->bring_it_home)) {
                            $driver['is_pulangkan'] = false;
                        }else{
                            $driver['is_pulangkan'] = true;
                            $driver['done_check_kesehatan'] = true;
                            $driver['status_check_kesehatan'] = 'red';
                        }

                        if (is_null($kesehatan->note)) {
                            $driver['is_tindakan'] = null;
                        }else{
                            $driver['is_tindakan'] = $kesehatan->note;
                            $driver['done_check_kesehatan'] = true;
                            $driver['status_check_kesehatan'] = 'red';
                        }
                    }else{
                        $kesehatan_last = 0;
                        $driver['done_check_kesehatan'] = false;
                        $driver['status_check_kesehatan'] = null;
                        $driver['is_waiting_kesehatan'] = null;
                        $driver['is_pulangkan'] = false;
                        $driver['is_tindakan'] = null;
                    }

                    if ($k3) {
                        $k3_last = $k3->id;
                        $waiting = Carbon::parse($k3->waiting)->addMinutes(30);
                        if ($waiting->lte(Carbon::now())) {
                            $driver['done_check_k3'] = false;
                            $driver['status_check_k3'] = null;
                        }else{
                            $driver['done_check_k3'] = true;
                            if (isset($k3->statusVehicle->name)) {
                                $driver['status_check_k3'] = $k3->statusVehicle->name;
                            }else{
                                $driver['status_check_k3'] = null;
                            }
                        }
                    }else{
                        $k3_last = 0;
                        $driver['done_check_k3'] = false;
                        $driver['status_check_k3'] = null;
                    }

                    if ($kendaraan_last) {
                        if ($kendaraan_last->type == 1) {
                            if ($kendaraan_last->vehicle_repair) {
                                $driver['done_check_kendaraan'] = false;
                            }else if ($kendaraan && $kendaraan->id == $kendaraan_last->id) {
                                $driver['done_check_kendaraan'] = true;
                                if (isset($kendaraan->statusVehicle->name)) {
                                    if ($kendaraan->statusVehicle->name == 'green') {
                                        $driver['open_checklist_pulang'] = true;
                                    }else{
                                        $driver['open_checklist_pulang'] = false;
                                    }
                                    $driver['status_check_kendaraan'] = $kendaraan->statusVehicle->name;
                                }else{
                                    $driver['status_check_kendaraan'] = null;
                                    $driver['open_checklist_pulang'] = false;
                                }

                                if (is_null($kendaraan->vehicle_next_danger)) {
                                    $driver['tetap_lanjut_check_kendaraan'] = false;
                                }else{
                                    $driver['tetap_lanjut_check_kendaraan'] = true;
                                    $driver['done_check_kendaraan'] = true;
                                    $driver['status_check_kendaraan'] = 'green';
                                    $driver['open_checklist_pulang'] = true;
                                }
                            }else{
                                $driver['done_check_kendaraan'] = false;
                                $driver['status_check_kendaraan'] = null;
                                $driver['tetap_lanjut_check_kendaraan'] = false;
                                $driver['open_checklist_pulang'] = false;
                            }
                            
                            if ($kendaraan_stop && $kendaraan_stop->id > $kendaraan_last->id) {
                                $driver['done_check_kendaraan_pulang'] = true;
                                $driver['open_checklist_pulang'] = true;
                                if (isset($kendaraan_stop->statusVehicle->name)) {
                                    $driver['status_check_kendaraan_pulang'] = $kendaraan_stop->statusVehicle->name;
                                }else{
                                    $driver['status_check_kendaraan_pulang'] = null;
                                }

                                if (is_null($kendaraan_stop->vehicle_next_danger)) {
                                    $driver['tetap_lanjut_check_kendaraan_pulang'] = false;
                                }else{
                                    $driver['tetap_lanjut_check_kendaraan_pulang'] = true;
                                    $driver['done_check_kendaraan_pulang'] = true;
                                    $driver['status_check_kendaraan_pulang'] = 'red';
                                    $driver['open_checklist_pulang'] = true;
                                }
                            }else{
                                $driver['tetap_lanjut_check_kendaraan_pulang'] = false;
                                $driver['done_check_kendaraan_pulang'] = false;
                                $driver['status_check_kendaraan_pulang'] = null;
                            }
                        }else{
                            if ($kendaraan_last->vehicle_repair) {
                                $driver['done_check_kendaraan'] = false;
                            }else if ($kendaraan && $kendaraan->id < $kendaraan_last->id) {
                                $driver['done_check_kendaraan'] = true;
                                if (isset($kendaraan->statusVehicle->name)) {
                                    $driver['status_check_kendaraan'] = $kendaraan->statusVehicle->name;
                                }else{
                                    $driver['status_check_kendaraan'] = null;
                                }
                                if (is_null($kendaraan->vehicle_next_danger)) {
                                    $driver['tetap_lanjut_check_kendaraan'] = false;
                                }else{
                                    $driver['tetap_lanjut_check_kendaraan'] = true;
                                    $driver['done_check_kendaraan'] = true;
                                    $driver['status_check_kendaraan'] = 'green';
                                }
                            }else{
                                $driver['tetap_lanjut_check_kendaraan'] = false;
                                $driver['done_check_kendaraan'] = false;
                                $driver['status_check_kendaraan'] = null;
                            }

                            if ($kendaraan_stop && $kendaraan_stop->id == $kendaraan_last->id) {
                                $driver['done_check_kendaraan_pulang'] = true;
                                if (isset($kendaraan_stop->statusVehicle->name)) {
                                    $driver['status_check_kendaraan_pulang'] = $kendaraan_stop->statusVehicle->name;
                                }else{
                                    $driver['status_check_kendaraan_pulang'] = null;
                                }

                                if (is_null($kendaraan_stop->vehicle_next_danger)) {
                                    $driver['tetap_lanjut_check_kendaraan_pulang'] = false;
                                }else{
                                    $driver['tetap_lanjut_check_kendaraan_pulang'] = true;
                                    $driver['done_check_kendaraan_pulang'] = true;
                                    $driver['status_check_kendaraan_pulang'] = 'red';
                                }
                            }else{
                                $driver['tetap_lanjut_check_kendaraan_pulang'] = false;
                                $driver['done_check_kendaraan_pulang'] = false;
                                $driver['status_check_kendaraan_pulang'] = null;
                            }

                            $driver['open_checklist_pulang'] = false;
                        }

                        // JIKA KENDARAAN BELUM PULANG MAKA TIDAK ADA RESET
                        if ($driver['done_check_k3'] == true && $driver['done_check_kesehatan'] == true && $driver['done_check_kendaraan'] == true && $driver['done_check_kendaraan_pulang'] = false) {
                            $driver['done_check_k3'] = false;
                            $driver['done_check_kesehatan'] = false;
                            $driver['done_check_kendaraan'] = false;
                        }

                        // JIKA KENDARAAN PULANG RESET SEMUA SETELAH 15 MENIT
                        $reset_time = Carbon::parse($kendaraan_last->created_at)->addMinutes(15);
                        $driver['reset_time'] = Carbon::parse($reset_time)->format('Y-m-d H:i:s');
                        if ($kendaraan_last->type == 2 && $reset_time->lte(Carbon::now())) {
                            if ($kendaraan_last->id > $k3_last) {
                                $driver['done_check_k3'] = false;
                                $driver['done_check_kesehatan'] = false;
                                $driver['done_check_kendaraan'] = false;
                                $driver['status_check_k3'] = null;
                            }

                            if ($kendaraan_last->id > $kesehatan_last) {
                                $driver['done_check_kesehatan'] = false;
                                $driver['status_check_kesehatan'] = null;
                                $driver['is_waiting_kesehatan'] = null;
                                $driver['is_pulangkan'] = false;
                                $driver['is_tindakan'] = null;
                            }



                            if (isset($kendaraan->id) && $kendaraan_last->id > $kendaraan->id) {
                                $driver['done_check_kendaraan'] = false;
                                $driver['status_check_kendaraan'] = null;
                                $driver['tetap_lanjut_check_kendaraan'] = false;
                            }

                            if (isset($kendaraan_stop->id) && $kendaraan_last->id > $kendaraan_stop->id) {
                                $driver['done_check_kendaraan_pulang'] = false;
                                $driver['status_check_kendaraan_pulang'] = null;
                                $driver['open_checklist_pulang'] = false;
                                $driver['tetap_lanjut_check_kendaraan_pulang'] = false;
                            }

                            $driver['reset_time'] = null;
                        }
                    }else{
                        $driver['done_check_kendaraan'] = false;
                        $driver['status_check_kendaraan'] = null;
                        $driver['done_check_kendaraan_pulang'] = false;
                        $driver['status_check_kendaraan_pulang'] = null;
                        $driver['open_checklist_pulang'] = false;
                        $driver['tetap_lanjut_check_kendaraan'] = false;
                        $driver['tetap_lanjut_check_kendaraan_pulang'] = false;
                    }
                }

                if (!count($driver)) {
                    $driver = null;
                }

                $data['vehicle_id'] = Driver::where('user_id', $data->id)->first()->vehicle_id ?? '';
                $data['driver_data'] = $driver;
                $data['full_name'] = $data->first_name.' '.$data->last_name;
                $data['vehicle_id'] = Driver::where('user_id', $user->id)->first()->vehicle_id ?? '';

                $token = $user->createToken('API Token')->accessToken;

                //last history checklist kendaraan
                $last_check_kendaraan = [];

                $last_check_kendaraan_get = AnswerQuestionUser::with('statusVehicle')
                ->where('user_id',Auth::user()->id)
                ->where('slug','checklist-vehicle')
                ->whereDate('input_date',Carbon::today())
                ->whereNotNull('status_vehicle_id')
                ->orderBy('id','desc')
                ->get();

                Carbon::setLocale('id');

                foreach ($last_check_kendaraan_get as $key => $kendaraan) {
                    $last_check_kendaraan[$key]['id'] = $kendaraan->id;
                    $last_check_kendaraan[$key]['tanggal'] = Carbon::parse($kendaraan->created_at)->isoFormat('dddd, D MMMM Y').' '.Carbon::parse($kendaraan->created_at)->format('H:i');
                    $last_check_kendaraan[$key]['status']['name'] = $kendaraan->statusVehicle->name;
                    $last_check_kendaraan[$key]['status']['color'] = $kendaraan->statusVehicle->color;
                    $last_check_kendaraan[$key]['licenso_no'] = AssetDetail::where('asset_id', $kendaraan->asset->id)->where('attribute_code', 'nomor_polisi')->first()->value ?? null;
                    if ($kendaraan->type == 1) {
                        $last_check_kendaraan[$key]['type'] = 'berangkat';
                    }else{
                        $last_check_kendaraan[$key]['type'] = 'pulang';
                    }
                }

                $data['last_check_kendaraan'] = $last_check_kendaraan;

                //last history checklist kesehatan
                $last_check_kesehatan = [];

                $last_check_kesehatan_get = AnswerQuestionUser::with('statusVehicle')
                ->where('user_id',Auth::user()->id)
                ->where('slug','checklist-health')
                ->whereDate('input_date',Carbon::today())
                ->whereNotNull('status_vehicle_id')
                ->orderBy('id','desc')
                // ->skip(0)->take(3)
                ->get();

                Carbon::setLocale('id');

                foreach ($last_check_kesehatan_get as $key => $kesehatan) {
                    $last_check_kesehatan[$key]['id'] = $kesehatan->id;
                    $last_check_kesehatan[$key]['tanggal'] = Carbon::parse($kesehatan->created_at)->isoFormat('dddd, D MMMM Y').' '.Carbon::parse($kesehatan->created_at)->format('H:i');
                    $last_check_kesehatan[$key]['status']['name'] = $kesehatan->statusVehicle->name;
                    $last_check_kesehatan[$key]['status']['color'] = $kesehatan->statusVehicle->color;
                }

                $data['last_check_kesehatan'] = $last_check_kesehatan;

                if (config('auth.must_verify_email') && !$user->hasVerifiedEmail()) {
                    return response([
                        'message' => 'Email must be verified.'
                    ], 401);
                }

                return response([
                    'message' => 'success',
                    'token' => $token,
                    'user' => $data
                ]);
            }else{
                return response([
                    'message' => 'Invalid Email or password.'
                ], 200);
            }
        } catch (\Exception $e) {
            return response([
                'message' => $e->getMessage()
            ], 200);
        }

        return response([
            'message' => 'Invalid Email or password.'
        ], 200);
    }

    public function user()
    {
        $auth = Auth::user();
        $data = User::with(['Company','department','branch','section','subSection','driver.vehicle'])->findOrFail($auth->id);
        $photo = $data->photo;
        $data->photo = url('').'/'.$photo;
        $driver = [];
        if ($data->role == 'Driver' && !isset($data->driver->user_id)) {
            $driver['sim'] = null;
        }
        if ($data->role == 'Driver' && isset($data->driver->user_id)) {
            $driver['sim'] = $data->driver->driving_license;
            $driver['sim_expired_date'] = $data->driver->sim_validity_period;
            $driver['sim_type'] = $data->driver->type_sim->name;
            $driver['driver_birthday']  = $data->driver->birth;
            $sim_period = now()->diffInDays($data->driver->sim_validity_period);

            if ($data->driver->sim_validity_period) {
                $data['sim'] = 'ada';
                if ($data->driver->sim_validity_period < now()) {
                    $data['sim_period'] = "masa berlaku sim sudah lewat ".$sim_period." hari";
                }else{
                    $data['sim_period'] = "masa berlaku sim kurang ".$sim_period." hari";
                }
            }else{
                $data['sim'] = 'tidak ada sim';
                $data['sim_period'] = null;
            }

            if (isset($data->driver->sim_validity_period)) {
                $start_time = \Carbon\Carbon::now();
                $finish_time = \Carbon\Carbon::parse($data->driver->sim_validity_period);

                $result = $start_time->diffInDays($finish_time, false);
                $driver['day_expired'] = $result;
                if ($result < 30) {
                    $driver['sim_expired_warning'] = true;
                }else{
                    $driver['sim_expired_warning'] = false;
                }

                if ($result < 0) {
                    $driver['sim_expired'] = true;
                }else{
                    $driver['sim_expired'] = false;
                }
            }else{
                $driver['day_expired'] = 0;
            }

            $driver['plate_no'] = $data->driver->vehicle->license_no;

            //check checlist kesehatan
            $kesehatan = AnswerQuestionUser::with('statusVehicle')->where('user_id',Auth::user()->id)->where('slug','checklist-health')->whereDate('input_date',Carbon::today())->whereNotNull('status_vehicle_id')->orderBy('id','desc')->first();

            //check checlist kendaraan

            $kendaraan = AnswerQuestionUser::with('statusVehicle')->where('user_id',Auth::user()->id)->where('slug','checklist-vehicle')->whereNotNull('status_vehicle_id')->where('type',1)->orderBy('id','desc')->first();

            $kendaraan_stop = AnswerQuestionUser::with('statusVehicle')->where('user_id',Auth::user()->id)->where('slug','checklist-vehicle')->whereNotNull('status_vehicle_id')->where('type',2)->orderBy('id','desc')->first();

            $kendaraan_last = AnswerQuestionUser::with('statusVehicle')->where('user_id',Auth::user()->id)->where('slug','checklist-vehicle')->whereNotNull('status_vehicle_id')->orderBy('id','desc')->first();

            //check k3
            $k3 = AnswerQuestionUser::with('statusVehicle')->where('user_id',Auth::user()->id)->where('slug','quiz')->whereDate('input_date',Carbon::today())->whereNotNull('status_vehicle_id')->where('type',1)->orderBy('id', 'desc')->first();

            $driver['reset_time'] = null;
            $driver['id_checklist_kendaraan'] = $kendaraan_last->id ?? null;

            // CHECKLIST NOT FINISHED
            $id_checklist_not_finished = AnswerQuestionUser::where('user_id',Auth::user()->id)->orderBy('id','desc')->first();

            if ($id_checklist_not_finished && $id_checklist_not_finished->status == 0 && $id_checklist_not_finished->status_vehicle_id == null) {
                if ($id_checklist_not_finished->slug == 'checklist-vehicle') {
                    if ($id_checklist_not_finished->type == 1) {
                        $driver['id_sesi_check_kendaraan_berangkat'] = $id_checklist_not_finished->id;
                    }elseif ($id_checklist_not_finished->type == 2) {
                        $driver['id_sesi_check_kendaraan_pulang'] = $id_checklist_not_finished->id;
                    }
                }elseif ($id_checklist_not_finished->slug == 'checklist-health') {
                    $driver['id_sesi_check_kesehatan'] = $id_checklist_not_finished->location_checklist_id;
                }elseif ($id_checklist_not_finished->slug == 'quiz') {
                    $driver['id_sesi_check_quiz'] = $id_checklist_not_finished->id;
                }
            }

            if ($kesehatan) {
                $kesehatan_last = $kesehatan->id;
                if ($kesehatan->waiting) {
                    $waiting = Carbon::parse($kesehatan->waiting)->addMinutes(30);
                    if ($waiting->lte(Carbon::now())) {
                        $driver['done_check_kesehatan'] = false;
                        $driver['status_check_kesehatan'] = null;
                    }else{
                        $driver['done_check_kesehatan'] = true;
                        if (isset($kesehatan->statusVehicle->name)) {
                            $driver['status_check_kesehatan'] = $kesehatan->statusVehicle->name;
                        }else{
                            $driver['status_check_kesehatan'] = null;
                        }

                        if($kesehatan->status_vehicle_id == 2){
                            if ($kesehatan->vehicle_next_danger == 1) {
                                $driver['allow_chek_vehicle'] = true;
                                $driver['allow_chek_vehicle_message'] = 'anda diizinkan cheklist kendaraan';
                            }else{
                                $driver['allow_chek_vehicle'] = false;
                                $driver['allow_chek_vehicle_message'] = 'periksa kesehatan anda';
                            }
                        }
                    }
                }else{
                    $driver['done_check_kesehatan'] = true;
                    if (isset($kesehatan->statusVehicle->name)) {
                        $driver['status_check_kesehatan'] = $kesehatan->statusVehicle->name;
                    }else{
                        $driver['status_check_kesehatan'] = null;
                    }

                    if($kesehatan->status_vehicle_id == 2 && $kesehatan->vehicle_next_danger){
                        if ($kesehatan->vehicle_next_danger == 1) {
                            $driver['allow_chek_vehicle'] = true;
                            $driver['allow_chek_vehicle_message'] = 'anda diizinkan cheklist kendaraan';
                        }else{
                            $driver['allow_chek_vehicle'] = false;
                            $driver['allow_chek_vehicle_message'] = 'periksa kesehatan anda';
                        }
                    }
                }

                if (is_null($kesehatan->waiting)) {
                    $driver['is_waiting_kesehatan'] = null;
                }else{
                    $driver['is_waiting_kesehatan'] = $waiting->format('Y-m-d H:i:s');
                }

                if (is_null($kesehatan->bring_it_home)) {
                    $driver['is_pulangkan'] = false;
                }else{
                    $driver['is_pulangkan'] = true;
                    $driver['done_check_kesehatan'] = true;
                    $driver['status_check_kesehatan'] = 'red';
                }

                if (is_null($kesehatan->note)) {
                    $driver['is_tindakan'] = null;
                }else{
                    $driver['is_tindakan'] = $kesehatan->note;
                    $driver['done_check_kesehatan'] = true;
                    $driver['status_check_kesehatan'] = 'red';
                }

                // if ($kesehatan->vehicle_next_danger == 1) {
                //     $driver['done_check_kesehatan'] = true;
                //     $driver['status_check_kesehatan'] = $kesehatan->statusVehicle->name;
                // }
            }else{
                $kesehatan_last = 0;
                $driver['done_check_kesehatan'] = false;
                $driver['status_check_kesehatan'] = null;
                $driver['is_waiting_kesehatan'] = null;
                $driver['is_pulangkan'] = false;
                $driver['is_tindakan'] = null;
            }

            if ($k3) {
                $k3_last = $k3->id;
                $waiting = Carbon::parse($k3->waiting)->addMinutes(30);
                if ($waiting->lte(Carbon::now())) {
                    $driver['done_check_k3'] = false;
                    $driver['status_check_k3'] = null;
                }else{
                    $driver['done_check_k3'] = true;
                    if (isset($k3->statusVehicle->name)) {
                        $driver['status_check_k3'] = $k3->statusVehicle->name;
                    }else{
                        $driver['status_check_k3'] = null;
                    }
                }
            }else{
                $k3_last = 0;
                $driver['done_check_k3'] = false;
                $driver['status_check_k3'] = null;
            }
            
            $driver['done_check_kendaraan'] = false;
            if ($kendaraan_last) {
                if ($kendaraan_last->vehicle_repair) {
                }else if ($kendaraan_last->type == 1) {
                    if ($kendaraan && $kendaraan->id == $kendaraan_last->id) {
                        $driver['done_check_kendaraan'] = true;
                        if (isset($kendaraan->statusVehicle->name)) {
                            if ($kendaraan->statusVehicle->name == 'green') {
                                $driver['open_checklist_pulang'] = true;
                            }else{
                                $driver['open_checklist_pulang'] = false;
                            }
                            $driver['status_check_kendaraan'] = $kendaraan->statusVehicle->name;
                        }else{
                            $driver['status_check_kendaraan'] = null;
                            $driver['open_checklist_pulang'] = false;
                        }

                        // if (is_null($kendaraan->vehicle_next_danger)) {
                        //     $driver['tetap_lanjut_check_kendaraan'] = false;
                        // }else{
                        //     $driver['tetap_lanjut_check_kendaraan'] = true;
                        //     $driver['done_check_kendaraan'] = true;
                        //     $driver['status_check_kendaraan'] = 'green';
                        //     $driver['open_checklist_pulang'] = true;
                        // }
                    }else{
                        $driver['done_check_kendaraan'] = false;
                        $driver['status_check_kendaraan'] = null;
                        $driver['tetap_lanjut_check_kendaraan'] = false;
                        $driver['open_checklist_pulang'] = false;
                    }
                    
                    if ($kendaraan_stop && $kendaraan_stop->id > $kendaraan_last->id) {
                        $driver['done_check_kendaraan_pulang'] = true;
                        $driver['open_checklist_pulang'] = true;
                        if (isset($kendaraan_stop->statusVehicle->name)) {
                            $driver['status_check_kendaraan_pulang'] = $kendaraan_stop->statusVehicle->name;
                        }else{
                            $driver['status_check_kendaraan_pulang'] = null;
                        }

                        // if (is_null($kendaraan_stop->vehicle_next_danger)) {
                        //     $driver['tetap_lanjut_check_kendaraan_pulang'] = false;
                        // }else{
                        //     $driver['tetap_lanjut_check_kendaraan_pulang'] = true;
                        //     $driver['done_check_kendaraan_pulang'] = true;
                        //     $driver['status_check_kendaraan_pulang'] = 'red';
                        //     $driver['open_checklist_pulang'] = true;
                        // }
                    }else{
                        $driver['tetap_lanjut_check_kendaraan_pulang'] = false;
                        $driver['done_check_kendaraan_pulang'] = false;
                        $driver['status_check_kendaraan_pulang'] = null;
                    }
                }else{
                    if ($kendaraan && $kendaraan->id > $kendaraan_last->id) {
                        $driver['done_check_kendaraan'] = false;
                        if (isset($kendaraan->statusVehicle->name)) {
                            $driver['done_check_kendaraan'] = true;
                            $driver['status_check_kendaraan'] = $kendaraan->statusVehicle->name;
                        }else{
                            $driver['status_check_kendaraan'] = null;
                            $driver['done_check_kendaraan'] = false;
                        }
                        // if (is_null($kendaraan->vehicle_next_danger)) {
                        //     $driver['tetap_lanjut_check_kendaraan'] = false;
                        // }else{
                        //     $driver['tetap_lanjut_check_kendaraan'] = true;
                        //     $driver['done_check_kendaraan'] = true;
                        //     $driver['status_check_kendaraan'] = 'green';
                        // }
                    }else{
                        $driver['tetap_lanjut_check_kendaraan'] = false;
                        $driver['done_check_kendaraan'] = false;
                        $driver['status_check_kendaraan'] = null;
                    }

                    if ($kendaraan_stop && $kendaraan_stop->id == $kendaraan_last->id) {
                        $driver['done_check_kendaraan_pulang'] = false;
                        if (isset($kendaraan_stop->statusVehicle->name)) {
                            $driver['done_check_kendaraan_pulang'] = true;
                            $kendaraan_stop->statusVehicle->name;
                        }else{
                            $driver['status_check_kendaraan_pulang'] = null;
                            $driver['done_check_kendaraan_pulang'] = false;
                        }

                        // if (is_null($kendaraan_stop->vehicle_next_danger)) {
                        //     $driver['tetap_lanjut_check_kendaraan_pulang'] = false;
                        // }else{
                        //     $driver['tetap_lanjut_check_kendaraan_pulang'] = true;
                        //     $driver['done_check_kendaraan_pulang'] = true;
                        //     $driver['status_check_kendaraan_pulang'] = 'red';
                        // }
                    }else{
                        $driver['tetap_lanjut_check_kendaraan_pulang'] = false;
                        $driver['done_check_kendaraan_pulang'] = false;
                        $driver['status_check_kendaraan_pulang'] = null;
                    }

                    $driver['open_checklist_pulang'] = false;
                }
                
                // FILTER KENDARAAN JALAN/TIDAK
                if ($kendaraan_last) {
                    if ($kendaraan_last->vehicle_repair == 1) {
                        $driver['allow_kendaraan'] = false;
                        $driver['approve_vehicle_danger'] = false;
                    }elseif ($kendaraan_last->vehicle_next_danger == 1) {
                        $driver['allow_kendaraan'] = true;
                        $driver['approve_vehicle_danger'] = true;
                    }elseif ($kendaraan_last->vehicle_next_danger == 2) {
                        $driver['allow_kendaraan'] = true;
                        $driver['done_check_kendaraan'] = true;
                        $driver['approve_vehicle_danger'] = true;
                    }else{
                        if ($kendaraan_last->type == 2) {
                            $driver['allow_kendaraan'] = true;
                        }elseif ($kendaraan_last->type == 1) {
                            if ($kendaraan_last->status_vehicle_id == 2 || $kendaraan_last->status_vehicle_id == 1) {
                                $driver['allow_kendaraan'] = true;
                                $driver['approve_vehicle_danger'] = false;
                            }elseif ($kendaraan_last->status_vehicle_id == 1 || $kendaraan_last->status_vehicle_id == 4) {
                                $driver['allow_kendaraan'] = false;
                                $driver['approve_vehicle_danger'] = false;
                            }
                        }
                    }
                }
                
                // JIKA KENDARAAN PULANG RESET SEMUA SETELAH 15 MENIT
                $reset_time = Carbon::parse($kendaraan_last->created_at)->addMinutes(15);
                $driver['reset_time'] = Carbon::parse($reset_time)->format('Y-m-d H:i:s');
                if ($kendaraan_last->type == 2) {
                    if (now() > $reset_time) {
                        if ($kendaraan_last->id > $k3_last) {
                            $driver['done_check_k3'] = false;
                            $driver['done_check_kesehatan'] = false;
                            $driver['done_check_kendaraan'] = false;
                            $driver['done_check_kendaraan_pulang'] = false;
                            $driver['status_check_k3'] = null;
                        }
    
                        if ($kendaraan_last->id > $kesehatan_last) {
                            $driver['done_check_kesehatan'] = false;
                            $driver['status_check_kesehatan'] = null;
                            $driver['is_waiting_kesehatan'] = null;
                            $driver['is_pulangkan'] = false;
                            $driver['is_tindakan'] = null;
                        }
    
                        if (isset($kendaraan->id) && $kendaraan_last->id > $kendaraan->id) {
                            $driver['done_check_kendaraan'] = false;
                            $driver['status_check_kendaraan'] = null;
                            $driver['tetap_lanjut_check_kendaraan'] = false;
                        }
    
                        if (isset($kendaraan_stop->id) && $kendaraan_last->id > $kendaraan_stop->id) {
                            $driver['done_check_kendaraan_pulang'] = false;
                            $driver['status_check_kendaraan_pulang'] = null;
                            $driver['open_checklist_pulang'] = false;
                            $driver['tetap_lanjut_check_kendaraan_pulang'] = false;
                        }
    
                        $driver['reset_time'] = null;
                    }else{
                        $driver['done_check_k3'] = true;
                        $driver['done_check_kesehatan'] = true;
                        $driver['status_check_kesehatan'] = AnswerQuestionUser::with('statusVehicle')->where('user_id',Auth::user()->id)->where('slug','checklist-health')->whereNotNull('status_vehicle_id')->orderBy('id','desc')->first()->statusVehicle->name ?? null;
                        $driver['done_check_kendaraan'] = true;
                        $driver['status_check_kendaraan'] = $kendaraan->statusVehicle->name ?? null;
                        $driver['done_check_kendaraan_pulang'] = true;
                        $driver['status_check_kendaraan_pulang'] = $kendaraan_stop->statusVehicle->name ?? null;
                        $driver['allow_kendaraan'] = true;
                    }
                }
            }else{
                $driver['done_check_kendaraan'] = false;
                $driver['status_check_kendaraan'] = null;
                $driver['done_check_kendaraan_pulang'] = false;
                $driver['status_check_kendaraan_pulang'] = null;
                $driver['open_checklist_pulang'] = false;
                $driver['tetap_lanjut_check_kendaraan'] = false;
                $driver['tetap_lanjut_check_kendaraan_pulang'] = false;
            }
        }

        // JIKA KENDARAAN BELUM PULANG MAKA TIDAK ADA RESET
        if (isset($driver['done_check_kendaraan'])) {
            if ($driver['done_check_kendaraan'] == true && $driver['done_check_kendaraan_pulang'] == false) {
                $driver['done_check_k3'] = true;
                $driver['status_check_k3'] = $k3->statusVehicle->name ?? null;
                $driver['done_check_kesehatan'] = true;
                // FILTER KENDARAAN JALAN/TIDAK
                if ($kendaraan_last) {
                    if ($kendaraan_last->vehicle_repair == 1) {
                        $driver['allow_kendaraan'] = false;
                        $driver['approve_vehicle_danger'] = false;
                    }elseif ($kendaraan_last->vehicle_next_danger == 1) {
                        $driver['allow_kendaraan'] = true;
                        $driver['approve_vehicle_danger'] = true;
                    }elseif ($kendaraan_last->vehicle_next_danger == 2) {
                        $driver['allow_kendaraan'] = true;
                        $driver['done_check_kendaraan'] = true;
                        $driver['approve_vehicle_danger'] = true;
                    }else{
                        if ($kendaraan_last->type == 2) {
                            $driver['allow_kendaraan'] = true;
                        }elseif ($kendaraan_last->type == 1) {
                            if ($kendaraan_last->status_vehicle_id == 2 || $kendaraan_last->status_vehicle_id == 1) {
                                $driver['allow_kendaraan'] = true;
                                $driver['approve_vehicle_danger'] = false;
                            }elseif ($kendaraan_last->status_vehicle_id == 1 || $kendaraan_last->status_vehicle_id == 4) {
                                $driver['allow_kendaraan'] = false;
                                $driver['approve_vehicle_danger'] = false;
                            }
                        }
                    }
                }
                $kesehatan2 = AnswerQuestionUser::with('statusVehicle')->where('user_id',Auth::user()->id)->where('slug','checklist-health')->whereNotNull('status_vehicle_id')->orderBy('id','desc')->first();
                $driver['status_check_kesehatan'] = $kesehatan2->statusVehicle->name ?? null;
            }
        }

        if (!count($driver)) {
            $driver = null;
        }

        $data['vehicle_id'] = Driver::where('user_id', $data->id)->first()->vehicle_id ?? '';
        $data['driver_data'] = $driver;
        $data['full_name'] = $data->first_name.' '.$data->last_name;
        
        //last history checklist kendaraan
        $last_check_kendaraan = [];

        $last_check_kendaraan_get = AnswerQuestionUser::with('statusVehicle')
        ->where('user_id',Auth::user()->id)
        ->where('slug','checklist-vehicle')
        ->whereDate('input_date',Carbon::today())
        ->whereNotNull('status_vehicle_id')
        ->orderBy('id','desc')
        ->get();

        Carbon::setLocale('id');

        foreach ($last_check_kendaraan_get as $key => $kendaraan) {
            $last_check_kendaraan[$key]['id'] = $kendaraan->id;
            $last_check_kendaraan[$key]['tanggal'] = Carbon::parse($kendaraan->created_at)->isoFormat('dddd, D MMMM Y').' '.Carbon::parse($kendaraan->created_at)->format('H:i');
            $last_check_kendaraan[$key]['status']['name'] = $kendaraan->statusVehicle->name;
            $last_check_kendaraan[$key]['status']['color'] = $kendaraan->statusVehicle->color;
            $last_check_kendaraan[$key]['licenso_no'] = AssetDetail::where('asset_id', $kendaraan->asset->id)->where('attribute_code', 'nomor_polisi')->first()->value ?? null;
            if ($kendaraan->type == 1) {
                $last_check_kendaraan[$key]['type'] = 'berangkat';
            }else{
                $last_check_kendaraan[$key]['type'] = 'pulang';
            }
        }

        $data['last_check_kendaraan'] = $last_check_kendaraan;

        //last history checklist kesehatan
        $last_check_kesehatan = [];

        $last_check_kesehatan_get = AnswerQuestionUser::with('statusVehicle')
        ->where('user_id',Auth::user()->id)
        ->where('slug','checklist-health')
        ->whereDate('input_date',Carbon::today())
        ->whereNotNull('status_vehicle_id')
        ->orderBy('id','desc')
        // ->skip(0)->take(3)
        ->get();

        Carbon::setLocale('id');

        foreach ($last_check_kesehatan_get as $key => $kesehatan) {
            $last_check_kesehatan[$key]['id'] = $kesehatan->id;
            $last_check_kesehatan[$key]['tanggal'] = Carbon::parse($kesehatan->created_at)->isoFormat('dddd, D MMMM Y').' '.Carbon::parse($kesehatan->created_at)->format('H:i');
            $last_check_kesehatan[$key]['status']['name'] = $kesehatan->statusVehicle->name;
            $last_check_kesehatan[$key]['status']['color'] = $kesehatan->statusVehicle->color;
        }

        $data['last_check_kesehatan'] = $last_check_kesehatan;

        return response()->json($data);
    }

    public function historyLocation(Request $request)
    {
        try {
            $data = $request->all();
            $data['user_id'] = Auth::user()->id;
            $historyLocation = HistoryLocation::create($data);
            return response()->json(['status' => true, 'message' => 'input data berhasil']);
        } catch (\Throwable $th) {
            return response()->json(['status' => false, 'message' => $th]);
        }
    }

    public function logout()
    {
        DB::table('oauth_access_tokens')
        ->where('user_id', Auth::user()->id)
        ->update([
            'revoked' => true
        ]);

        return response()->json(['status' => true, 'message' => 'Anda berhasil Logout']);
    }
}
