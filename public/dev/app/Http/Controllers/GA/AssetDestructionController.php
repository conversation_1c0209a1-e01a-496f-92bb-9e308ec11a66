<?php

namespace App\Http\Controllers\GA;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\GA\AssetDestruction;
use App\Models\GA\AssetDestructionItem;
use App\Models\GA\AssetDestructionSetting;
use App\Models\GA\AssetDestructionSetHistory;
use App\Models\Fleet\Location;
use App\Models\Fleet\LocStock;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use Auth;

class AssetDestructionController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $cek_set = $this->checkApproveSetting();
        if (!$cek_set['status']) {
            return response()->json(['success' => $cek_set['status'], 'message' => $cek_set['message']], 500);
        }
        
        $data = AssetDestruction::with('user')->orderBy('id','desc')->get();
        foreach ($data as $key => $datas) {
            //check approve
            $sets = AssetDestructionSetting::orderBy('seq','asc')->get();
            $datas->text_approve = '';
            foreach ($sets as $set) {
                if ($datas->text_approve != "") {
                    break;
                }
                $set_history = AssetDestructionSetHistory::where('asset_destruction_id',$datas->id)->where('setting_id',$set->id)->count();
                if (!$set_history) {
                    $datas->text_approve = 'Waiting approve by '.$set->role;
                }
            }

            $datas->checkbox = "";
        }

        return response()->json(['data' => $data]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create(Request $request)
    {
        $cek_set = $this->checkApproveSetting();
        if (!$cek_set['status']) {
            return response()->json(['success' => $cek_set['status'], 'message' => $cek_set['message']], 500);
        }

        $location = Location::orderBy('id','desc')->get();
        if (AssetDestruction::latest()->first()) {
            $last_get = AssetDestruction::latest()->first()->id + 1;
            $last_id = str_pad($last_get,3,'0',STR_PAD_LEFT);
        }else{
            $last_id = '001';
        }
        $no_dokumen = 'LJR-FRM-SDC-'.$last_id;

        $items = [];
        if ($request->has('id_location')) {
            $items = LocStock::with('stock')->where('location_id',$request->get('id_location'))->get();
        }

        $item_detail = [];
        if ($request->has('id_item')) {
            $item_detail = LocStock::with('stock')->find($request->get('id_item'));
        }

        $location_detail = [];
        if ($request->has('id_location')) {
            $location_detail = Location::find($request->get('id_location'));
        }

        return response()->json(['location' => $location, 'no_dokumen' => $no_dokumen, 'items' => $items, 'item_detail' => $item_detail, 'location_detail' => $location_detail]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        try {
            $cek_set = $this->checkApproveSetting();
            if (!$cek_set['status']) {
                return response()->json(['success' => $cek_set['status'], 'message' => $cek_set['message']], 500);
            }

            $validator = Validator::make($request->all(), [
                'document_code' => 'required',
                'no_rev' => 'required',
                'date_set' => 'required',
                'date_effective' => 'required',
                'destruction' => 'required',
            ]);
    
            if ($validator->fails()) {
                return response()->json($validator->errors(), 500);
            }

            $data = new AssetDestruction;
            $data->document_code = $request->input('document_code');
            $data->no_rev = $request->input('no_rev');
            $data->date_set = $request->input('date_set');
            $data->date_effective = $request->input('date_effective');
            $data->requester = Auth::user()->id;
            $data->save();

            $destruction = $request->input('destruction');
            if (is_array($destruction)) {
                foreach ($destruction as $key => $item) {
                    $detail = new AssetDestructionItem;
                    $detail->asset_destruction_id = $data->id;
                    $detail->locstock_id = $item['location_id'];
                    $detail->quantity = $item['item_qty'];
                    $detail->destruction_method = $item['destruction_method'];
                    $detail->user_responsible = Auth::user()->id;
                    $detail->start_date = $item['date_start'];
                    $detail->end_date = $item['date_end'];
                    $detail->save();

                    $locstock = LocStock::find($item['item_id']);
                    if ($locstock) {
                        $locstock->quantity = $locstock->quantity - $item['item_qty'];
                        $locstock->save();
                    }
                }
            }

            return response()->json(['success' => true, 'message' => 'Berhasil create data']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit(Request $request, $id)
    {
        $cek_set = $this->checkApproveSetting();
        if (!$cek_set['status']) {
            return response()->json(['success' => $cek_set['status'], 'message' => $cek_set['message']], 500);
        }

        $asset = AssetDestruction::find($id);
        $asset_item = AssetDestructionItem::with(['locStock.location','locStock.stock','user'])->where('asset_destruction_id',$id)->get();
        $location = Location::orderBy('id','desc')->get();
        if (AssetDestruction::latest()->first()) {
            $last_get = AssetDestruction::latest()->first()->id + 1;
            $last_id = str_pad($last_get,3,'0');
        }else{
            $last_id = '001';
        }
        $no_dokumen = 'LJR-FRM-SDC-'.$last_id;

        $items = [];
        if ($request->has('id_location')) {
            $items = LocStock::with('stock')->where('location_id',$request->get('id_location'))->get();
        }

        $item_detail = [];
        if ($request->has('id_item')) {
            $item_detail = LocStock::with('stock')->find($request->get('id_item'));
        }

        $location_detail = [];
        if ($request->has('id_location')) {
            $location_detail = Location::find($request->get('id_location'));
        }

        return response()->json(['location' => $location, 'no_dokumen' => $no_dokumen, 'items' => $items, 'item_detail' => $item_detail, 'location_detail' => $location_detail, 'asset' =>$asset, 'asset_item' => $asset_item]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        try {
            $cek_set = $this->checkApproveSetting();
            if (!$cek_set['status']) {
                return response()->json(['success' => $cek_set['status'], 'message' => $cek_set['message']], 500);
            }

            $validator = Validator::make($request->all(), [
                'document_code' => 'required',
                'no_rev' => 'required',
                'date_set' => 'required',
                'date_effective' => 'required',
                'destruction' => 'required',
            ]);
    
            if ($validator->fails()) {
                return response()->json($validator->errors(), 500);
            }

            $data = AssetDestruction::find($id);
            $data->document_code = $request->input('document_code');
            $data->no_rev = $request->input('no_rev');
            $data->date_set = $request->input('date_set');
            $data->date_effective = $request->input('date_effective');
            $data->requester = Auth::user()->id;
            $data->save();

            $destruction = $request->input('destruction');
            if (is_array($destruction)) {
                $id_delete = [];
                foreach ($destruction as $key => $item) {
                    if (isset($item['id'])) {
                        $detail = AssetDestructionItem::find($item['id']);
                    }else{
                        $detail = [];
                    }
                    if ($detail && isset($item['id'])) {
                        $locstock = LocStock::find($item['item_id']);
                        if ($locstock) {
                            if ($detail->quantity != $item['item_qty']) {
                                $quantity_edit = $detail->quantity + $item['item_qty'];

                                $locstock->quantity = $locstock->quantity + $quantity_edit;
                                $locstock->save();
                            }
                        }
                        $detail->asset_destruction_id = $data->id;
                        $detail->locstock_id = $item['location_id'];
                        $detail->quantity = $item['item_qty'];
                        $detail->destruction_method = $item['destruction_method'];
                        $detail->start_date = $item['date_start'];
                        $detail->end_date = $item['date_end'];
                        $detail->save();

                        $id_delete[] = $detail->id;
                    }else{
                        $detail = new AssetDestructionItem;
                        $detail->asset_destruction_id = $data->id;
                        $detail->locstock_id = $item['location_id'];
                        $detail->quantity = $item['item_qty'];
                        $detail->destruction_method = $item['destruction_method'];
                        $detail->user_responsible = Auth::user()->id;
                        $detail->start_date = $item['date_start'];
                        $detail->end_date = $item['date_end'];
                        $detail->save();
                        $id_delete[] = $detail->id;

                        $locstock = LocStock::find($item['item_id']);
                        if ($locstock) {
                            $locstock->quantity = $locstock->quantity - $item['item_qty'];
                            $locstock->save();
                        }
                    }

                    $delete_items = AssetDestructionItem::whereNotIn('id',$id_delete)->get();
                    foreach ($delete_items as $key => $delete) {
                        $locstock = LocStock::find($delete->item_id);
                        if ($locstock) {
                            $locstock->quantity = $locstock->quantity + $delete->item_qty;
                            $locstock->save();
                        }
                        $delete_now = AssetDestructionItem::find($delete->id)->delete();
                    }
                }
            }

            return response()->json(['success' => true, 'message' => 'Berhasil update data']);
        } catch (\Exception $e) {
            Log::info($e);
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        try{
            $data = AssetDestruction::find($id);
            if ($data) {
                $delete_items = AssetDestructionItem::where('asset_destruction_id',$id)->get();
                foreach ($delete_items as $key => $delete) {
                    $locstock = LocStock::find($delete->item_id);
                    if ($locstock) {
                        $locstock->quantity = $locstock->quantity + $delete->item_qty;
                        $locstock->save();
                    }
                    $delete_now = AssetDestructionItem::find($delete->id)->delete();
                }

                $data->delete();
            }

            return response()->json(['success' => true, 'message' => 'Berhasil delete']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    public function approve(Request $request)
    {
        try{
            $data = $request->input('data');
            $message_error = [];
            if (is_array($data)) {
                foreach ($data as $key => $approve) {
                    if (isset($approve['checkbox'])) {
                        $update = AssetDestruction::find($approve['id']);

                        //check approve
                        $sets = AssetDestructionSetting::orderBy('seq','asc')->get();
                        foreach ($sets as $set) {
                            $set_history = AssetDestructionSetHistory::where('asset_destruction_id',$update->id)->where('setting_id',$set->id)->count();

                            if (!$set_history) {
                                if ($set->role == Auth::user()->role) {
                                    $insert_history = AssetDestructionSetHistory::create(['setting_id' => $set->id, 'asset_destruction_id' => $update->id]);
                                    break;
                                }else{
                                    $message_error[] = $update->document_code.' - Anda tidak dapat akses untuk approve data ini';
                                    break;
                                }
                            }
                        }

                        $jum_set = AssetDestructionSetting::count();
                        $jum_set_history = AssetDestructionSetHistory::where('asset_destruction_id',$update->id)->count();

                        if ($jum_set == $jum_set_history) {
                            $update->authorised = 1;
                            $update->closed = 1;
                            $update->authorised_date = Carbon::today();
                            $update->save();
                            
                            $detail_update = AssetDestructionItem::where('asset_destruction_id',$approve['id'])->update(['completed' => 1, 'authorised' => 1]);
                        }
                    }
                }
            }

            if (count($message_error)) {
                return response()->json(['success' => false, 'message' => 'Anda tidak memiliki akses approve', 'error' => $message_error]); 
            }else{
                return response()->json(['success' => true, 'message' => 'Berhasil Approve']);
            }            
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    public function checkApproveSetting()
    {
        $return['status'] = true;
        $return['message'] = "";
        $jum_sett = AssetDestructionSetting::count();
        if ($jum_sett) {
            $data_cek = AssetDestructionSetting::where('role','Director')->count();
            if ($data_cek) {
                $return['status'] = true;
            }else{
                $return['status'] = false;
                $return['message'] = 'Wajib Set Approval Director di setting approval';
            }
        }else{
            $return['status'] = false;
            $return['message'] = 'Wajib isi approval setting dahulu';
        }

        return $return;
    }
}
