<?php

namespace App\Http\Controllers\GA;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\GA\ComparativeQuotation;
use App\Models\GA\ComparativeQuotationItem;
use App\Models\GA\StockRequest;
use App\Models\GA\StockRequestItems;
use App\Models\GA\PurchaseOrder;
use App\Models\GA\PurchaseOrderPayment;
use App\Models\GA\PurchaseOrderDetail;
use App\Models\GA\PurchaseOrderSetting;
use App\Models\GA\PurchaseOrderApprovalHistory;
use App\Models\GA\PurchaseOrderSetHistory;
use App\Models\GA\Grns;
use App\Models\GA\StockMove;
use App\Models\Fleet\LocStock;
use App\Models\Fleet\Supplier;
use App\Models\Fleet\Location;
use App\Models\Fleet\Shipper;
use App\Models\Fleet\PaymentTerm;
use App\Models\Fleet\ChartMaster;
use App\Models\Fleet\FixedAsset;
use App\Models\Fleet\Asset;
use App\Models\Fleet\StockMaster;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use DB;
use Auth;

class PurchaseOrderPaymentController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $setting = PurchaseOrderSetting::where('role',Auth::user()->role)->first();
        if (!$setting) {
            return response()->json(['success' => false, 'message' => 'You are not authorise for this menu'], 500);
        }

        $receive = PurchaseOrder::with(['ComparativeQuotation.purchaseRequest','shipper','user','supplier','location','PaymentTerm', 'detail.stock', 'detail.chartMaster', 'detail.fixedAsset','payment'])
        ->withCount([
            'detail AS total' => function ($query) {
                $query->select(DB::raw("sum(unit_price * qty_invoiced)"));
            },
            'payment AS dibayar' => function ($query) {
                $query->select(DB::raw("sum(total)"));
            }
        ])
        ->orderBy('id','desc')
        ->get();

        $total_dibayar = 0;
        $total_all = 0;
        foreach ($receive as $key => $data) {
            $total_dibayar += $data->dibayar;
            $total_all += $data->total;
        }

        $total_belum_bayar = $total_all - $total_dibayar;

        return response()->json(['receive' => $receive, 'total_dibayar' => $total_dibayar, 'total_belum_bayar' => $total_belum_bayar]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'po_id' => 'required',
                'total' => 'required',
                'bank_act' => 'required',
            ]);
    
            if ($validator->fails()) {
                return response()->json($validator->errors(), 500);
            }

            $po = PurchaseOrder::find($request->input('po_id'));

            $data = new PurchaseOrderPayment;
            $data->po_id = $request->input('po_id');
            $data->total = $request->input('total');
            $data->supplier_id = $po->supplier_no;
            $data->bank_act = $request->input('bank_act');
            $data->date_payment = Carbon::today();
            $data->note = $request->input('note');
            $data->created_by = Auth::user()->id;
            if ($data->save()) {
                $check_po = PurchaseOrder::with(['payment.po.supplier','supplier','detail'])
                ->where('id',$po->id)
                ->withCount([
                    'detail AS total' => function ($query) {
                        $query->select(DB::raw("sum(unit_price * qty_invoiced)"));
                    },
                    'payment AS dibayar' => function ($query) {
                        $query->select(DB::raw("sum(total)"));
                    }
                ])
                ->first();

                if ($check_po->dibayar >= $check_po->total) {
                    $update_po = PurchaseOrder::where('id',$po->id)->update(['status_payment' => 1]);
                }elseif ($check_po->dibayar > 0) {
                    $update_po = PurchaseOrder::where('id',$po->id)->update(['status_payment' => 2]);
                }else{
                    $update_po = PurchaseOrder::where('id',$po->id)->update(['status_payment' => 0]);
                }
            }

            return response()->json(['success' => true, 'message' => 'Berhasil create data']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $data = PurchaseOrder::with(['payment.po.supplier','supplier','detail'])
        ->where('id',$id)
        ->withCount([
            'detail AS total' => function ($query) {
                $query->select(DB::raw("sum(unit_price * qty_invoiced)"));
            },
            'payment AS dibayar' => function ($query) {
                $query->select(DB::raw("sum(total)"));
            }
        ])
        ->first();

        $total = (int)$data->total;
        $total_dibayar = PurchaseOrderPayment::where('po_id',$id)->sum('total');
        $total_sisa = $total - $total_dibayar;

        return response()->json(['data' => $data, 'total' => $total, 'total_dibayar' => $total_dibayar, 'total_sisa' => $total_sisa]);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        try{
            $payment = PurchaseOrderPayment::find($id);
            $po_id = $payment->po_id;
            if ($payment->delete()) {
                $check_po = PurchaseOrder::with(['payment.po.supplier','supplier','detail'])
                ->where('id',$po_id)
                ->withCount([
                    'detail AS total' => function ($query) {
                        $query->select(DB::raw("sum(unit_price * qty_invoiced)"));
                    },
                    'payment AS dibayar' => function ($query) {
                        $query->select(DB::raw("sum(total)"));
                    }
                ])
                ->first();

                if ($check_po->dibayar >= $check_po->total) {
                    $update_po = PurchaseOrder::where('id',$po_id)->update(['status_payment' => 1]);
                }elseif ($check_po->dibayar > 0) {
                    $update_po = PurchaseOrder::where('id',$po_id)->update(['status_payment' => 2]);
                }else{
                    $update_po = PurchaseOrder::where('id',$po_id)->update(['status_payment' => 0]);
                }
            }

            return response()->json(['success' => true, 'message' => 'Berhasil deleted data']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }
}
