<?php

namespace App\Http\Controllers\GA;

use App\Http\Controllers\Controller;
use App\Http\Helper;
use App\Models\Fleet\PurchaseOrder;
use App\Models\Fleet\RequestFormDetail;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class GoodReceivedController extends Controller
{
    public function index(Request $request)
    {
        if ($request->has('start_date')) {
            $start_date = Carbon::parse($request->start_date)->format('Y-m-d').' 00:00:00';
        }else{
            $start_date = Carbon::today()->format('Y-m-d').' 00:00:00';
        }
        if ($request->has('to_date')) {
            $to_date = Carbon::parse($request->to_date)->format('Y-m-d').' 23:59:00';
        }else{
            $to_date = Carbon::today()->format('Y-m-d').' 23:59:00';
        }
        $data['start_date'] = $start_date;
        $data['to_date'] = $to_date;

        
        if ($request->get('date-range') == 1 || $request->has('start_date')) {
            $data = PurchaseOrder::with(['company', 'department', 'location', 'user'])
            ->where(function($query) use($request){
                if ($request->get('po-gr') == 1) {
                    $query->where('approve_po', 2)->whereNull('doc_gr');
                }else if ($request->get('pr-gr') == 1) {
                    $query->where('approve_pr', '>=', 2)->whereNull('doc_gr');
                }else if ($request->get('list-gr') == 1) {
                    $query->whereNotNull('doc_gr');
                }

                if ($request->get('company')) {
                    $query->where('company_id', $request->get('company'));
                }
                if ($request->get('location')) {
                    $query->where('location_id', $request->get('location'));
                }
                if ($request->get('department')) {
                    $query->where('department_id', $request->get('department'));
                }
                if ($request->get('type_submission')) {
                    $query->where('type_submission', $request->get('type_submission'));
                }
            })
            // ->whereBetween('created_at', [$start_date,$to_date])
            ->orderBy('id', 'desc')
            ->get();
        }else{
            $data = PurchaseOrder::with(['company', 'department', 'location', 'user'])
            ->where(function($query) use($request){
                if ($request->get('po-gr') == 1) {
                    $query->where('approve_po', 2)->whereNull('doc_gr');
                }else if ($request->get('pr-gr') == 2) {
                    $query->where('approve_pr', 2)->whereNotNull('doc_gr');
                }

                if ($request->get('company')) {
                    $query->where('company_id', $request->get('company'));
                }
                if ($request->get('location')) {
                    $query->where('location_id', $request->get('location'));
                }
                if ($request->get('department')) {
                    $query->where('department_id', $request->get('department'));
                }
                if ($request->get('type_submission')) {
                    $query->where('type_submission', $request->get('type_submission'));
                }
            })
            ->orderBy('id', 'desc')
            ->get();
        }

        foreach ($data as $key => $value) {
            $value['company_name'] = $value->company->name ?? null;
            $value['department_name'] = $value->department->name ?? null;
            $value['location_name'] = $value->location->name ?? null;
            $value['supplier_name'] = $value->supplier->name ?? null;
            $value['uid'] = Auth::user()->id;

            if ($value->type == 'Transfer'){
                $value['no_pr'] = $value->no_tf;
                $value['no_seri'] = $value->no_tf;
            }else if($value->type == 'Cash Advance'){
                $value['no_pr'] = $value->no_ca;
                $value['no_seri'] = $value->no_ca;
            }else if($value->type == 'Virtual Account'){
                $value['no_pr'] = $value->no_va;
                $value['no_seri'] = $value->no_va;
            }
            if($value->type == 'PO'){
                $value['no_seri'] = $value->no_po;
            }
        }

        return response()->json($data);
    }

    public function uploadDoc(Request $request, $id)
    {
        $data = $request->all();

        if ($request->doc_gr) {
            $dataPO['doc_gr'] = Helper::saveFile($request->doc_gr, 'doc_gr');
            $dataPO['no_gr'] = Helper::genCode($data['company_id'], $data['department_id'], $data['location_id'], 'GR') ?? 'GR';
            
            $update = PurchaseOrder::find($id)->update($dataPO);

            foreach ($data['items'] as $key => $value) {
                $rfd = RequestFormDetail::find($value['id']);
                $rfd->qty_gr = $value->qty_gr;
                $rfd->qty_in = $value->qty_in;
                $rfd->outstanding = $value->outstanding;
                $rfd->save();
            }

            return response()->json(['status' => true, 'message' => 'Good Receive sudah tersimpan di system dengan No : ', 'no_gr' => $dataPO['no_gr']]);
        }else{
            return response()->json(['status' => true, 'message' => 'Upload dokumen anda terlebih dahulu']);
        }

    }
}
