<?php

namespace App\Http\Controllers\GA;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\GA\StockRequest;
use App\Models\GA\StockRequestItems;
use App\Models\GA\StockRequestSetting;
use App\Models\GA\StockRequestApprovalHistory;
use App\Models\GA\StockRequestSetHistory;
use App\Models\Fleet\Asset;
use App\Models\Fleet\Department;
use App\Models\Fleet\StockCategory;
use App\Models\Fleet\StockMaster;
use App\Models\Fleet\Supplier;
use App\Models\Fleet\Location;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use Auth;

class PRApprovalSettingController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $pr_item_null = StockRequestItems::whereNull('stock_id')->pluck('dispatch_id','dispatch_id');
        $data = StockRequest::with('department','category','items','history')
        // ->where('authorised',0)
        // ->where('budget_authorised',1)
        ->whereNotIn('id',$pr_item_null)
        ->orderBy('id','desc')
        ->get();
        foreach ($data as $key => $datas) {
            //check item select all
            // $select_all = $datas->items->whereNull('stock_id')->count();
            // if ($select_all) {
            //     unset($data[$key]);
            // }
            //check approve
            $sets = StockRequestSetting::orderBy('seq','asc')->get();
            $datas->text_approve = '';
            foreach ($sets as $set) {
                if ($datas->text_approve != "") {
                    break;
                }
                $set_history = StockRequestSetHistory::where('stock_request_id',$datas->id)->where('setting_id',$set->id)->count();
                if (!$set_history) {
                    if ($datas->authorised == 0) {
                        $datas->text_approve = 'Waiting approve by '.$set->role;
                    }elseif($datas->authorised == 1){
                        $datas->text_approve = 'Approve by '.$set->role;
                    }else{
                        $datas->text_approve = 'Not Approve waiting '.$set->role;
                    }
                }
            }

            if ($datas->text_approve == "") {
                if ($datas->authorised == 0) {
                    $datas->text_approve = 'Waiting approve';
                }elseif($datas->authorised == 1){
                    $datas->text_approve = 'Approve';
                }else{
                    $datas->text_approve = 'Not Approve';
                }
            }

            $totalAll = 0;
            $item = StockRequestItems::where('dispatch_id', $datas->id)->get();
            foreach ($item as $items) {
                $totalAll += $items->quantity * $items->price;
            }
            $datas->totalAll = $totalAll;
        }

        return response()->json(['data' => $data]);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $data = StockRequest::with('history.user.role')->find($id);
        $request = StockRequestItems::with('item')->where('dispatch_id',$id)->get();
        $department = Department::all();
        $category = StockCategory::all();
        $master = StockMaster::all();
        $supplier = Supplier::all();
        $location = Location::all();

        return response()->json(['department' => $department, 'category' => $category, 'master' => $master, 'data' => $data, 'request' => $request, 'supplier' => $supplier, 'location' => $location]);
    }
    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        try {
            //check approve
            $sets = StockRequestSetting::orderBy('seq','asc')->get();
            $update = StockRequest::find($id);

            $message_error = "";
            $stock_request_setting_id = 0;
            $status_error = false;
            foreach ($sets as $set) {
                $set_history = StockRequestSetHistory::where('stock_request_id',$id)->where('setting_id',$set->id)->count();

                if (!$set_history) {
                    if ($set->role == Auth::user()->role) {
                        $del_history = StockRequestSetHistory::where('stock_request_id',$id)->where('setting_id',$set->id)->delete();

                        $insert_history = StockRequestSetHistory::create(['setting_id' => $set->id, 'stock_request_id' => $id]);
                        $stock_request_setting_id = $set->id;
                        break;
                    }else{
                        $message_error = $update->code.' - Anda tidak dapat akses untuk approve data ini';
                        $status_error = true;
                        break;
                    }
                }
            }

            if (!$status_error) {
                if ($request->input('authorised') == 2) {
                    $update = StockRequest::find($id);
                    $update->authorised = $request->input('authorised');
                    $update->supplier_rec = $request->input('supplier_rec');
                    $update->save();
                }

                //suplier rec
                $update = StockRequest::find($id);
                $update->supplier_rec = $request->input('supplier_rec');
                $update->save();

                $log = new StockRequestApprovalHistory;
                $log->stockrequest_id = $id;
                $log->date = Carbon::today();
                $log->approved_by = Auth::user()->id;
                $log->remark = $request->input('description');
                $log->stock_request_setting_id = $stock_request_setting_id;
                $log->authorised = $request->input('authorised');
                $log->save();

                if(is_array($request->input('request'))){
                    $item = $request->input('request');
                    for ($i=0; $i < count($item); $i++) { 
                        // if (isset($item[$i]['completed']) && $item[$i]['completed'] == 1) {
                            $detail = StockRequestItems::find($item[$i]['id']);
                            $detail->price = $item[$i]['price'];
                            $detail->quantity = $item[$i]['quantity'];
                            $detail->authorised = $item[$i]['authorised'];
                            if ((int)$item[$i]['quantity'] <= (int)$item[$i]['qty_ready']) {
                                $detail->completed = 1;
                            }else{
                                $detail->completed = 0;
                            }
                            // $detail->cancel_id = Auth::user()->id;
                            // $detail->cancel_date = Carbon::today();
                            $detail->save();
                        // }
                    }
                }

                $jum_set = StockRequestSetting::count();
                $jum_set_history = StockRequestSetHistory::where('stock_request_id',$id)->count();
                if ($jum_set == $jum_set_history) {
                    $update = StockRequest::find($id);
                    $update->authorised = $request->input('authorised');
                    $update->authorised_date = Carbon::now();
                    $update->save();

                    //check stock instock
                    $item_completed_sum = StockRequestItems::where('dispatch_id',$id)->where('completed',1)->sum('qty_ready');
                    $item_completed_pluck = StockRequestItems::where('dispatch_id',$id)->where('completed',1)->pluck('stock_id','stock_id');
                    $asset_ready_count = Asset::whereIn('stock_id',$item_completed_pluck)->where('in_stock',0)->count();
                    if ($asset_ready_count >= $item_completed_sum) {
                        $asset_ready_update = Asset::whereIn('stock_id',$item_completed_pluck)->where('in_stock',0)->skip(0)->take(10)->update(['pr_id' => $id, 'in_stock' => 1]);
                    }else{
                        $item_completed_update = StockRequestItems::where('dispatch_id',$id)->where('completed',1)->update(['completed' => 0]);
                    }

                    //check ready pickup
                    $item_all = StockRequestItems::where('dispatch_id',$id)->count();
                    $item_completed = StockRequestItems::where('dispatch_id',$id)->where('completed',1)->count();
                    $item_no_completed = StockRequestItems::where('dispatch_id',$id)->where('completed',0)->count();

                    if ($item_all == $item_completed) {
                        $update_pr = StockRequest::find($id);
                        $update_pr->pick_up = 1;
                        $update_pr->save();
                    }elseif ($item_all != $item_no_completed && $item_no_completed > 0) {
                        $update_pr = StockRequest::find($id);
                        $update_pr->pick_up = 2;
                        $update_pr->save();
                    }elseif($item_all == $item_no_completed){
                        $update_pr = StockRequest::find($id);
                        $update_pr->pick_up = 0;
                        $update_pr->save();
                    }
                }
            }

            // $checkCancel = StockRequestItems::where('dispatch_id',$id)->where('completed',0)->count();
            // if ($checkCancel == 0) {
            //     $sr = StockRequest::find($id);
            //     $sr->authorised = 2;
            //     $sr->authorised_date = Carbon::now();
            // }
            
            if ($message_error) {
                return response()->json(['success' => false, 'message' => 'Anda tidak memiliki akses approve', 'error' => $message_error]); 
            }else{
                return response()->json(['success' => true, 'message' => 'Berhasil approve data']);
            }   
        } catch (\Exception $e) {
            report($e);
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }
}
