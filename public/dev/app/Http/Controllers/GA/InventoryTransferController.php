<?php

namespace App\Http\Controllers\GA;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Fleet\LocStock;
use App\Models\Fleet\Location;
use App\Models\Fleet\StockMaster;
use App\Models\GA\StockMove;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;

class InventoryTransferController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $stock_moves = StockMove::with(['type','location','stock'])
        ->where('hide_movt',0)
        ->orderBy('id','desc')
        ->get();

        return response()->json(['stock_moves' => $stock_moves]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create(Request $request)
    {
        $locstock = LocStock::where(function($query) use($request){
            if ($request->has('item_id')) {
                $query->where('stock_id',$request->get('item_id'));
            }
        })
        ->pluck('location_id');

        $location_from = Location::whereIn('id',$locstock)
        ->orderBy('id','desc')
        ->get();

        $location_to = Location::where(function($query) use($request){
            if ($request->has('location_from_id')) {
                $query->where('id','<>',$request->get('location_from_id'));
            }
        })
        ->orderBy('id','desc')
        ->get();

        $qty_avail = 0;
        if ($request->has('item_id') && $request->has('location_from_id')) {
            $check_qty = LocStock::where('stock_id',$request->get('item_id'))
            ->where('location_id',$request->get('location_from_id'))
            ->first();

            if ($check_qty) {
                $qty_avail = $check_qty->quantity;
            }
        }

        $items = StockMaster::orderBy('id','desc')->get();

        return response()->json(['location_from' => $location_from, 'location_to' => $location_to, 'items' => $items, 'qty_avail' => $qty_avail]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        try{
            $validator = Validator::make($request->all(), [
                'item' => 'required',
                'location_from' => 'required',
                'location_to' => 'required',
                'qty' => 'required',
            ]);
    
            if ($validator->fails()) {
                return response()->json($validator->errors(), 500);
            }

            $get_qty = LocStock::where('stock_id',$request->input('item'))
            ->where('location_id',$request->input('location_from'))
            ->first();

            $location_from = Location::find($request->input('location_from'));
            $location_to = Location::find($request->input('location_to'));
            
            $stockmove = new StockMove;
            $stockmove->stock_id = $request->input('item');
            $stockmove->type = 16;
            $stockmove->trans_no = 0;
            $stockmove->loc_code = $request->input('location_from');
            $stockmove->tran_date = Carbon::now();
            $stockmove->prd = 0;
            $stockmove->debtor_no = 0;
            $stockmove->price = 0;
            $stockmove->reference = 'To '.$location_from->name;
            $stockmove->qty = -$request->input('qty');
            $stockmove->new_qoh = $get_qty->quantity - $request->input('qty');
            $stockmove->save();

            $stockmove_add = new StockMove;
            $stockmove_add->stock_id = $request->input('item');
            $stockmove_add->type = 16;
            $stockmove_add->trans_no = 0;
            $stockmove_add->loc_code = $request->input('location_from');
            $stockmove_add->tran_date = Carbon::now();
            $stockmove_add->prd = 0;
            $stockmove_add->debtor_no = 0;
            $stockmove_add->price = 0;
            $stockmove_add->reference = 'To '.$location_from->name;
            $stockmove_add->qty = $request->input('qty');
            $stockmove_add->new_qoh = $get_qty->quantity + $request->input('qty');
            $stockmove_add->save();

            //add stock to new location
            $check_location_stock = LocStock::where('stock_id',$request->input('item'))
            ->where('location_id',$request->input('location_to'))
            ->first();

            if ($check_location_stock) {
                $update_stock = LocStock::find($check_location_stock->id);
                $update_stock->quantity = $update_stock->quantity + $request->input('qty');
                $update_stock->save();
            }else{
                $add_lockstock = new LocStock;
                $add_lockstock->location_id = $request->input('location_to');
                $add_lockstock->stock_id = $request->input('item');
                $add_lockstock->quantity = $request->input('qty');
                $add_lockstock->bin = '-';
                $add_lockstock->save();
            }

            //subtraction stock to old location
            $subtraction_location_stock = LocStock::where('stock_id',$request->input('item'))
            ->where('location_id',$request->input('location_from'))
            ->first();

            if ($subtraction_location_stock) {
                $subtraction_location_stock->quantity = $subtraction_location_stock->quantity - $request->input('qty');
                $subtraction_location_stock->save();
            }

            return response()->json(['success' => true, 'message' => 'Berhasil Transfer Stock Data']);
        } catch (\Exception $e) {
            Log::info($e);
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}
