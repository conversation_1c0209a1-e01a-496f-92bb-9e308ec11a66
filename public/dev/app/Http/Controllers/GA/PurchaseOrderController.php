<?php

namespace App\Http\Controllers\GA;

use App\Http\Controllers\Controller;
use App\Http\Helper;
use Illuminate\Http\Request;
use App\Models\GA\ComparativeQuotation;
use App\Models\GA\ComparativeQuotationItem;
use App\Models\GA\StockRequest;
use App\Models\GA\StockRequestItems;
use App\Models\GA\PurchaseOrder;
use App\Models\GA\PurchaseOrderDetail;
use App\Models\GA\PurchaseOrderSetting;
use App\Models\GA\PurchaseOrderApprovalHistory;
use App\Models\GA\PurchaseOrderSetHistory;
use App\Models\GA\Grns;
use App\Models\GA\StockMove;
use App\Models\Fleet\LocStock;
use App\Models\Fleet\Supplier;
use App\Models\Fleet\Location;
use App\Models\Fleet\Shipper;
use App\Models\Fleet\PaymentTerm;
use App\Models\Fleet\ChartMaster;
use App\Models\Fleet\FixedAsset;
use App\Models\Fleet\Asset;
use App\Models\Fleet\Deliver;
use App\Models\Fleet\Department;
use App\Models\Fleet\InvoiceAddress;
use App\Models\Fleet\LogApproval;
use App\Models\Fleet\PurchaseOrder as FleetPurchaseOrder;
use App\Models\Fleet\PurchaseOrderDetail as FleetPurchaseOrderDetail;
use App\Models\Fleet\RequestForm;
use App\Models\Fleet\RequestFormDetail;
use App\Models\Fleet\StockMaster;
use App\Models\Fleet\Tax;
use App\Models\Fleet\Top;
use App\Models\Fleet\TypeItem;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;

class PurchaseOrderController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        if ($request->has('start_date')) {
            $start_date = Carbon::parse($request->start_date)->format('Y-m-d').' 00:00:00';
        }else{
            $start_date = Carbon::today()->format('Y-m-d').' 00:00:00';
        }
        if ($request->has('to_date')) {
            $to_date = Carbon::parse($request->to_date)->format('Y-m-d').' 23:59:00';
        }else{
            $to_date = Carbon::today()->format('Y-m-d').' 23:59:00';
        }
        $data['start_date'] = $start_date;
        $data['to_date'] = $to_date;

        
        if ($request->get('date-range') == 1 || $request->has('start_date')) {
            $data = FleetPurchaseOrder::with(['company', 'department', 'location', 'user'])
            ->where(function($query) use($request){
                if ($request->get('approve_po') == 1) {
                    $query->where('approve_po', 1);
                }else if ($request->get('approve_po') == 2) {
                    $query->where('approve_po', 2);
                }else if ($request->get('approve_pr') == 1) {
                    $query->where('approve_pr', 1);
                }else if ($request->get('approve_pr') == 2) {
                    $query->where('approve_pr', 2);
                }else if ($request->get('approve_pr') == 3) {
                    $query->where('approve_pr', 3);
                }else if($request->get('po-psc')){
                    $query->where('type', 'PO')->whereNull('approve_po');
                }else if($request->get('pr-psc')){
                    $query->where('type', '!=', 'PO')->whereNull('approve_pr');
                }

                if ($request->get('company')) {
                    $query->where('company_id', $request->get('company'));
                }
                if ($request->get('location')) {
                    $query->where('location_id', $request->get('location'));
                }
                if ($request->get('department')) {
                    $query->where('department_id', $request->get('department'));
                }
                if ($request->get('type_submission')) {
                    $query->where('type_submission', $request->get('type_submission'));
                }
            })
            // ->whereBetween('created_at', [$start_date,$to_date])
            ->orderBy('id', 'desc')
            ->get();
        }else{
            $data = FleetPurchaseOrder::with(['company', 'department', 'location', 'user'])
            ->where(function($query) use($request){
                if ($request->get('approve_po') == 1) {
                    $query->where('approve_po', 1);
                }else if ($request->get('approve_po') == 2) {
                    $query->where('approve_po', 2);
                }else if ($request->get('approve_pr') == 1) {
                    $query->where('approve_po', 1);
                }else if ($request->get('approve_pr') == 2) {
                    $query->where('approve_po', 2);
                }else if($request->get('po-psc')){
                    $query->where('type', 'PO')->whereNull('approve_po');
                }else if($request->get('pr-psc')){
                    $query->where('type', '!=', 'PO')->whereNull('approve_pr');
                }

                if ($request->get('company')) {
                    $query->where('company_id', $request->get('company'));
                }
                if ($request->get('location')) {
                    $query->where('location_id', $request->get('location'));
                }
                if ($request->get('department')) {
                    $query->where('department_id', $request->get('department'));
                }
                if ($request->get('type_submission')) {
                    $query->where('type_submission', $request->get('type_submission'));
                }
            })
            ->orderBy('id', 'desc')
            ->get();
        }

        foreach ($data as $key => $value) {
            $value['company_name'] = $value->company->name ?? null;
            $value['department_name'] = $value->department->name ?? null;
            $value['location_name'] = $value->location->name ?? null;
            $value['supplier_name'] = $value->supplier->name ?? null;
            $value['uid'] = Auth::user()->id;

            if ($value->type == 'Transfer'){
                $value['no_pr'] = $value->no_tf;
            }else if($value->type == 'Cash Advance'){
                $value['no_pr'] = $value->no_ca;
            }else if($value->type == 'Virtual Account'){
                $value['no_pr'] = $value->no_va;
            }
        }

        return response()->json($data);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $cek_set = $this->checkApproveSetting();
        if (!$cek_set['status']) {
            return response()->json(['success' => $cek_set['status'], 'message' => $cek_set['message']], 500);
        }

        $location = Location::all();
        $shipper = Shipper::all();
        $payment_term = PaymentTerm::all();
        $req = 'REQ-'.Carbon::now()->format('ymdHis').rand(10,99);

        return response()->json(['location' => $location, 'shipper' => $shipper, 'payment_term' => $payment_term, 'req' => $req]);
    }

    public function createPo($id,$supplier)
    {
        $cek_set = $this->checkApproveSetting();
        if (!$cek_set['status']) {
            return response()->json(['success' => $cek_set['status'], 'message' => $cek_set['message']], 500);
        }

        $cq = ComparativeQuotation::with('items.stock','details.supplier','termin','purchaseRequest')->findOrFail($id);
        $cqi = ComparativeQuotationItem::with('stock','supplier','ComparativeQuotation.termin','ComparativeQuotation.purchaseRequest','detail.supplier')->where('comparative_quotation_id',$id)->where('supplier_id',$supplier)->get();
        $location = Location::all();
        $shipper = Shipper::all();
        $payment_term = PaymentTerm::all();
        $req = 'REQ-'.Carbon::now()->format('ymdHis').rand(10,99);
        $chart_master = ChartMaster::orderBy('accountcode','asc')->get();
        $fixed_asset = FixedAsset::orderBy('id','asc')->get();

        return response()->json(['location' => $location, 'shipper' => $shipper, 'payment_term' => $payment_term, 'req' => $req, 'cqi' => $cqi, 'chart_master' => $chart_master, 'fixed_asset' => $fixed_asset, 'cq' => $cq]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request, $id, $supplier_id)
    {
        try {
            $cek_set = $this->checkApproveSetting();
            if (!$cek_set['status']) {
                return response()->json(['success' => $cek_set['status'], 'message' => $cek_set['message']], 500);
            }
            
            $validator = Validator::make($request->all(), [
                'exRate' => 'required',
                'requisition' => 'required',
                'location' => 'required',
                'shipper' => 'required',
                // 'comment' => 'required',
                // 'statusComment' => 'required',
                'deliveryDate' => 'required',
                'payment_term' => 'required',
            ]);
    
            if ($validator->fails()) {
                return response()->json($validator->errors(), 500);
            }

            $cq = ComparativeQuotation::with('items.stock','details.supplier','termin','purchaseRequest')->findOrFail($id);
            $location = Location::find($request->input('location'));
            $supplier = Supplier::find($supplier_id);

            $po = new PurchaseOrder;
            $po->supplier_no = $supplier->id;
            $po->comments = 'created';
            $po->ord_date = Carbon::now();
            $po->rate = $request->input('exRate');
            $po->allow_print = 1;
            $po->initiator = Auth::user()->id;
            $po->requisition_no = $request->input('requisition');
            $po->into_stock_location = $request->input('location');
            if ($location) {
                $po->tel = $location->tel;
                $po->contact = $location->contact;
            }
            $po->supp_tel = $supplier->telephone; 
            $po->supplier_contact = $supplier->pic_phone; 
            $po->version = 1; 
            $po->revised = Carbon::now();
            $po->real_order_no = 0;
            $po->delivery_by = $request->input('shipper');
            $po->delivery_date = $request->input('deliveryDate');
            $po->status = 'created';
            $po->stat_comment = Carbon::now()->format('m/d/Y').' - PO Created by '.Auth::user()->first_name;
            $po->payment_terms = $request->input('payment_term');
            $po->port = $supplier->port;
            $po->comparative_quotation_id = $id;
            $po->code = 'PO-'.Carbon::now()->format('ymdHis').rand(10,99);
            $po->authorise = 0;
            if($po->save()){
                if (is_array($request->input('items'))) {
                    $items = $request->input('items');
                    for ($i=0; $i < count($request->input('items')); $i++) { 
                        $detail = new PurchaseOrderDetail;
                        $detail->po_id = $po->id;
                        $detail->item_code = $items[$i]['stock_id'];
                        $detail->delivery_date = $request->input('deliveryDate');
                        $detail->item_description = $items[$i]['stock']['description'];
                        $detail->gl_code = $items[$i]['GLCode'];
                        $detail->qty_invoiced = $items[$i]['quantity'];
                        $detail->unit_price = $items[$i]['price'];
                        $detail->act_price = $items[$i]['price'];
                        $detail->std_cost_unit = $items[$i]['price'];
                        $detail->quantity_ord = $items[$i]['quantity'];
                        $detail->quantity_recd = 0;
                        $detail->shipt_ref = 0;
                        $detail->job_ref = 0;
                        $detail->completed = 0;
                        $detail->suppliers_unit = $items[$i]['uom'];
                        $detail->suppliers_part_no = 0;
                        if ($items[$i]['AssetID'] != 'Not an Asset') {
                            $detail->asset_id = $items[$i]['AssetID'];
                        }
                        $detail->save();

                        $cqi_update = ComparativeQuotationItem::find($items[$i]['id']);
                        $cqi_update->used = 1;
                        $cqi_update->save();
                    }
                }
            }

            return response()->json(['success' => true, 'message' => 'Berhasil create data']);
        } catch (\Exception $e) {
            Log::info($e);
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $data = FleetPurchaseOrder::find($id);
        $items = FleetPurchaseOrderDetail::with(['typeItem', 'requestFormDetail'])->where('purchase_order_id', $id)->get();

        $invoice_address = InvoiceAddress::orderBy('id', 'desc')->get();
        $deliver = Deliver::orderBy('id', 'desc')->get();
        $top = Top::orderBy('id', 'desc')->get();
        $supplier = Supplier::orderBy('id', 'desc')->get();
        $tax = Tax::orderBy('id', 'desc')->get();

        return response()->json(['data' => $data,
                                 'items' => $items,
                                 'invoice_address' => $invoice_address,
                                 'deliver' => $deliver,
                                 'top' => $top,
                                 'supplier' => $supplier,
                                 'tax' => $tax]);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    public function showPo($id)
    {
        $cek_set = $this->checkApproveSetting();
        if (!$cek_set['status']) {
            return response()->json(['success' => $cek_set['status'], 'message' => $cek_set['message']], 500);
        }

        $po = PurchaseOrder::with(['ComparativeQuotation','shipper','user','supplier','location','PaymentTerm', 'detail.stock', 'detail.chartMaster', 'detail.fixedAsset'])->findOrFail($id);

        return response()->json(['po' => $po]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request)
    {
        try {
            $setting = PurchaseOrderSetting::where('role',Auth::user()->role)->first();
            if (!$setting) {
                return response()->json(['success' => false, 'message' => 'You are not authorise for this menu'], 500);
            }

            $message_error = [];

            $data_po = $request->all();
            foreach ($data_po as $key => $datas) {
                if (isset($datas['checkbox'])) {
                    $id = $datas['checkbox'];
                    $update = PurchaseOrder::find($id);

                    //check approve
                    $sets = PurchaseOrderSetting::orderBy('seq','asc')->get();
                    foreach ($sets as $set) {
                        $set_history = PurchaseOrderSetHistory::where('purchase_order_id',$update->id)->where('setting_id',$set->id)->count();

                        if (!$set_history) {
                            if ($set->role == Auth::user()->role) {
                                $insert_history = PurchaseOrderSetHistory::create(['setting_id' => $set->id, 'purchase_order_id' => $update->id]);
                                break;
                            }else{
                                $message_error[] = $update->code.' - Anda tidak dapat akses untuk approve data ini';
                                break;
                            }
                        }
                    }

                    $jum_set = PurchaseOrderSetting::count();
                    $jum_set_history = PurchaseOrderSetHistory::where('purchase_order_id',$update->id)->count();

                    if ($jum_set == $jum_set_history) {
                        $update = PurchaseOrder::find($id);
                        $update->authorise = 1;
                        $update->authorise_date = Carbon::now();
                        $update->save();

                        $history = new PurchaseOrderApprovalHistory;
                        $history->purchase_order_id = $id;
                        $history->date = Carbon::now();
                        $history->approved_by = Auth::user()->id;
                        if (isset($datas['remark'])) {
                            $history->remark = $datas['remark'];
                        }
                        $history->purchase_order_setting_id = $setting->id;
                        $history->save();
                    }
                }
            }

            if (count($message_error)) {
                return response()->json(['success' => false, 'message' => 'Anda tidak memiliki akses approve', 'error' => $message_error]); 
            }else{
                return response()->json(['success' => true, 'message' => 'Berhasil Approve data']);
            }
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }

    public function receiveList()
    {
        $setting = PurchaseOrderSetting::where('role',Auth::user()->role)->first();
        if (!$setting) {
            return response()->json(['success' => false, 'message' => 'You are not authorise for this menu'], 500);
        }

        $receive = PurchaseOrder::with(['ComparativeQuotation.purchaseRequest','shipper','user','supplier','location','PaymentTerm', 'detail.stock', 'detail.chartMaster', 'detail.fixedAsset'])
        ->withCount([
            'detail AS total' => function ($query) {
                $query->select(DB::raw("sum(unit_price * qty_invoiced)"));
            }
        ])
        ->orderBy('id','desc')
        ->get();

        return response()->json(['receive' => $receive]);
    }

    public function receiveShow($id)
    {
        $setting = PurchaseOrderSetting::where('role',Auth::user()->role)->first();
        if (!$setting) {
            return response()->json(['success' => false, 'message' => 'You are not authorise for this menu'], 500);
        }

        $receive = PurchaseOrder::with(['ComparativeQuotation.purchaseRequest','ComparativeQuotation.details','shipper','user','supplier','location','PaymentTerm', 'detail.stock', 'detail.chartMaster', 'detail.fixedAsset'])
        ->withCount([
            'detail AS total' => function ($query) {
                $query->select(DB::raw("sum(unit_price * qty_invoiced)"));
            }
        ])
        ->find($id);

        return response()->json(['receive' => $receive]);
    }

    public function receiveStore(Request $request, $id)
    {
        try{
            $setting = PurchaseOrderSetting::where('role',Auth::user()->role)->first();
            if (!$setting) {
                return response()->json(['success' => false, 'message' => 'You are not authorise for this menu'], 500);
            }

            $details = $request->input('detail');
            if (is_array($details)) {
                foreach ($details as $key => $detail) { 
                    if (!isset($detail['receive_qty'])) {
                        $detail['receive_qty'] = 0;
                    }

                    //update detai po qty received
                    $podetail = PurchaseOrderDetail::find($detail['id']);

                    if ($detail['completed']) {
                        $detail['receive_qty'] = $podetail->quantity_ord - $podetail->quantity_recd;
                        $podetail->return_qty = 0;
                    }else{
                        $total_recieve_return = $detail['receive_qty'] + $detail['return_qty'] + $podetail->quantity_recd;
                        if ($total_recieve_return > $podetail->quantity_ord) {
                            return response()->json(['success' => false, 'message' => 'quantity receive dan return tidak sesuai'], 500);
                        }
                        $podetail->return_qty = $detail['return_qty'];
                    }

                    $total_received = $podetail->quantity_recd + $detail['receive_qty'];

                    if ($detail['completed'] || $total_received == $detail['qty_invoiced']) {
                        $podetail->completed = 1;
                        $podetail->quantity_recd = $detail['qty_invoiced'];
                    }else{
                        $podetail->quantity_recd = $podetail->quantity_recd + $detail['receive_qty'];
                    }
                    $podetail->save();


                    //add to table grns
                    $count_grns = Grns::where('po_detail_item', $detail['id'])->where('item_code', $detail['item_code'])->count();

                    $grns = new Grns;
                    $grns->grn_batch = $count_grns + 1;
                    $grns->item_code = $detail['item_code'];
                    $grns->delivery_date = $request->input('date_goods');
                    $grns->item_description = $detail['item_description'];
                    $grns->qty_recd = $detail['receive_qty'];
                    $grns->quantity_inv = $detail['qty_invoiced'];
                    $grns->supplier_id = $request->input('supplier_no');
                    $grns->std_cost_unit = $detail['std_cost_unit'];
                    $grns->save();

                    //update qty in table lockstock
                    $cek_lockstock = LocStock::where('location_id',$request->input('into_stock_location'))
                    ->where('stock_id',$detail['item_code'])
                    ->first();

                    if (!$cek_lockstock) {
                        $add_lockstock = new LocStock;
                        $add_lockstock->location_id = $request->input('into_stock_location');
                        $add_lockstock->stock_id = $detail['item_code'];
                        $add_lockstock->bin = '-';
                        $add_lockstock->save();
                        
                        $lockstock = LocStock::find($add_lockstock->id);
                    }else{
                        $lockstock = LocStock::find($cek_lockstock->id);
                    }
                    $lockstock->qty_pending = $lockstock->qty_pending + $detail['receive_qty'];
                    $lockstock->save();

                    //add histroy stock move
                    $stockmove = new StockMove;
                    $stockmove->stock_id = $detail['item_code'];
                    $stockmove->type = 25;
                    $stockmove->trans_no = $grns->grn_batch;
                    $stockmove->loc_code = $request->input('into_stock_location');
                    $stockmove->tran_date = $request->input('date_goods');
                    $stockmove->debtor_no = $request->input('supplier_no');
                    $stockmove->branch_code = '0';
                    $stockmove->price = $detail['unit_price'] / $request->input('rate');
                    $stockmove->prd = 0;
                    $stockmove->reference = $request->input('supplier_no').' ('.$request->input('supplier')['name'].') - '.$id;
                    $stockmove->qty = $detail['receive_qty'];
                    $stockmove->discount_percent = 0;
                    $stockmove->standard_cost = $detail['unit_price'];
                    $stockmove->new_qoh = $lockstock->quantity;
                    $stockmove->save();

                    //add to table asset
                    // $purchase_order = PurchaseOrder::find($id);
                    // if (is_numeric($detail['receive_qty'])) {
                    //     for ($i=0; $i < $detail['receive_qty']; $i++) { 
                    //         $stock_master = StockMaster::find($detail['item_code']);
                    //         if ($stock_master && $stock_master->category_id == 'CT001') {
                    //             $asset = new Asset;
                    //             $asset->code = 'LJR-'.Carbon::now()->format('ymdHis').rand(10,99);
                    //             $asset->created_asset = Carbon::now();
                    //             $asset->stock_id = $detail['item_code'];
                    //             $asset->created_by = Auth::user()->id;
                    //             $asset->requester = $purchase_order->ComparativeQuotation->requester;
                    //             $asset->save();
                    //         }
                    //     }
                    // }
                }
            }

            $detail_count_complete = PurchaseOrderDetail::where('po_id',$id)->where('completed',1)->count();
            $detail_count = PurchaseOrderDetail::where('po_id',$id)->count();
            if ($detail_count == $detail_count_complete) {
                $update_completed = PurchaseOrder::find($id);
                $update_completed->status = 'Completed';
                $update_completed->stat_comment = Carbon::now()->format('m/d/Y').' - Order Completed on entry of GRN';
                $update_completed->save();

                //get CQ
                if (isset($update_completed->ComparativeQuotation->id)) {
                    $cq_id = $update_completed->ComparativeQuotation->id;

                    $comparative = ComparativeQuotation::find($cq_id);
                    if ($comparative && $comparative->purchaseRequest->category_id != 9) {
                        $po_cq_all = PurchaseOrder::where('comparative_quotation_id',$comparative->id)->count();
                        $po_cq_completed = PurchaseOrder::where('comparative_quotation_id',$comparative->id)->where('status','Completed')->count();
                        
                        // if ($po_cq_all > 0) {
                        //     //update purchase request ready pickup
                        //     $purchase_update = StockRequest::find($comparative->stock_request_id);
                        //     $purchase_update->pick_up = 1;
                        //     $purchase_update->save();
                        // }
                        // elseif ($po_cq_all > 0) {
                        //     $purchase_update = StockRequest::find($comparative->stock_request_id);
                        //     $purchase_update->pick_up = 2;
                        //     $purchase_update->save();
                        // }
                    }
                }
            }

            // Log::info($detail_count.' === '.$detail_count_complete);
            return response()->json(['success' => true, 'message' => 'Berhasil Update data']);
        } catch (\Exception $e) {
            Log::info($e);
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    public function receiveLabel($id)
    {
        try {
            $setting = PurchaseOrderSetting::where('role',Auth::user()->role)->first();
            if (!$setting) {
                return response()->json(['success' => false, 'message' => 'You are not authorise for this menu'], 500);
            }

            $receive = PurchaseOrder::with(['ComparativeQuotation.purchaseRequest','ComparativeQuotation.details','shipper','user','supplier','location','PaymentTerm', 'detail.stock', 'detail.chartMaster', 'detail.fixedAsset'])
            ->withCount([
                'detail AS total' => function ($query) {
                    $query->select(DB::raw("sum(unit_price * qty_invoiced)"));
                }
            ])
            ->find($id);

            if ($receive->ComparativeQuotation->purchaseRequest->category_id != 9) {
                return response()->json(['success' => false, 'message' => 'Data Error'], 500);
            }

            $assets = [];
            $no = 0;

            $poassets = Asset::where('po_id',$id)->get();
            foreach ($poassets as $key => $poasset) {
                $assets[$no]['id'] = $poasset->stock_id;
                $assets[$no]['label'] = $poasset->code;
                $assets[$no]['description'] = $poasset->stock->description;
                $assets[$no]['long_description'] = $poasset->stock->long_description;
                $assets[$no]['units'] = $poasset->stock->units;
                $assets[$no]['saved'] = 1;
                $assets[$no]['show_qr'] = false;
                if ($poasset->pickup_date != "") {
                    $assets[$no]['pickup'] = true;
                }
                $no++;
            }

            $details = PurchaseOrderDetail::where('po_id',$id)->get();
            $poassets_count = Asset::where('po_id',$id)->count();
            foreach ($details as $key => $detail) {
                if ($detail->stock->category_id == 'CT001') {
                    for ($i=0; $i < ($detail->quantity_recd - $poassets_count); $i++) { 
                        $assets[$no]['id'] = $detail->item_code;
                        $assets[$no]['label'] = 'LJR-'.Carbon::now()->format('ymdHis').rand(10,99);
                        $assets[$no]['description'] = $detail->stock->description;
                        $assets[$no]['long_description'] = $detail->stock->long_description;
                        $assets[$no]['units'] = $detail->stock->units;
                        $assets[$no]['saved'] = 0;
                        $assets[$no]['show_qr'] = false;
                        $assets[$no]['pickup'] = false;
                        $no++;
                    }
                }
            }

            return response()->json(['receive' => $receive, 'assets' => $assets]);
        } catch (\Exception $e) {
            Log::info($e);
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    public function receiveLabelStore(Request $request, $id)
    {
        try {
            $setting = PurchaseOrderSetting::where('role',Auth::user()->role)->first();
            if (!$setting) {
                return response()->json(['success' => false, 'message' => 'You are not authorise for this menu'], 500);
            }

            $purchase_order = PurchaseOrder::find($id);
            $items = $request->input('items');

            if (is_array($items)) {
                $assetdel = Asset::where('po_id',$id)->delete();
                foreach ($items as $key => $item) {
                    $asset = new Asset;
                    $asset->po_id = $purchase_order->id;
                    $asset->pr_id = $purchase_order->ComparativeQuotation->purchaseRequest->id;
                    $asset->code = $item['label'];
                    $asset->created_asset = Carbon::now();
                    $asset->stock_id = $item['id'];
                    $asset->created_by = Auth::user()->id;
                    $asset->requester = $purchase_order->ComparativeQuotation->requester;
                    $asset->in_stock = 1;
                    $asset->save();
                }
            }

            //get CQ
            if (isset($purchase_order->ComparativeQuotation->id)) {
                $cq_id = $purchase_order->ComparativeQuotation->id;

                $comparative = ComparativeQuotation::find($cq_id);
                if ($comparative && $comparative->purchaseRequest->category_id == 9) {
                    $po_cq_asset_all = PurchaseOrder::where('comparative_quotation_id',$comparative->id)->pluck('id','id');
                    $total_asset_po = PurchaseOrderDetail::whereIn('po_id',$po_cq_asset_all)->sum('qty_invoiced');
                    $total_asset_label = Asset::whereIn('po_id',$po_cq_asset_all)->count();
                    
                    if ($total_asset_label > 0 && $total_asset_po == $total_asset_label) {
                        //update purchase request ready pickup
                        $purchase_update = StockRequest::find($comparative->stock_request_id);
                        $purchase_update->pick_up = 1;
                        $purchase_update->save();
                    }elseif ($total_asset_label > 0) {
                        $purchase_update = StockRequest::find($comparative->stock_request_id);
                        $purchase_update->pick_up = 2;
                        $purchase_update->save();
                    }
                }
            }

            return response()->json(['success' => true, 'message' => 'Berhasil Labeling Asset']);
        } catch (\Exception $e) {
            Log::info($e);
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    public function checkApproveSetting()
    {
        $return['status'] = true;
        $return['message'] = "";
        $jum_sett = PurchaseOrderSetting::count();
        if ($jum_sett) {
            $data_cek = PurchaseOrderSetting::where('role','Director')->count();
            if ($data_cek) {
                $return['status'] = true;
            }else{
                $return['status'] = false;
                $return['message'] = 'Wajib Set Approval Director di setting approval';
            }
        }else{
            $return['status'] = false;
            $return['message'] = 'Wajib isi approval setting dahulu';
        }

        return $return;
    }

    public function getListRfDetail(Request $request)
    {
        if ($request->has('start_date')) {
            $start_date = Carbon::parse($request->start_date)->format('Y-m-d').' 00:00:00';
        }else{
            $start_date = Carbon::today()->format('Y-m-d').' 00:00:00';
        }
        if ($request->has('to_date')) {
            $to_date = Carbon::parse($request->to_date)->format('Y-m-d').' 23:59:00';
        }else{
            $to_date = Carbon::today()->format('Y-m-d').' 23:59:00';
        }
        $data['start_date'] = $start_date;
        $data['to_date'] = $to_date;

        
        $rf_id = RequestForm::with(['company', 'department', 'location', 'workshop', 'user'])
        ->where('status_approval', 1)->where('verified', 1)->where('status_approval_pr_ga', 1)->where('status_approval_pr_direktur', 1)
        ->where(function($query) use($request){
            if ($request->get('company')) {
                $query->where('company_id', $request->get('company'));
            }
            if ($request->get('location')) {
                $query->where('location_id', $request->get('location'));
            }
            if ($request->get('department')) {
                $query->where('department_id', $request->get('department'));
            }
            if ($request->get('type_submission')) {
                $query->where('type_submission', $request->get('type_submission'));
            }
            if ($request->get('no_seri')) {
                $query->whereId($request->get('no_seri'));
            }
            if ($request->get('nopol')) {
                $query->where('nopol', $request->get('nopol'));
            }
        })
        // ->whereBetween('created_at', [$start_date,$to_date])
        ->orderBy('id', 'desc')
        ->pluck('id');

        $data = RequestFormDetail::with('requestForm')->whereNull('po_id')->whereIn('request_form_id', $rf_id)->orderBy('id', 'desc')->get();

        foreach ($data as $key => $value) {
            $value['no_pr'] = $value->requestForm->no_pr ?? null;
            $value['type_submission'] = $value->requestForm->type_submission ?? null;
            $value['company_name'] = $value->requestForm->company->name ?? null;
            $value['department_name'] = $value->requestForm->department->name ?? null;
            $value['location_name'] = $value->requestForm->location->name ?? null;
            $value['company_id'] = $value->requestForm->company_id ?? null;
            $value['department_id'] = $value->requestForm->department_id ?? null;
            $value['location_id'] = $value->requestForm->location_id ?? null;
            $value['category_item_name'] = $value->categoryItem->name ?? null;
            $value['type_item_name'] = $value->typeItem->name ?? null;
            $value['type_item_uom'] = $value->typeItem->uom ?? null;
            $value['no_seri'] = $value->requestForm->workshop->no_seri ?? null;
            $value['nopol'] = $value->requestForm->nopol ?? null;
            $value['po_qty'] = FleetPurchaseOrderDetail::where('type_item_id', $value->type_item_id)->count() ?? 0;
            $value['balance'] = $value['qty_item_approve'] - $value['po_qty'] - $value['qty_create'];
            $value['check'] = false;
        }

        return response()->json($data);
    }

    public function sessionItemList(Request $request)
    {
        $data = $request->all();

        $item_detail_id = '';
        $index = 0;
        foreach ($data as $key => $value) {
            if ($value['check'] == true) {
                $item_detail_id .= 'items['.$index.']='.$value['id'].'&';

                $rfd = RequestFormDetail::find($value['id']);
                $rfd->qty_create = $value['qty_create'];
                // $rfd->balance = $value->balance;
                $rfd->save();

                $index++;
            }
        }
        
        return response()->json($item_detail_id);
    }

    public function getSessionItemList(Request $request)
    {
        $rfd_id = $request->items;

        $items = RequestFormDetail::with(['categoryItem', 'typeItem'])->whereIn('id', $rfd_id)->orderBy('id', 'desc')->get();

        $data['sub_total'] = 0;
        foreach ($items as $key => $value) {
            $data['sub_total'] += $value->qty * $value->budget;
            $value['amount'] = $value->qty * $value->budget;

            if ($key == 0) {
                $rf = RequestForm::find($value->request_form_id);
                $data['department_id'] = $rf->department_id;
                $data['location_id'] = $rf->location_id;
                $data['company_id'] = $rf->company_id;
            }
        }
        
        $data['ppn'] = 0;
        $data['total'] = $data['sub_total'] + $data['ppn'];

        $invoice_address = InvoiceAddress::orderBy('id', 'desc')->get();
        $deliver = Deliver::orderBy('id', 'desc')->get();
        $top = Top::orderBy('id', 'desc')->get();
        $supplier = Supplier::orderBy('id', 'desc')->get();
        $tax = Tax::orderBy('id', 'desc')->get();

        return response()->json(['data' => $data,
                                 'items' => $items,
                                 'invoice_address' => $invoice_address,
                                 'deliver' => $deliver,
                                 'top' => $top,
                                 'supplier' => $supplier,
                                 'tax' => $tax]);
    }

    public function createdPO(Request $request)
    {
        $data = $request->all();

        $dataPO['user_id'] = Auth::user()->id;
        $dataPO['tnc'] = $data['tnc'] ?? null;
        $no_po = Helper::genCode($data['company_id'], $data['department_id'], $data['location_id'], 'PO');
        $dataPO['no_po'] = $no_po;
        if ($data['type'] == 'Transfer'){
            $dataPO['no_tf'] = Helper::genCode($data['company_id'], $data['department_id'], $data['location_id'], 'TF') ?? 'TF';
        }else if($data['type'] == 'Cash Advance'){
            $dataPO['no_ca'] = Helper::genCode($data['company_id'], $data['department_id'], $data['location_id'], 'CA') ?? 'CA';
        }else if($data['type'] == 'Virtual Account'){
            $dataPO['no_va'] = Helper::genCode($data['company_id'], $data['department_id'], $data['location_id'], 'VA') ?? 'VA';
        }
        $dataPO['department_id'] = $data['department_id'];
        $dataPO['location_id'] = $data['location_id'];
        $dataPO['company_id'] = $data['company_id'];
        $dataPO['supplier_id'] = $data['supplier_id'] ?? null;
        $dataPO['type'] = $data['type'] ?? null;
        $dataPO['deliver_id'] = $data['deliver_id'] ?? null;
        $dataPO['invoice_address_id'] = $data['invoice_address_id'] ?? null;
        $dataPO['top_id'] = $data['top_id'] ?? null;
        $dataPO['tax_id'] = $data['tax_id'] ?? null;
        $dataPO['total'] = $data['total'] ?? null;
        $dataPO['sub_total'] = $data['sub_total'] ?? null;
        $dataPO['ppn'] = $data['ppn'] ?? null;
        $dataPO['va_number'] = $data['va_number'] ?? null;
        $dataPO['price_ongkir'] = $data['price_ongkir'] ?? null;
        $dataPO['remark'] = $data['remark'] ?? null;
        $dataPO['no_rek'] = $data['no_rek'] ?? null;
        $dataPO['type_bank'] = $data['type_bank'] ?? null;
        $dataPO['rek_name'] = $data['rek_name'] ?? null;
        $dataPO['user_pjd_id'] = $data['user_pjd_id'] ?? null;

        $po = FleetPurchaseOrder::create($dataPO);
        
        foreach ($data['items'] as $key => $value) {
            $dataDetail['purchase_order_id'] = $po->id;
            $dataDetail['request_form_detail_id'] = $value['id'];
            $dataDetail['type_item_id'] = $value['type_item_id'];
            $dataDetail['qty'] = $value['qty'];
            $dataDetail['unit_price'] = $value['budget'];
            $dataDetail['amount'] = $value['amount'];

            FleetPurchaseOrderDetail::create($dataDetail);

            $rfd = RequestFormDetail::find($value['id']);
            $rfd->po_id = $po->id;
            $rfd->save();
        }

        return response()->json(['status' => true, 'message' => 'Pengajuan anda sudah tersimpan dengan No : ', 'no_po' => $po->no_po]);
    }

    public function approvePo(Request $request, $id)
    {
        $data = $request->all();

        if (isset($data['po_rsc'])) {
            $po = FleetPurchaseOrder::find($id);
            $po->approve_po = 1;
            $po->save();

            LogApproval::createLogApproval(1, 'approval-po-rsc', 'RSC', $id);
        }elseif (isset($data['po_direktur'])) {
            $po = FleetPurchaseOrder::find($id);
            $po->approve_po = 2;
            $po->save();

            LogApproval::createLogApproval(1, 'approval-po-direktur', 'DIREKTUR', $id);
        }

        return response()->json(['status' => true, 'message' => 'Dokumen Berhasil Diapprove']);
    }

    public function approvePr(Request $request, $id)
    {
        $data = $request->all();

        if (isset($data['pr_rsc'])) {
            $pr = FleetPurchaseOrder::find($id);
            $pr->approve_pr = 1;
            $pr->save();

            LogApproval::createLogApproval(1, 'approval-pr-rsc', 'RSC', $id);
        }elseif (isset($data['pr_finance'])) {
            $pr = FleetPurchaseOrder::find($id);
            $pr->approve_pr = 2;
            $pr->save();

            LogApproval::createLogApproval(1, 'approval-pr-finance', 'FINANCE', $id);
        }elseif (isset($data['pr_direktur'])) {
            $pr = FleetPurchaseOrder::find($id);
            $pr->approve_pr = 3;
            $pr->save();

            LogApproval::createLogApproval(1, 'approval-pr-direktur', 'DIREKTUR', $id);
        }

        return response()->json(['status' => true, 'message' => 'Dokumen Berhasil Diapprove']);
    }

    public function pdfApprovePo(Request $request)
    {
        $data = $request->all();
        $id = $request->id;

        if (isset($data['po_rsc'])) {
            $po = FleetPurchaseOrder::find($id);
            $po->approve_po = 1;
            $po->save();

            LogApproval::createLogApproval(1, 'approval-po-rsc', 'RSC', $id, $data['uid']);
        }elseif (isset($data['po_direktur'])) {
            $po = FleetPurchaseOrder::find($id);
            $po->approve_po = $data['status'];
            $po->save();

            LogApproval::createLogApproval(1, 'approval-po-direktur', 'DIREKTUR', $id, $data['uid']);
        }

        return redirect()->back()->with('success', 'Dokumen Berhasil Diapprove');
    }

    public function pdfApprovePr(Request $request)
    {
        $data = $request->all();
        $id = $request->id;

        if (isset($data['pr_rsc'])) {
            $pr = FleetPurchaseOrder::find($id);
            $pr->approve_pr = 1;
            $pr->save();

            LogApproval::createLogApproval(1, 'approval-pr-rsc', 'RSC', $id);
        }elseif (isset($data['pr_finance'])) {
            $pr = FleetPurchaseOrder::find($id);
            $pr->approve_pr = $data['status'];
            $pr->save();

            LogApproval::createLogApproval(1, 'approval-pr-finance', 'FINANCE', $id, $data['uid']);
        }elseif (isset($data['pr_direktur'])) {
            $pr = FleetPurchaseOrder::find($id);
            $pr->approve_pr = 3;
            $pr->save();

            LogApproval::createLogApproval(1, 'approval-pr-direktur', 'DIREKTUR', $id, $data['uid']);
        }

        return redirect()->back()->with('success', 'Dokumen Berhasil Diapprove');
    }

    public function approvePoDirektur(Request $request)
    {
        $data = FleetPurchaseOrder::find($request->id);
        $data['approval_rsc'] = LogApproval::where('slug', 'approval-po-rsc')->where('ref_id', $data->id)->orderBy('id', 'desc')->first();
        $data['approval_direktur'] = LogApproval::where('slug', 'approval-po-direktur')->where('ref_id', $data->id)->orderBy('id', 'desc')->first();

        return view('approval.po.direktur', ['data' => $data]);
    }

    public function approvePrFinance(Request $request)
    {
        $data = FleetPurchaseOrder::find($request->id);
        $data['approval_rsc'] = LogApproval::where('slug', 'approval-pr-rsc')->where('ref_id', $data->id)->orderBy('id', 'desc')->first();
        $data['approval_finance'] = LogApproval::where('slug', 'approval-pr-finance')->where('ref_id', $data->id)->orderBy('id', 'desc')->first();
        $data['approval_direktur'] = LogApproval::where('slug', 'approval-pr-direktur')->where('ref_id', $data->id)->orderBy('id', 'desc')->first();
        if ($data->type == 'Transfer'){
            $data['no_pr'] = $data->no_tf;
        }else if($data->type == 'Cash Advance'){
            $data['no_pr'] = $data->no_ca;
        }else if($data->type == 'Virtual Account'){
            $data['no_pr'] = $data->no_va;
        }
        
        return view('approval.pr.finance', ['data' => $data]);
    }

    public function approvePrDirektur(Request $request)
    {
        $data = FleetPurchaseOrder::find($request->id);
        $data['approval_rsc'] = LogApproval::where('slug', 'approval-pr-rsc')->where('ref_id', $data->id)->orderBy('id', 'desc')->first();
        $data['approval_finance'] = LogApproval::where('slug', 'approval-pr-finance')->where('ref_id', $data->id)->orderBy('id', 'desc')->first();
        $data['approval_direktur'] = LogApproval::where('slug', 'approval-pr-direktur')->where('ref_id', $data->id)->orderBy('id', 'desc')->first();
        if ($data->type == 'Transfer'){
            $data['no_pr'] = $data->no_tf;
        }else if($data->type == 'Cash Advance'){
            $data['no_pr'] = $data->no_ca;
        }else if($data->type == 'Virtual Account'){
            $data['no_pr'] = $data->no_va;
        }

        return view('approval.pr.direktur', ['data' => $data]);
    }
}
