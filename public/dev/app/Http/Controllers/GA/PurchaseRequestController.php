<?php

namespace App\Http\Controllers\GA;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\GA\StockRequest;
use App\Models\GA\StockRequestItems;
use App\Models\GA\StockRequestSetting;
use App\Models\GA\StockRequestSetHistory;
use App\Models\Fleet\Department;
use App\Models\Fleet\StockCategory;
use App\Models\Fleet\StockMaster;
use App\Models\Fleet\Location;
use App\Models\Fleet\LocStock;
use App\Models\Fleet\Asset;
use App\Models\Fleet\AssetPickup;
use App\Models\Fleet\AssetPickupDetail;
use App\Models\User;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use Auth;

class PurchaseRequestController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $cek_set = $this->checkApproveSetting();
        if (!$cek_set['status']) {
            return response()->json(['success' => $cek_set['status'], 'message' => $cek_set['message']], 500);
        }

        $data = StockRequest::with('department','category','location')->orderBy('id','desc')->get();
        foreach ($data as $key => $datas) {
            //check item 
            $item = StockRequestItems::where('dispatch_id',$datas->id)->whereNull('stock_id')->count();
            if ($item) {
                $datas->item_exist = 0;
            }else{
                $datas->item_exist = 1;
            }
            //check approve
            $sets = StockRequestSetting::orderBy('seq','asc')->get();
            $datas->text_approve = '';
            foreach ($sets as $set) {
                if ($datas->text_approve != "") {
                    break;
                }
                $set_history = StockRequestSetHistory::where('stock_request_id',$datas->id)->where('setting_id',$set->id)->count();
                if (!$set_history) {
                    $datas->text_approve = 'Waiting approve by '.$set->role;
                }
            }
        }
        return response()->json($data);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create(Request $request)
    {
        $cek_set = $this->checkApproveSetting();
        if (!$cek_set['status']) {
            return response()->json(['success' => $cek_set['status'], 'message' => $cek_set['message']], 500);
        }

        $category_detail = [];
        if ($request->has('id_category')) {
            $category_detail = StockCategory::find($request->get('id_category'));
        }
        $department = Department::all();
        $category = StockCategory::all();
        $location = Location::all();
        $master = StockMaster::where(function($query) use($request,$category_detail){
            if ($request->has('id_category') && $request->get('id_category') == '9' && isset($category_detail->code)) {
                $query->where('category_id',$category_detail->code);
            }
        })
        ->orderBy('id','desc')
        ->get();

        $quanti = 0;
        if ($request->has('stock_id') && $request->has('id_location')) {
            $check_qty = LocStock::where('stock_id',$request->get('stock_id'))
            ->where('location_id',$request->get('id_location'))
            ->first();

            if ($check_qty) {
                $quanti = $check_qty->quantity;
            }
        }

        return response()->json(['department' => $department, 'category' => $category, 'master' => $master, 'location' => $location, 'quanti' => $quanti]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        try {
            $cek_set = $this->checkApproveSetting();
            if (!$cek_set['status']) {
                return response()->json(['success' => $cek_set['status'], 'message' => $cek_set['message']], 500);
            }
            
            $validator = Validator::make($request->all(), [
                'department_id' => 'required',
                'category_id' => 'required',
                'narrative' => 'required',
                'despatch_date' => 'required',
                'to_date' => 'required',
                'location' => 'required',
                'request' => 'required',
                'company_id' => 'required',
                'branch_id' => 'required',
                'division_id' => 'required',
            ]);
    
            if ($validator->fails()) {
                return response()->json($validator->errors(), 500);
            }

            $stock_request = $request->except(['request']);
            $stock_request['code'] = 'PR-'.Carbon::now()->format('ymdHis').rand(10,99);
            $stock_request['closed'] = 0;
            $stock_request['budget_authorised'] = 0;
            $stock_request['authorised'] = 0;
            $stock_request['created_date'] = Carbon::now();
            $stock_request['requester'] = Auth::user()->first_name.' '.Auth::user()->last_name;
            $stock_request['seq_id'] = 1;
            $data = StockRequest::create($stock_request);
            
            if(is_array($request->input('request'))){
                $item = $request->input('request');
                for ($i=0; $i < count($item); $i++) { 
                    $detail = new StockRequestItems;
                    $detail->dispatch_id = $data->id;
                    $detail->stock_id = $item[$i]['item_id'];
                    $detail->quantity = $item[$i]['quantity'];
                    $detail->qty_ready = $item[$i]['quanti'];

                    if($item[$i]['quanti'] >= $item[$i]['quantity']){
                        $detail->completed = 1;
                    }
                    else{
                        $detail->completed = 0;
                    }

                    $detail->qty_delivered = 0;
                    $detail->decimal_places = 0;
                    $detail->price = $item[$i]['price'];
                    if (isset($item[$i]['uom'])) {
                        $detail->uom = $item[$i]['uom'];
                    }
                    // $detail->description = $item[$i]['description'];
                    $detail->group_id = $item[$i]['group_id'];
                    $detail->class_id = $item[$i]['class_id'];
                    $detail->note = $item[$i]['note'];
                    
                    $detail->save();
                }
            } 

            return response()->json(['success' => true, 'message' => 'Berhasil create data']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $cek_set = $this->checkApproveSetting();
        if (!$cek_set['status']) {
            return response()->json(['success' => $cek_set['status'], 'message' => $cek_set['message']], 500);
        }

        $data = StockRequest::find($id);
        $request = StockRequestItems::with(['item','group','class'])->where('dispatch_id',$id)->get();

        foreach ($request as $key => $req) {
            $category_detail = StockCategory::find($data->category_id);
    
            $item = StockMaster::where(function($query) use($req,$category_detail,$data){
                    $query->where('group_id', $req->group_id);
                    $query->where('class_id', $req->class_id);
                    if ($data->category_id == '9' && isset($category_detail->code)) {
                        $query->where('category_id',$category_detail->code);
                    }
            })
            ->get();

            $req->item_option = $item;
        }

        $category_detail = [];
        if (isset($data->category_id)) {
            $category_detail = StockCategory::find($data->category_id);
        }
        $department = Department::all();
        $category = StockCategory::all();
        $location = Location::all();
        $master = StockMaster::where(function($query) use($category_detail,$data){
            if (isset($category_detail->code) && isset($data->category_id) && $data->category_id == '9') {
                $query->where('category_id',$category_detail->code);
            }
        })
        ->orderBy('id','desc')
        ->get();

        return response()->json(['department' => $department, 'category' => $category, 'master' => $master, 'data' => $data, 'request' => $request, 'location' => $location]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        try {
            $cek_set = $this->checkApproveSetting();
            if (!$cek_set['status']) {
                return response()->json(['success' => $cek_set['status'], 'message' => $cek_set['message']], 500);
            }

            $validator = Validator::make($request->all(), [
                'department_id' => 'required',
                'category_id' => 'required',
                'narrative' => 'required',
                'despatch_date' => 'required',
                'to_date' => 'required',
                'location' => 'required',
                'request' => 'required',
            ]);
    
            if ($validator->fails()) {
                return response()->json($validator->errors(), 500);
            }

            $stock_request = $request->except(['request']);
            $data = StockRequest::find($id)->update($stock_request);
            
            if(is_array($request->input('request'))){
                StockRequestItems::where('dispatch_id',$id)->delete();
                $item = $request->input('request');
                for ($i=0; $i < count($item); $i++) { 
                    $detail = new StockRequestItems;
                    $detail->dispatch_id = $id;
                    if (isset($item[$i]['code'])) {
                        $detail->stock_id = $item[$i]['code'];
                    }
                    if (isset($item[$i]['quanti'])) {
                        $detail->qty_ready = $item[$i]['quanti'];

                        if($item[$i]['quantity'] > $item[$i]['quanti']){
                            $detail->completed = 1;
                        }
                        else{
                            $detail->completed = 0;
                        }
                    }
                    $detail->quantity = $item[$i]['quantity'];

                    
                    $detail->qty_delivered = 0;
                    $detail->decimal_places = 0;
                    $detail->price = $item[$i]['price'];
                    $detail->uom = $item[$i]['uom'];
                    $detail->group_id = $item[$i]['group_id'];
                    $detail->class_id = $item[$i]['class_id'];
                    $detail->note = $item[$i]['note'];
                    // $detail->description = $item[$i]['description'];
                    $detail->save();
                }
            } 

            return response()->json(['success' => true, 'message' => 'Berhasil update data']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        try{
            $cek_set = $this->checkApproveSetting();
            if (!$cek_set['status']) {
                return response()->json(['success' => $cek_set['status'], 'message' => $cek_set['message']], 500);
            }
            
            $data = StockRequest::destroy($id);
            StockRequestItems::where('dispatch_id',$id)->delete();

            return response()->json(['success' => true, 'message' => 'Berhasil deleted data']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    public function checkApproveSetting()
    {
        $return['status'] = true;
        $return['message'] = "";
        $jum_sett = StockRequestSetting::count();
        if ($jum_sett) {
            $data_cek = StockRequestSetting::where('role','Director')->count();
            if ($data_cek) {
                $return['status'] = true;
            }else{
                $return['status'] = false;
                $return['message'] = 'Wajib Set Approval Director di setting approval';
            }
        }else{
            $return['status'] = false;
            $return['message'] = 'Wajib isi approval setting dahulu';
        }

        return $return;
    }

    public function pickup($id)
    {
        try {
            $cek_set = $this->checkApproveSetting();
            if (!$cek_set['status']) {
                return response()->json(['success' => $cek_set['status'], 'message' => $cek_set['message']], 500);
            }

            $data = StockRequest::with(['ComparativeQuotation','items.item'])->find($id);
            if ($data->pick_up != 1) {
                return response()->json(['success' => false, 'message' => 'Data Error, Purchase request cant pickup'], 500);
            }
            
            $request = Asset::with('stock')->where('pr_id',$id)->get();
            foreach ($request as $key => $req) {
                $req->show_qr = false;
            }
            $user = User::orderBy('id','desc')->get();
            $id_user = Auth::user()->id;
            
            return response()->json(['data' => $data, 'request' => $request, 'user' => $user, 'id_user' => $id_user]);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    public function pickupStore(Request $request,$id)
    {
        try {
            $cek_set = $this->checkApproveSetting();
            if (!$cek_set['status']) {
                return response()->json(['success' => $cek_set['status'], 'message' => $cek_set['message']], 500);
            }

            $validator = Validator::make($request->all(), [
                'handover' => 'required',
                'receiver' => 'required',
            ]);
    
            if ($validator->fails()) {
                return response()->json($validator->errors(), 500);
            }

            $data = StockRequest::find($id);
            if ($data->pick_up != 1) {
                return response()->json(['success' => false, 'message' => 'Data Error, Purchase request cant pickup'], 500);
            }

            if ($data->category_id == 9) {
                $pickup = new AssetPickup;
                $pickup->stock_request_id = $id;
                $pickup->receive_by = $request->input('receiver');
                $pickup->handover_by = $request->input('handover_by');
                $pickup->date_receive = Carbon::today();
                $pickup->jumlah = StockRequestItems::where('dispatch_id',$id)->sum('quantity');
                $pickup->save();

                $update_assets = Asset::where('pr_id',$id)->update(['status' => 1, 'pickup_date' => Carbon::today(),'on_hand_id' => $request->input('receiver'), 'in_stock' => 1]);
                $assets = Asset::where('pr_id',$id)->get();
                foreach ($assets as $key => $asset) {
                    $detail = new AssetPickupDetail;
                    $detail->asset_pickup_id = $pickup->id;
                    $detail->asset_id = $asset->id;
                    $detail->save();

                    //update qty in table lockstock
                    $cek_lockstock = LocStock::where('location_id',$asset->pr->location)
                    ->where('stock_id',$asset->stock_id)
                    ->first();

                    if ($cek_lockstock) {
                        $this->updateLockStock($cek_lockstock->id);
                    }
                }
            }

            $data->pick_up = 3;
            $data->closed = 1;
            $data->save();

            $update_item = StockRequestItems::where('dispatch_id',$id)->update(['completed' => 1]);

            return response()->json(['success' => true, 'message' => 'Berhasil update pickup']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    public function updateLockStock($id)
    {
        $lockstock = LocStock::find($id);
        
        $asset_instock = Asset::where('stock_id',$lockstock->stock_id)
        ->where('location_id',$lockstock->location_id)
        ->where('in_stock',0)
        ->count();

        $asset_pending = Asset::where('stock_id',$lockstock->stock_id)
        ->where('location_id',$lockstock->location_id)
        ->whereNull('pickup_date')
        ->count();

        $asset_pickup = Asset::where('stock_id',$lockstock->stock_id)
        ->where('location_id',$lockstock->location_id)
        ->whereNotNull('pickup_date')
        ->count();

        $update = LocStock::where('id',$id)->update(['quantity' => $asset_instock, 'qty_pending' => $asset_pending, 'qty_pickup' => $asset_pickup]);
    }
}
