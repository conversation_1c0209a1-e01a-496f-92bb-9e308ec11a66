<?php

namespace App\Http\Controllers\GA;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\GA\AssetDestruction;
use App\Models\GA\AssetDestructionIncome;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use Auth;

class DestructionIncomeController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $data = AssetDestructionIncome::with(['destruction','user'])->orderBy('id','desc')->get();

        $total = AssetDestructionIncome::sum('total');

        return response()->json(['data' => $data, 'total' => $total]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $destruction = AssetDestruction::where('closed',1)->orderBy('id','desc')->get();

        return response()->json(['destruction' => $destruction]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'asset_destruction_id' => 'required',
                'total' => 'required',
            ]);
    
            if ($validator->fails()) {
                return response()->json($validator->errors(), 500);
            }

            $data = $request->all();
            $data['created_by'] = Auth::user()->id;
            
            $data = AssetDestructionIncome::create($data);

            return response()->json(['success' => true, 'message' => 'Berhasil input data']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $data = AssetDestructionIncome::find($id);
        $destruction = AssetDestruction::where('closed',1)->orderBy('id','desc')->get();

        return response()->json(['data' => $data, 'destruction' => $destruction]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        try {
            $validator = Validator::make($request->all(), [
                'asset_destruction_id' => 'required',
                'total' => 'required',
            ]);
    
            if ($validator->fails()) {
                return response()->json($validator->errors(), 500);
            }

            $data = $request->all();
            
            $data = AssetDestructionIncome::find($id)->update($data);

            return response()->json(['success' => true, 'message' => 'Berhasil update data']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        try{
            $data = AssetDestructionIncome::destroy($id);

            return response()->json(['success' => true, 'message' => 'Berhasil deleted data']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }
}
