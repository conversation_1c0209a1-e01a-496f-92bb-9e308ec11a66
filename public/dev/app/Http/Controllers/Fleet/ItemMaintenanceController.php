<?php

namespace App\Http\Controllers\Fleet;

use App\Http\Controllers\Controller;
use App\Models\Fleet\StockCategory;
use App\Models\Fleet\StockMaster;
use App\Models\Fleet\StockMasterClass;
use App\Models\Fleet\StockMasterGroup;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ItemMaintenanceController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $itemMaintenance = StockMaster::with('groupBarang', 'classBarang', 'stockCategoryGNRIVT', 'UOM', 'taxCategory')->orderBy('id','desc')->get();

        return $itemMaintenance;
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        // 
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {        
        try {
            $itemMaintenance = new StockMaster();
            $itemMaintenance->category_id = $request->get('category_id');
            $itemMaintenance->description = $request->get('description');
            $itemMaintenance->long_description = $request->get('long_description');
            $itemMaintenance->units = $request->get('units');
            $itemMaintenance->mbflag = $request->get('mbflag');
            $itemMaintenance->actual_cost = 0;
            $itemMaintenance->last_cost = 0;
            $itemMaintenance->material_cost = 0;
            $itemMaintenance->labour_cost = 0;
            $itemMaintenance->overhead_cost = 0;
            $itemMaintenance->last_cost_update = now();
            $itemMaintenance->lowest_level = 0;
            $itemMaintenance->eoq = 0;
            $itemMaintenance->volume = 0;
            $itemMaintenance->gross_weight = 0;
            $itemMaintenance->minimum_quantity = $request->get('minimum_quantity');
            // $itemMaintenance->barcode = $request->get('barcode');
            // $itemMaintenance->discount_category = $request->get('discount_category');
            $image_parts = explode(";base64,", $request->image);
            if ($image_parts) {
                $image_type_aux = explode("image/", $image_parts[0]);
                $image_type = $image_type_aux[1];
                if ($image_type == 'jpg' || $image_type == 'jpeg' || $image_type == 'png') {
                    $image_base64 = base64_decode($image_parts[1]);
                    $folderPath = 'storage/item_maintenance/';
                    $imageName = uniqid();
                    $imageFullPath = $folderPath.$imageName.".".$image_type;
                    file_put_contents($imageFullPath, $image_base64);
                    $itemMaintenance->append_file = $imageFullPath;
                }else{
                    return "Format gambar tidak sesuai, Tipe gambar hanya jpg, jpeg, png";
                }
            }
            $itemMaintenance->discontinued = $request->get('discontinued');
            $itemMaintenance->controlled = 0;
            $itemMaintenance->taxcat_id = $request->get('taxcat_id');
            $itemMaintenance->serialised = 0;
            $itemMaintenance->decimal_places = 0;
            $itemMaintenance->pansize = 0;
            $itemMaintenance->shrink_factor = 0;
            $itemMaintenance->next_serial_no = 0;
            $itemMaintenance->net_weight = 0;
            $itemMaintenance->class_id = $request->get('class_id');
            $itemMaintenance->group_id = $request->get('group_id');
            $itemMaintenance->save();

            $res = array([
                'status' => true,
                'message' => 'Membuat Pemeliharaan Item Berhasil'
            ]);
            return response()->json($res);
        } catch (\Throwable $th) {
            $res = array([
                'status' => false,
                'message' => $th
            ]);
            return response()->json($res);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $data = StockMaster::find($id);

        return $data;
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $itemMaintenance = StockMaster::findOrFail($id);

        return $itemMaintenance;
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        try {
            $itemMaintenance = StockMaster::findOrFail($id);
            $itemMaintenance->category_id = $request->get('category_id');
            $itemMaintenance->description = $request->get('description');
            $itemMaintenance->long_description = $request->get('long_description');
            $itemMaintenance->units = $request->get('units');
            $itemMaintenance->mbflag = $request->get('mbflag');
            $itemMaintenance->actual_cost = $request->get('actual_cost');
            $itemMaintenance->last_cost = $request->get('last_cost');
            $itemMaintenance->material_cost = $request->get('material_cost');
            $itemMaintenance->labour_cost = $request->get('labour_cost');
            $itemMaintenance->overhead_cost = $request->get('overhead_cost');
            $itemMaintenance->last_cost_update = $request->get('last_cost_update');
            $itemMaintenance->lowest_level = $request->get('lowest_level');
            $itemMaintenance->eoq = $request->get('eoq');
            $itemMaintenance->volume = $request->get('volume');
            $itemMaintenance->gross_weight = $request->get('gross_weight');
            $itemMaintenance->barcode = $request->get('barcode');
            $itemMaintenance->discount_category = $request->get('discount_category');
            $image_parts = explode(";base64,", $request->image);
            if ($image_parts) {
                $image_type_aux = explode("image/", $image_parts[0]);
                $image_type = $image_type_aux[1];
                // dd($image_type);
                if ($image_type == 'jpg' || $image_type == 'jpeg' || $image_type == 'png') {
                    $image_base64 = base64_decode($image_parts[1]);
                    $folderPath = 'storage/item_maintenance/';
                    $imageName = uniqid();
                    $imageFullPath = $folderPath.$imageName.".".$image_type;
                    file_put_contents($imageFullPath, $image_base64);
                    $itemMaintenance->append_file = $imageFullPath;
                }else{
                    return "Format gambar tidak sesuai, Tipe gambar hanya jpg, jpeg, png";
                }
            }
            $itemMaintenance->discontinued = $request->get('discontinued');
            $itemMaintenance->controlled = $request->get('controlled');
            $itemMaintenance->taxcat_id = $request->get('taxcat_id');
            $itemMaintenance->serialised = $request->get('serialised');
            $itemMaintenance->decimal_places = $request->get('decimal_places');
            $itemMaintenance->pansize = $request->get('pansize');
            $itemMaintenance->shrink_factor = $request->get('shrink_factor');
            $itemMaintenance->next_serial_no = $request->get('next_serial_no');
            $itemMaintenance->net_weight = $request->get('net_weight');
            $itemMaintenance->class_id = $request->get('class_id');
            $itemMaintenance->group_id = $request->get('group_id');
            $itemMaintenance->save();
    
            $res = array([
                'status' => true,
                'message' => 'Perbarui Pemeliharaan Item Berhasil'
            ]);
            return response()->json($res);
        } catch (\Throwable $th) {
            $res = array([
                'status' => false,
                'message' => $th
            ]);
            return response()->json($res);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        try {
            $itemMaintenance = StockMaster::findOrFail($id);
            $itemMaintenance->delete();

            $res = array([
                'status' => true,
                'message' => 'Hapus Pemeliharaan Item Berhasil'
            ]);
            return response()->json($res);
        } catch (\Throwable $th) {
            $res = array([
                'status' => false,
                'message' => $th
            ]);
            return response()->json($res);   
        }
    }

    public function groupBarang()
    {
        $groupBarang = StockMasterGroup::get();

        return $groupBarang;
    }

    public function classBarang()
    {
        $classBarang = StockMasterClass::get();

        return $classBarang;
    }

    public function stockCategoryGNRIVT()
    {
        $stock_category = StockCategory::all();

        return $stock_category;
    }

    public function getItemByType(Request $request)
    {
        $category_detail = [];
        if ($request->has('category_id')) {
            $category_detail = StockCategory::find($request->get('category_id'));
        }

        $item = StockMaster::where(function($query) use($request,$category_detail){
            if ($request->has('group_id')) {
                $query->where('group_id', $request->get('group_id'));
            }
            if ($request->has('class_id')) {
                $query->where('class_id', $request->get('class_id'));
            }
            if ($request->has('category_id') && $request->get('category_id') == '9' && isset($category_detail->code)) {
                $query->where('category_id',$category_detail->code);
            }
        })
        ->get();

        return response()->json($item);
    }
    
}
