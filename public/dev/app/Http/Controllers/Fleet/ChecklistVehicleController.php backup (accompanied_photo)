<?php

namespace App\Http\Controllers\Fleet;

use App\FleetMaster\Vehicle;
use App\FleetMaster\VehicleKmActual;
use App\Models\Fleet\Driver;
use App\Http\Controllers\Controller;
use App\Models\Fleet\AnswerQuestion;
use App\Models\Fleet\AnswerQuestionDetail;
use App\Models\Fleet\AnswerQuestionUser;
use App\Models\Fleet\CategoryQuestion;
use App\Models\Fleet\Question;
use App\Models\Fleet\QuestionMark;
use App\Models\Fleet\StatusVehicle;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class ChecklistVehicleController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $auth = Auth::user()->role;
        $data = AnswerQuestionUser::with('answerQuestionDetail', 'vehicle')->where('vehicle_id', '!=', null)->orderBy('id', 'desc')->get();
        foreach ($data as $key => $res) {
            $answerQuestionDetail = AnswerQuestionDetail::where('answer_question_user_id', $res->id)->get();
            $data[$key]['danger'] = false;
            foreach ($answerQuestionDetail as $cvalue) {
                if ($cvalue->answerQuestion->danger) {
                    $data[$key]['danger'] = true;
                }
            }

            if ($data[$key]['danger'] == false) {
                if (isset($res->statusVehicle->start)) {
                    $sv = $res->statusVehicle->start;
                    if ($res['total_point'] <= $sv) {
                        $data[$key]['ready'] = 0;
                    }else{
                        $data[$key]['ready'] = 1;
                    }
                }
                else{
                    $data[$key]['ready'] = 0;
                }
            }else if($data[$key]['danger'] == true){
                $data[$key]['status_vehicle'] = array('name' => 'red', 'color' => '#ff0000', 'danger' => true);
                $data[$key]['ready'] = 0;
            }
        }

        return response()->json(['data' => $data, 'user' => $auth]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create(Request $request)
    {
        try {
            $qrcode = $request->qrcode;
            $answer_question_user_id = $request->answer_question_user_id;
    
            $data = CategoryQuestion::where('slug', 'checklist-vehicle')->orderBy('id', 'asc')->with('question.answer')->get();
            foreach ($data as $key => $value) {
                $data[$key]['danger'] = false;
                $data[$key]['status_category'] = 0;
                $data[$key]['point'] = 0;
                foreach ($value->question as $ckey => $cvalue) {
                    $answerQuestionDetail = AnswerQuestionDetail::where('answer_question_user_id', $answer_question_user_id)->where('question_id', $cvalue->id)->first();
                    // return $answerQuestionDetail->answerQuestion->danger;
                    if (isset($answerQuestionDetail->point)) {
                        $data[$key]['status_category'] = 1;
                    }
                    $data[$key]['point'] += $answerQuestionDetail->point ?? 0;
                    if (isset($answerQuestionDetail->answerQuestion->danger)) {
                        $data[$key]['danger'] = true;
                    }
                }
                
                if ($data[$key]['danger'] == false) {
                    if ($data[$key]['status_category'] == 0) {
                        $data[$key]['status'] = array('name' => 'gray', 'color' => '#808080', 'status' => 'belum dikerjakan');
                    }else{
                        $data[$key]['status'] = StatusVehicle::where('start', '<=', $data[$key]['point'])->where('to', '>=', $data[$key]['point'])->first();
                    }
                }else if($data[$key]['danger'] == true){
                    $data[$key]['status'] = array('name' => 'red', 'color' => '#ff0000', 'danger' => true);
                }
            }
            return $data;
            if ($qrcode) {
                $data = CategoryQuestion::where('slug', 'checklist-vehicle')->where('qr_code', $qrcode)->orderBy('id', 'asc')->with('question.answer')->get();
                return response()->json($data);
            }
            
            return response()->json($data);
        } catch (\Throwable $th) {
            return response()->json(['status' => false, 'messages' => $th]);
        }
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        try{
            $data = $request->all();
            // dd($data);
            $answerQuestionUser['vehicle_id'] = $data[0]['kendaraan_id'];
            $answerQuestionUser['status'] = 0;
            $answerQuestionUser['user_id'] = Auth::user()->id;
            $answerQuestionUser['slug'] = 'checklist-vehicle';
            $answerUser = AnswerQuestionUser::create($answerQuestionUser);
            
            $CategoryQuestion = array();
            foreach ($data as $key => $res) {
                $question = Question::findOrFail($res['question_id']);
                $answer = AnswerQuestion::findOrFail($res['answer_id']);
                $res['question'] = $question->question;
                $res['answer'] = $answer->answer;
                $res['point'] = $answer->point;
                $res['answer_question_user_id'] = $answerUser->id;
                $res['answer_question_id'] = $res['answer_id'];
                unset($res['kendaraan_id']);

                $CategoryQuestion[$key] = AnswerQuestionDetail::create($res);
            }

            $point = 0;
            foreach ($CategoryQuestion as $answer) {
                // dd($answer->point);
                $point += (int)$answer->point;
            }

            // dd($point);
            $color = StatusVehicle::where('start', '<=', $point)->where('to', '>=', $point)->first()->id;
            $qPoint['status_vehicle_id'] = $color;
            $qPoint['total_point'] = $point;
            $qPoint['input_date'] = now();
            $qPoint['status'] = 1;
            AnswerQuestionUser::findOrFail($answerUser->id)->update($qPoint);
            // $questionPoint = QuestionMark::findOrFail($idM)->update($qPoint);

            return response()->json(['success' => true, 'message' => 'Selamat anda mendapatkan '.$point.' point']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        try {
            $data = AnswerQuestionDetail::where('answer_question_user_id', $id)->with('answerQuestion', 'answerUser', 'questions.categoryQuestion')->get();
            foreach ($data as $key => $value) {
                $answer_question = AnswerQuestion::where('question_id', $value->question_id)->get();
                $data[$key]['questions']['list_answer_question'] = $answer_question;
            }
            $status = AnswerQuestionUser::with('statusVehicle')->findOrFail($id);
            
            return response()->json(['data' => $data, 'status' => $status]);
        } catch (\Throwable $th) {
            return response()->json(['success' => false, 'message' => $th], 500);
        }
    }

    public function approvedQuestion(Request $request)
    {
        try {
            $data = $request->all();
            foreach ($data as $value) {
                $approval = AnswerQuestionDetail::findOrFail($value['question_id']);
                unset($value['question_id']);
                $approval->update($value);
            }
            if ($data[0]['status'] == 1) {
                $message = "Approval Successfully!";
            }elseif ($data[0]['status'] == 0) {
                $message = "Disaproval Successfully!";
            }
            return response()->json(['success' => true, 'message' => $message]);
        } catch (\Throwable $th) {
            return response()->json(['success' => false, 'message' => $th]);
        }
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $data = AnswerQuestionUser::findOrFail($id);
        if ($data['total_point'] <= $data->statusVehicle->start) {
            $data['ready'] = 0;
        }else{
            $data['ready'] = 1;
        }

        return response()->json($data);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        try{
            $data = $request->all();
            $CategoryQuestion = AnswerQuestionUser::findOrFail($id)->update($data);
            
            return response()->json(['success' => true, 'message' => 'Berhasil edit data']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $data = AnswerQuestionUser::findOrFail($id);
        $data->delete();
        $response = array(
            'success' => true,
            'message' => 'Berhasil hapus data'
        );

        return response()->json($response);
    }

    public function groupByColor(Request $request)
    {
        try {
            $data = AnswerQuestionUser::where('vehicle_id')->orderBy('id', 'desc')->with('statusVehicle', 'vehicle')->get()->groupBy('status_vehicle_id');
            if ($request->status_vehicle_id) {
                $data = AnswerQuestionUser::where('status_vehicle_id', $request->status_vehicle_id)->where('vehicle_id')->orderBy('id', 'desc')->with('statusVehicle', 'vehicle')->get();
            }
    
            return response()->json(['success' => true, 'data' => $data]);
        } catch (\Throwable $th) {
            return response()->json(['success' => false, 'message' => $th->getMessage()]);
        }
    }

    public function groupByCategory($id)
    {
        $data = AnswerQuestionDetail::where('answer_question_user_id', $id)->with('questions.categoryQuestion')->get();
        
        $groupByCategory = array();
        foreach ($data as $key => $value) {
            $groupByCategory[$value->questions->category_question_id][$key]['answer_question_user_id'] = $value->answer_question_user_id;
            $groupByCategory[$value->questions->category_question_id][$key]['point'] = $value->point;
            $groupByCategory[$value->questions->category_question_id][$key]['danger'] = $value->answerQuestion->danger;
            $groupByCategory[$value->questions->category_question_id][$key]['question_id'] = $value->question_id;
            $groupByCategory[$value->questions->category_question_id][$key]['answer_question_id'] = $value->answer_question_id;
            $groupByCategory[$value->questions->category_question_id][$key]['category_question_id'] = $value->questions->category_question_id;
        }
        $point = array();
        
        foreach ($groupByCategory as $key => $value) {
            $points = 0;
            $danger = false;
            foreach ($value as $cvalue) {
                $points += (int)$cvalue['point'];
                if ($cvalue['danger']) {
                    $danger = true;
                }
            }

            if ($danger == false) {
                $point[$key]['status'] = StatusVehicle::where('start', '<=', $points)->where('to', '>=', $points)->first();
            }else if($danger == true){
                $point[$key]['status'] = array('name' => 'red', 'color' => '#ff0000', 'danger' => true);
            }
            // $point[$key]['status'] = StatusVehicle::where('start', '<=', $points)->where('to', '>=', $points)->first();
            $point[$key]['point'] = $points;
            $point[$key]['question_detail'] = $value;
            $point[$key]['category_question_name'] = CategoryQuestion::findOrFail($key)->name;
            $point[$key]['category_question_id'] = $key;
        }
        return $point;
    }

    public function cheklistStatus($id)
    {
        try {
            $answerQuestionUser = AnswerQuestionUser::findOrFail($id);
            $data['driver'] = Driver::where('vehicle_id', $answerQuestionUser->vehicle_id)->with('user')->first();
            if ($data['driver']) {
                if ($data['driver']->sim_validity_period > now()) {
                    $data['driver']['masa_berlaku'] = 'HIDUP';
                }else if ($data['driver']->sim_validity_period > now()) {
                    $data['driver']['masa_berlaku'] = 'MATI';
                }
            }else{
                $data['driver'] = array('status' => false, 'message' => 'user belum terdaftar sebagai driver');
            }
            $data['checklist-health'] = AnswerQuestionUser::whereDate('created_at', Carbon::now())->where('user_id', $answerQuestionUser->user_id)->where('slug', 'checklist-health')->orderBy('id', 'desc')->get();
            $data['checklist-vehicle'] = AnswerQuestionUser::whereDate('created_at', Carbon::now())->where('user_id', $answerQuestionUser->user_id)->where('slug', 'checklist-vehicle')->with('vehicle')->orderBy('id', 'desc')->get();
            return response()->json($data);
        } catch (\Throwable $th) {
            return response()->json(['success' => false, 'message' => $th->getMessage()]);
        }
    }

    public function createQuestionUser(Request $request)
    {
        try {
            $data = $request->all();
            $vehicle = Vehicle::where('qr_code', $data['qr_code'])->first();
            $dataActual = new VehicleKmActual;
            $dataActual->id_vehicle = $vehicle->id;
            $dataActual->km_actual = $data['km_actual'];
            $dataActual->tanggal = $data['tanggal'];
            $dataActual->save();

            unset($data['qr_code']);
            unset($data['km_actual']);
            unset($data['tanggal']);

            $answerQuestionUser['vehicle_id'] = $vehicle->id;
            $answerQuestionUser['status'] = 0;
            $answerQuestionUser['input_date'] = now();
            $answerQuestionUser['user_id'] = Auth::user()->id;
            $answerQuestionUser['slug'] = 'checklist-vehicle';
            $answerUser = AnswerQuestionUser::create($answerQuestionUser);
    
            return response()->json(['status' => true, 'answer_question_user_id' => $answerUser->id]);
        } catch (\Throwable $th) {
            return response()->json(['success' => false, 'message' => $th->getMessage()]);
        }
    }

    public function storeByCategory(Request $request)
    {
        try{
            $data = $request->all();
            $CategoryQuestion = array();
            foreach ($data['answer'] as $key => $res) {
                $question = Question::findOrFail($res['question_id']);
                $answer = AnswerQuestion::findOrFail($res['answer_id']);
                $res['question_id'] = $question->id;
                $res['question'] = $question->question;
                $res['answer'] = $answer->answer;
                $res['point'] = $answer->point;
                $res['answer_question_user_id'] = $data['answer_question_user_id'];
                $res['answer_question_id'] = $res['answer_id'];

                $CategoryQuestion[$key] = AnswerQuestionDetail::create($res);
            }

            $point = 0;
            $danger = false;
            foreach ($CategoryQuestion as $answer) {
                $point += (int)$answer->point;
                if ($answer->danger) {
                    $danger = true;
                }
            }

            if ($danger == false) {
                $color = StatusVehicle::where('start', '<=', $point)->where('to', '>=', $point)->first();
            }else if($danger == true){
                $color = array('name' => 'red', 'color' => '#ff0000', 'danger' => true);
            }

            return response()->json(['success' => true, 'point' => $point, 'status' => $color]);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    public function finishStoreByCategory(Request $request)
    {
        try{
            $data = $request->all();
            $point = AnswerQuestionDetail::where('answer_question_user_id', $data['answer_question_user_id'])->sum('point');

            $statusVehicle = StatusVehicle::where('start', '<=', $point)->where('to', '>=', $point)->first();
            $qPoint['status_vehicle_id'] = $statusVehicle->id;
            $qPoint['total_point'] = $point/$data['length_category'];
            $qPoint['status'] = 1;
            AnswerQuestionUser::findOrFail($data['answer_question_user_id'])->update($qPoint);

            return response()->json(['success' => true, 'message' => 'Selamat anda mendapatkan '.$point.' point']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }
}
