<?php

namespace App\Http\Controllers\Fleet;

use App\Http\Controllers\Controller;
use App\Models\Fleet\StockMasterGroup;
use Illuminate\Http\Request;

class StockMasterGroupController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $masterClass = StockMasterGroup::get();

        return $masterClass;
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $masterClass = new StockMasterGroup;
        $masterClass->code = $request->get('code');
        $masterClass->description = $request->get('description');
        $masterClass->save();
        
        return 'Create Stock Master Class Successfully';
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $masterClass = StockMasterGroup::with('class')->find($id);

        return response()->json($masterClass);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $masterClass = StockMasterGroup::findOrFail($id);

        return $masterClass;
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $masterClass = StockMasterGroup::findOrFail($id);
        $masterClass->code = $request->get('code');
        $masterClass->description = $request->get('description');
        $masterClass->save();
        
        return 'Update Stock Master Class Successfully';
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $masterClass = StockMasterGroup::findOrFail($id);
        $masterClass->delete();
        
        return 'Delete Stock Master Class Successfully';
    }
}
