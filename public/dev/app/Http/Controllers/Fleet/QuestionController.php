<?php

namespace App\Http\Controllers\Fleet;

use App\Http\Controllers\Controller;
use App\Models\Fleet\AnswerQuestion;
use App\Models\Fleet\CategoryQuestion;
use App\Models\Fleet\Question;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class QuestionController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $type = $request->input('type');
        $category = CategoryQuestion::pluck('id','id');
        if ($type == 'kendaraan') {
            $category = CategoryQuestion::whereIn('slug',['checklist-vehicle'])->pluck('id','id');
        }elseif($type == 'kesehatan'){
            $category = CategoryQuestion::whereIn('slug',['checklist-health','quiz'])->pluck('id','id');
        }
        $data = Question::orderBy('id', 'desc')->whereIn('category_question_id',$category)->with('categoryQuestion')->get();
        return response()->json($data);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $data = CategoryQuestion::orderBy('id', 'asc')->get();

        return response()->json($data);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        try{
            $data = $request->all();
            $quest['category_question_id'] = $data['category_question_id'];
            $quest['question'] = $request->question;
            $quest['age_above'] = $request->age_above;
            if (!$request->age_above) {
                $quest['age_under'] = $request->age_under;
            }else{
                $quest['age_under'] = NULL;
            }
            if (isset($request->accompanied_photo)) {
                $quest['accompanied_photo'] = $request->accompanied_photo = 1;
            }else {
                $quest['accompanied_photo'] = $request->accompanied_photo = 0;
            }
            $Question = Question::create($quest);
            
            foreach ($data['answer'] as $key => $answer) {
                $answerResult['option'] = $answer['option'];
                $answerResult['answer'] = $answer['val_answer'];
                $answerResult['point'] = $answer['val_point'];
                $answerResult['required'] = $answer['required'];
                $answerResult['question_id'] = $Question->id;
                if (isset($answer['danger'])) {
                    $answerResult['danger'] = 1;
                }else {
                    $answerResult['danger'] = 0;
                }
                if ($answer['val_answer'] && $answer['val_point'] != "") {
                    $AnswerQuestion = AnswerQuestion::create($answerResult);
                }
            }

            return response()->json(['success' => true, 'message' => 'Berhasil input data']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $data = Question::findOrFail($id);
        $category = CategoryQuestion::orderBy('id', 'asc')->get();
        return response()->json(['question' => $data, 'answer' => $data->answer()->orderBy('option', 'asc')->get(), 'category' => $category]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        try{
            $data = $request->all();
            Log::info($data);
            $Question = Question::find($data['id']);
            $Question->question = $data['question'];
            $Question->category_question_id = $data['category_question_id'];
            $Question->accompanied_photo = $data['accompanied_photo'];
            $Question->age_above = $data['age_above'];
            if (!$data['age_above']) {
                $Question->age_under = $data['age_under'];
            }else{
                $Question->age_under = NULL;
            }
            $Question->save();
            
            if (is_array($data['answer'])) {
                for ($i=0; $i < count($data['answer']); $i++) { 
                    if (isset($data['answer'][$i]['id'])) {
                        $AnswerQuestion = AnswerQuestion::find($data['answer'][$i]['id']);
                        if ($AnswerQuestion) {
                            $AnswerQuestion->option = $data['answer'][$i]['option'];
                            $AnswerQuestion->answer = $data['answer'][$i]['answer'];
                            $AnswerQuestion->point = $data['answer'][$i]['point'];
                            $AnswerQuestion->danger = $data['answer'][$i]['danger'];
                            $AnswerQuestion->required = $data['answer'][$i]['required'];
                            $AnswerQuestion->save();
                        }
                    }
                }
            }

            return response()->json(['success' => true, 'message' => 'Berhasil edit data']);
        } catch (\Exception $e) {
            Log::info($e);
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $data = Question::findOrFail($id);
        $data->delete();
        $response = array(
            'success' => true,
            'message' => 'Berhasil hapus data'
        );

        return response()->json($response);
    }

    public function answerIndex()
    {
        $data = AnswerQuestion::orderBy('id', 'desc')->get();
        return response()->json($data);
    }
}
