<?php

namespace App\Http\Controllers\Fleet;

use App\Http\Controllers\Controller;
use App\Models\Fleet\CategoryItem;
use App\Models\Fleet\RequestFormDetail;
use App\Models\Fleet\TypeItem;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class RequestFormDetailController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $data = RequestFormDetail::with(['company', 'department', 'location', 'workshop', 'categoryItem', 'typeItem', 'user'])->orderBy('id', 'desc')->get();

        return response()->json($data);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        try{
            $data = $request->all();
            $type_item = TypeItem::find($data['type_item_id']);
            
            $data['uom'] = $type_item->uom ?? null;
            $data['budget'] = $type_item->budget ?? 0;
            $data['reminder_budget'] = $data['budget'] - $data['estimate_price'];
            $data['qty_item_approve'] = $data['qty'];
            $data['noted_manager'] = null;

            $create = RequestFormDetail::create($data);
            
            return response()->json(['success' => true, 'message' => 'Berhasil input data']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $data = RequestFormDetail::findOrFail($id);
        $data['category_item_name'] = CategoryItem::find($data->category_item_id)->name ?? null;
        $data['type_item_name'] = TypeItem::find($data->type_item_id)->name ?? null;

        return response()->json($data);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $data = RequestFormDetail::findOrFail($id);
        return response()->json($data);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        try{
            $data = $request->all();
            $type_item = TypeItem::find($data['type_item_id']);
            
            $data['uom'] = $type_item->uom ?? null;
            $data['budget'] = $type_item->budget ?? 0;
            $data['reminder_budget'] = $data['budget'] - $data['estimate_price'];
            $edit = RequestFormDetail::findOrFail($id)->update($data);

            $rfd = RequestFormDetail::findOrFail($id);
            $rfd->qty_reject = $data['qty_reject'] ?? null;
            $rfd->qty_request_buy = $data['qty_request_buy'] ?? null;
            $rfd->save();
            
            return response()->json(['success' => true, 'message' => 'Berhasil edit data']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $data = RequestFormDetail::findOrFail($id);
        $data->delete();
        $response = array(
            'success' => true,
            'message' => 'Berhasil hapus data'
        );

        return response()->json($response);
    }
}
