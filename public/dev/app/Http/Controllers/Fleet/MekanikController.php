<?php

namespace App\Http\Controllers\Fleet;

use App\Models\Fleet\SparepartUse;
use App\Http\Controllers\Controller;
use App\Http\Helper;
use App\Models\Fleet\Question;
use App\Models\Fleet\WorkshopDecision;
use App\Models\Fleet\WorkshopDetail;
use App\Models\Fleet\WorkshopSla;
use App\Models\User;
use App\Workshop\Workshop;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class MekanikController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        // try {
            // if ($request->has('start_date')) {
            //     $start_date = Carbon::parse($request->get('start_date'))->format('Y-m-d').' 00:00:00';
            // }else{
            //     $start_date = Carbon::today()->format('Y-m-d').' 00:00:00';
            // }
            // if ($request->has('end_date')) {
            //     $end_date = Carbon::parse($request->get('end_date'))->format('Y-m-d').' 23:59:00';
            // }else{
            //     $end_date = Carbon::today()->format('Y-m-d').' 23:59:00';
            // }

            $data = Workshop::with('workshopDetail', 'statusPerbaikan', 'user')
            // ->whereBetween('updated_at', [$start_date, $end_date])
            ->whereIn('status_perbaikan_id', [2, 4])
            ->orderBy('id', 'desc')
            ->get();
            foreach ($data as $k) {
                $license_no = $k->asset->assetDetail->where('attribute_code', 'nomor_polisi')->first()->value ?? '';
    
                $k['id'] = $k->id;
                $k['no_seri'] = $k->no_seri;
                $k['driver_name']  = ($k->driver->first_name ?? null).' '. ($k->driver->last_name ?? null);
                $k['license_no'] = $license_no;
                $k['waktu_pengajuan'] = $k->workshopDecision->created_at ?? null;
                $k['pengarah'] = User::findOrFail($k->gaho_id)->full_name ?? null;
                $k['alasan_pengajuan'] = $k->workshopDecision->note ?? null;
                $k['keputusan_pengarah'] = Helper::keputusan_ga_ho($k->keputusan_ga_ho);
                $k['status_kendaraan'] = $k->answerQuestionUser->statusVehicle->color ?? '-';

                $sla = WorkshopSla::where('workshop_id', $k->id)->orderBy('id', 'desc')->first();
                if (!empty($sla)) {
                    $k['sla_created_at'] = $sla->created_at->format('Y-m-d');
                    $k['sla_deadline'] = $sla->estimasi_pengerjaan;
                    $to = Carbon::parse($sla->estimasi_pengerjaan);
                    $from = Carbon::today();
                    $k['sla_status_time'] = ($to->diffInDays($from) > 0) ? 'Late Time' : 'On Time';
                }
            }

            return response()->json($data);
        // } catch (\Exception $e) {
        //     return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        // }
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        try{
            $data = $request->all();
            $dataWorkshopDetail['user_id'] = Auth::user()->id;
            $dataWorkshopDetail['note'] = $data['note'];
            $dataWorkshopDetail['workshop_id'] = $data['workshop_id'];
            $dataWorkshopDetail['jenis_perbaikan'] = $data['jenis_perbaikan'];
            $dataWorkshopDetail['jenis_kerusakan'] = $data['jenis_kerusakan'];
            if (isset($data['item_id'])) {
                $dataWorkshopDetail['item_id'] = $data['item_id'];
                $dataWorkshopDetail['item_name'] = Question::findOrFail($data['item_id'])->question ?? null;
            }else{
                $dataWorkshopDetail['item_id'] = null;
                $dataWorkshopDetail['item_name'] = 'SERVICE (optional)';
            }
            $WorkshopDetail = WorkshopDetail::create($dataWorkshopDetail);

            for ($i=0; $i < count($data['sparepart']); $i++) { 
                $dataSparepart['workshop_detail_id'] = $WorkshopDetail->id;
                $dataSparepart['sparepart_id'] = $data['sparepart'][$i];
                $dataSparepart['qty'] = $data['qty'][$i];
                $dataSparepart['status_perbaikan_id'] = 2;
                if ($data['sparepart'][$i] && $data['qty'][$i]) {
                    SparepartUse::create($dataSparepart);
                }
            }
            
            return response()->json(['success' => true, 'message' => 'Penambahan catatan berhasil']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 200);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        try {
            $data = Workshop::with('workshopDetail', 'statusPerbaikan')->findOrFail($id);

            return response()->json($data);
        } catch (\Throwable $th) {
            return response()->json(['success' => false, 'message' => $th->getMessage()], 500);
        }
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $data = WorkshopDetail::findOrFail($id);
        return response()->json($data);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        try {
            $workshop = Workshop::findOrFail($id);
            $workshop->tindakan_mekanik = 1;
            $workshop->status_perbaikan_id = 3;
            $workshop->save();

            return response()->json(['success' => true, 'message' => 'Kendaraan Berhasil Diperiksa Tunggu Arahan Dari GA']);
        } catch (\Throwable $th) {
            return response()->json(['success' => false, 'message' => $th->getMessage()], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $data = WorkshopDetail::findOrFail($id);
        $data->delete();
        $response = array(
            'success' => true,
            'message' => 'Berhasil hapus data'
        );

        return response()->json($response);
    }

    public function user()
    {
        try{
            $data = User::where('role', 'Mekanik')->orderBy('id', 'desc')->get();
            
            return response()->json($data);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    public function storeItem(Request $request, $id)
    {
        try{
            $data = $request->all();
            foreach ($data as $key => $value) {
                $data['workshop_id'] = $id;
                $data['user_id'] = Auth::user()->id;
                $data['jenis_kerusakan'] = $value['jenis_kerusakan'];
                $data['jenis_perbaikan'] = $value['jenis_perbaikan'];
                $data['sparepart'] = json_encode($value['sparepart']);
                // $data['mekanik_id'] = json_encode($value['mekanik_id']);
                $data['note'] = $value['note'];
                if (isset($value['item_id'])) {
                    $data['item_id'] = $value['item_id'];
                    $data['item_name'] = Question::findOrFail($value['item_id'])->question ?? null;
                }else{
                    $data['item_id'] = null;
                    $data['item_name'] = 'SERVICE (optional)';
                }
                $WorkshopDetail = WorkshopDetail::create($data);
                foreach ($value['sparepart'] as $ckey => $itemSparepart) {
                    $dataSparepart['workshop_detail_id'] = $WorkshopDetail->id;
                    $dataSparepart['sparepart_id'] = $itemSparepart;
                    $dataSparepart['qty'] = $value['qty'][$ckey];
                    $dataSparepart['status_perbaikan_id'] = 4;
                    if ($data['sparepart'][$ckey] && $data['qty'][$ckey]) {
                        SparepartUse::create($dataSparepart);
                    }
                }
            }

            $workshop = Workshop::findOrFail($id);
            $workshop->tindakan_mekanik = 1;
            $workshop->status_perbaikan_id = 3;
            $workshop->save();
            
            return response()->json(['success' => true, 'message' => 'Penambahan catatan berhasil']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 200);
        }
    }
}
