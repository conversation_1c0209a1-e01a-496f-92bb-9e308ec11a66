<?php

namespace App\Http\Controllers\Fleet;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Fleet\Department;
use App\Models\Fleet\Company;
use App\Models\User;
use Illuminate\Support\Facades\Validator;

class DepartmentController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $data = Department::with('company')->orderBy('id','desc')->get();
        return response()->json($data);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $company = Company::pluck('name','id');

        return response()->json(['company' => $company]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $department = User::get()->groupBy('department_id');
        $code = [];
        foreach ($department as $key => $value) {
            $com = Department::where('code', $value->first()->department_id)->first();
            if ($com) {
                $com->name = $value->first()->department_name;
                $com->save();
                array_push($code, $value->first()->department_id);
            }else {
                $data['code'] = $value->first()->department_id;
                $data['name'] = $value->first()->department_name;
                $create = Department::create($data);
                array_push($code, $value->first()->department_id);
            }
        }
        $delete = Department::whereNotIn('code', $code)->delete();
        // $validator = Validator::make($request->all(), [
        //     'code' => 'required',
        // ]);
 
        // if ($validator->fails()) {
        //     return response()->json($validator->errors(), 500);
        // }
        
        // $divisi = Department::create($request->all());

        return response()->json(['success' => true, 'message' => 'Berhasil Sinkronkan data']);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $data = Department::with(['user','section'])->find($id);
        return response()->json($data);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $data = Department::find($id);
        $company = Company::pluck('name','id');

        return response()->json(['data' => $data, 'company' => $company]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'code' => 'required'
        ]);
 
        if ($validator->fails()) {
            return response()->json($validator->errors(), 500);
        }
        
        $divisi = Department::find($id)->update($request->all());

        return response()->json(['success' => true, 'message' => 'Berhasil update data']);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $divisi = Department::destroy($id);

        return response()->json(['success' => true, 'message' => 'Berhasil delete data']);
    }
}
