<?php

namespace App\Http\Controllers\Fleet;

use App\Http\Controllers\Controller;
use App\Http\Helper;
use App\Models\Fleet\AssetDetail;
use App\Models\Fleet\CategoryItem;
use App\Models\Fleet\Company;
use App\Models\Fleet\Department;
use App\Models\Fleet\LogApproval;
use App\Models\Fleet\MasterLocation;
use App\Models\Fleet\RequestForm;
use App\Models\Fleet\RequestFormDetail;
use App\Models\Fleet\TypeItem;
use App\Models\User;
use App\Workshop\Workshop;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;

use function PHPUnit\Framework\isEmpty;
use function PHPUnit\Framework\isNull;

class RequestFormController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        if ($request->has('start_date')) {
            $start_date = Carbon::parse($request->start_date)->format('Y-m-d').' 00:00:00';
        }else{
            $start_date = Carbon::today()->format('Y-m-d').' 00:00:00';
        }
        if ($request->has('to_date')) {
            $to_date = Carbon::parse($request->to_date)->format('Y-m-d').' 23:59:00';
        }else{
            $to_date = Carbon::today()->format('Y-m-d').' 23:59:00';
        }
        $data['start_date'] = $start_date;
        $data['to_date'] = $to_date;

        
        if ($request->get('date-range') == 1 || $request->has('start_date')) {
            $data = RequestForm::with(['company', 'department', 'section', 'location', 'workshop', 'user'])
            ->where(function($query) use($request){
                if ($request->get('approval-rf') || $request->get('status') == 1) {
                    $company_user = Company::where('code', Auth::user()->company_id)->first();
                    $dept_user = Department::where('code', Auth::user()->department_id)->first();

                    if (Auth::user()->role == 'Admin') {
                        $query->whereNull('status_approval')->whereNull('verified')->whereNull('status_approval_pr_ga')->whereNull('status_approval_pr_direktur');
                    }else{
                        $query->where('department_id', $dept_user->id)->where('company_id', $company_user->id);
                        $query->whereNull('status_approval')->whereNull('verified')->whereNull('status_approval_pr_ga')->whereNull('status_approval_pr_direktur');
                    }
                }else if ($request->get('verivikasi-rf') || $request->get('status') == 3) {
                    $company_user = Company::where('code', Auth::user()->company_id)->first();
                    $dept_user = Department::where('code', Auth::user()->department_id)->first();

                    if (Auth::user()->role == 'Admin') {
                        $query->where('status_approval', 1)->whereNull('verified')->whereNull('status_approval_pr_ga')->whereNull('status_approval_pr_direktur');
                    }else{
                        $query->where('department_id', $dept_user->id)->where('company_id', $company_user->id);
                        $query->where('status_approval', 1)->whereNull('verified')->whereNull('status_approval_pr_ga')->whereNull('status_approval_pr_direktur');
                    }
                }else if ($request->get('pr-ga') || $request->get('status') == 4) {
                    $query->where('status_approval', 1)->where('verified', 1)->whereNull('status_approval_pr_ga')->whereNull('status_approval_pr_direktur');
                }else if ($request->get('pr-direktur') || $request->get('status') == 5) {
                    $query->where('status_approval', 1)->where('verified', 1)->where('status_approval_pr_ga', 1)->whereNull('status_approval_pr_direktur');
                }else if ($request->get('pending')) {
                    $query->where('status_approval', '<', 6);
                }
                
                if ($request->get('pr')) {
                    $query->where('status_approval', 1)->where('verified', 1);
                }elseif ($request->get('rf')) {
                    if ($request->has('status')) {
                        if ($request->status == 1) {
                            $query->where('status_approval', 1);
                        }elseif ($request->status == 1) {
                            $query->where('status_approval', 2);
                        }
                    }
                }

                if ($request->get('company')) {
                    $query->where('company_id', $request->get('company'));
                }
                if ($request->get('location')) {
                    $query->where('location_id', $request->get('location'));
                }
                if ($request->get('department')) {
                    $query->where('department_id', $request->get('department'));
                }
                if ($request->get('type_submission')) {
                    $query->where('type_submission', $request->get('type_submission'));
                }
                if ($request->get('no_seri')) {
                    $query->whereId($request->get('no_seri'));
                }
                if ($request->get('nopol')) {
                    $query->where('nopol', $request->get('nopol'));
                }
            })
            ->whereBetween('created_at', [$start_date,$to_date])
            ->orderBy('id', 'desc')
            ->get();
        }else{
            $data = RequestForm::with(['company', 'department', 'section', 'location', 'workshop', 'user'])
            ->where(function($query) use($request){
                if ($request->get('approval-rf') || $request->get('status') == 1) {
                    $company_user = Company::where('code', Auth::user()->company_id)->first();
                    $dept_user = Department::where('code', Auth::user()->department_id)->first();

                    if (Auth::user()->role == 'Admin') {
                        $query->whereNull('status_approval')->whereNull('verified')->whereNull('status_approval_pr_ga')->whereNull('status_approval_pr_direktur');
                    }else{
                        $query->where('department_id', $dept_user->id)->where('company_id', $company_user->id);
                        $query->whereNull('status_approval')->whereNull('verified')->whereNull('status_approval_pr_ga')->whereNull('status_approval_pr_direktur');
                    }
                }else if ($request->get('verivikasi-rf') || $request->get('status') == 3) {
                    $company_user = Company::where('code', Auth::user()->company_id)->first();
                    $dept_user = Department::where('code', Auth::user()->department_id)->first();

                    if (Auth::user()->role == 'Admin') {
                        $query->where('status_approval', 1)->whereNull('verified')->whereNull('status_approval_pr_ga')->whereNull('status_approval_pr_direktur');
                    }else{
                        $query->where('department_id', $dept_user->id)->where('company_id', $company_user->id);
                        $query->where('status_approval', 1)->whereNull('verified')->whereNull('status_approval_pr_ga')->whereNull('status_approval_pr_direktur');
                    }
                }else if ($request->get('pr-ga') || $request->get('status') == 4) {
                    $query->where('status_approval', 1)->where('verified', 1)->whereNull('status_approval_pr_ga')->whereNull('status_approval_pr_direktur');
                }else if ($request->get('pr-direktur') || $request->get('status') == 5) {
                    $query->where('status_approval', 1)->where('verified', 1)->where('status_approval_pr_ga', 1)->whereNull('status_approval_pr_direktur');
                }else if ($request->get('pending')) {
                    $query->where('status_approval', '<', 6);
                }
                
                if ($request->get('pr')) {
                    $query->where('status_approval', 1)->where('verified', 1);
                }elseif ($request->get('rf')) {
                    if ($request->has('status')) {
                        if ($request->status == 1) {
                            $query->where('status_approval', 1);
                        }elseif ($request->status == 1) {
                            $query->where('status_approval', 2);
                        }
                    }
                }

                if ($request->get('po')) {
                    $query->where('status_approval', 1)->where('verified', 1)->where('status_approval_pr_ga', 1)->where('status_approval_pr_direktur', 1);
                }

                if ($request->get('company')) {
                    $query->where('company_id', $request->get('company'));
                }
                if ($request->get('location')) {
                    $query->where('location_id', $request->get('location'));
                }
                if ($request->get('department')) {
                    $query->where('department_id', $request->get('department'));
                }
                if ($request->get('type_submission')) {
                    $query->where('type_submission', $request->get('type_submission'));
                }
                if ($request->get('no_seri')) {
                    $query->whereId($request->get('no_seri'));
                }
                if ($request->get('nopol')) {
                    $query->where('nopol', $request->get('nopol'));
                }
            })
            ->orderBy('id', 'desc')
            ->get();
        }

        foreach ($data as $key => $value) {
            $value['date_approval_rf'] = LogApproval::where('ref_id', $value->id)->where('slug', 'status-approval-rf')->orderBy('id', 'desc')->first()->created_at ?? null;
            $value['date_verivikasi_rf'] = LogApproval::where('ref_id', $value->id)->where('slug', 'verified-ga')->orderBy('id', 'desc')->first()->created_at ?? null;
            $value['date_pr_ga'] = LogApproval::where('ref_id', $value->id)->where('slug', 'status-approval-pr-ga')->orderBy('id', 'desc')->first()->created_at ?? null;
            $value['date_pr_direktur'] = LogApproval::where('ref_id', $value->id)->where('slug', 'status-approval-pr-direktur')->orderBy('id', 'desc')->first()->created_at ?? null;

            $value->no_seri = $value->workshop->no_seri ?? '-';
            
            // if ($value->status_approval == 1) {
            //     $value->workshop->no_seri = 'Approved Manager Dept';
            // }

            $value->uid = Auth::user()->id;

            if ($value->status_approval) {
                if ($value->status_approval == 1) {
                    $value->status_process = 'Proses Verivikasi GA';
                }elseif($value->status_approval == 2){
                    $value->status_process = 'Reject Manager Dept';
                }
            }else{
                $value->status_process = 'Proses Approval RF Manager Dept';
            }
            
            if ($value->verified == 1) {
                $value->status_process = 'Proses Approval PR GA';
            }
            if ($value->status_approval_pr_ga == 1) {
                $value->status_process = 'Proses Approval PR DIREKTUR';
                $value->status_approval_pr = 'Approved GA';
            }elseif ($value->status_approval_pr_ga == 2) {
                $value->status_approval_pr = 'Reject GA';
            }
            if ($value->status_approval_pr_direktur == 1) {
                $value->status_process = 'Selesai';
                $value->status_approval_pr = 'Approved DIREKTUR';
            }

        }

        return response()->json($data);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        try{
            $data = $request->all();
            $data['user_id'] = Auth::user()->id;

            $workshop = Workshop::find($data['workshop_id']);
            if (isset($workshop->asset)) {
                $data['plat_number'] = AssetDetail::where('asset_id', $workshop->id)->where('attribute_code', 'nomor_polisi')->first()->value ?? null;
            }
            
            $data['no_rf'] = Helper::genCode($data['company_id'], $data['department_id'], $data['location_id'], 'RF');

            $create = RequestForm::create($data);

            if (isset($data['request_form_detail'])) {
                foreach ($data['request_form_detail'] as $key => $value) {
                    if($value['type_item_id'] && $value['category_item_id']){
                        $type_item = TypeItem::find($value['type_item_id']);
                    
                        $value['uom'] = $type_item->uom ?? null;
                        $value['budget'] = $type_item->budget ?? 0;
                        $value['reminder_budget'] = $value['budget'] - $value['estimate_price'];
                        $value['qty_item_approve'] = $value['qty'];
                        $value['noted_manager'] = null;
                        $value['request_form_id'] = $create->id;
        
                        $createDetail = RequestFormDetail::create($value);
                    }
                }
            }
            
            return response()->json(['success' => true, 'message' => 'Pengajuan Anda Sudah tersimpan di sistem dengan No', 'no_rf' =>  $create->no_rf, 'request_form_id' => $create->id]);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $data = RequestForm::with(['company', 'department', 'location', 'workshop', 'user'])->find($id);

        $data['request_form_id'] = $data->id ?? null;
        $data['company_name'] = $data->company->name ?? null;
        $data['department_name'] = $data->department->name ?? null;
        $data['location_name'] = $data->location->name ?? null;
        $data['no_seri'] = $data->workshop->no_seri ?? null;

        $data['request_form_detail'] = RequestFormDetail::where('request_form_id', $data->id)->whereNotNull('category_item_id')->whereNotNull('type_item_id')->get();
        foreach ($data['request_form_detail'] as $key => $value) {
            $value['category_item_name'] = CategoryItem::find($value->category_item_id)->name ?? null;
            $value['type_item_name'] = TypeItem::find($value->type_item_id)->name ?? null;
        }
        
        return response()->json($data);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $data = RequestForm::findOrFail($id);
        return response()->json($data);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        try{
            $data = $request->all();

            $rf = RequestForm::findOrFail($id);
            
            if ($rf->status_approval == null && isset($data['status_approval'])) {
                LogApproval::createLogApproval(1, 'status-approval-rf', 'Manager Dept', $id);
            }elseif ($rf->verified == null && isset($data['verified'])) {
                LogApproval::createLogApproval(1, 'verified-ga', 'GA', $id);

                $data['no_pr'] = Helper::genCode($rf->company_id, $rf->department_id, $rf->location_id, 'PR');
            }elseif ($rf->status_approval_pr_ga == null && isset($data['status_approval_pr_ga'])) {
                LogApproval::createLogApproval(1, 'status-approval-pr-ga', 'GA', $id);
            }elseif ($rf->status_approval_pr_direktur == null && isset($data['status_approval_pr_direktur'])) {
                LogApproval::createLogApproval(1, 'status-approval-pr-direktur', 'Direktur', $id);
            }

            $edit = $rf->update($data);

            if (isset($data['request_form_detail'])) {
                foreach ($data['request_form_detail'] as $key => $value) {
                    if($value['type_item_id'] && $value['category_item_id']){
                        unset($value['category_item_name']);
                        unset($value['type_item_name']);
                        $type_item = TypeItem::find($value['type_item_id']);
                    
                        $value['uom'] = $type_item->uom ?? null;
                        $value['budget'] = $type_item->budget ?? 0;
                        $value['reminder_budget'] = $value['budget'] - $value['estimate_price'];
                        $value['qty_item_approve'] = $value['qty'];
        
                        $createDetail = RequestFormDetail::where('request_form_id', $id)->update($value);
                    }
                }
            }
            
            return response()->json(['success' => true, 'message' => 'Berhasil edit data']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $data = RequestForm::findOrFail($id);
        $data->delete();
        $response = array(
            'success' => true,
            'message' => 'Berhasil hapus data'
        );

        return response()->json($response);
    }

    public function resetDetail($id)
    {
        $rf = RequestForm::find($id);
        $rf->company_id = null;
        $rf->department_id = null;
        $rf->location_id = null;
        $rf->type_submission = null;
        $rf->save();

        $data = RequestFormDetail::where('request_form_id', $id)->get();
        foreach ($data as $key => $value) {
            $value->delete();
        }
        $response = array(
            'success' => true,
            'message' => 'Berhasil reset data'
        );

        return response()->json($response);
    }

    public function reportRf(Request $request)
    {
        $data = RequestForm::find($request->id);
        $user = User::find($request->uid);
        $data['approve_rf'] = LogApproval::where('ref_id', $data->id)->where('slug', 'status-approval-rf')->orderBy('id', 'desc')->first();

        return view('pdf.request_form', ['data' => $data, 'user' => $user]);
    }

    public function reportPr(Request $request)
    {
        $data = RequestForm::find($request->id);
        $user = User::find($request->uid);

        $approveManDept = LogApproval::where('ref_id', $data->id)->where('slug', 'status-approval-rf')->orderBy('id', 'desc')->first();
        $data['approved_manager_dept'] = $approveManDept->user->full_name ?? null;
        $data['date_approved_manager_dept'] = $approveManDept->created_at ?? null;
        $data['role_approved_manager_dept'] = $approveManDept->user->role ?? null;
        
        $approveByDirektur = LogApproval::where('ref_id', $data->id)->where('slug', 'status-approval-pr-ga')->orderBy('id', 'desc')->first();
        $data['approved_ga_manager'] = $approveByDirektur->user->full_name ?? null;
        $data['date_approved_ga_manager'] = $approveByDirektur->created_at ?? null;
        $data['role_approved_ga_manager'] = $approveByDirektur->user->role ?? null;

        $approveByDirektur = LogApproval::where('ref_id', $data->id)->where('slug', 'status-approval-pr-direktur')->orderBy('id', 'desc')->first();
        $data['approved_direktur'] = $approveByDirektur->user->full_name ?? null;
        $data['date_approved_direktur'] = $approveByDirektur->created_at ?? null;
        $data['role_approved_direktur'] = $approveByDirektur->user->role ?? null;
        
        return view('pdf.purchase_request_form', ['data' => $data, 'user' => $user]);
    }

    public function riwayatApproval($id)
    {
        $data = LogApproval::where('ref_id', $id)->get();

        return response()->json($data);
    }

    public function getRefNo(Request $request)
    {
        $data = $request->all();

        $ref_no = Helper::genCode($data['company_id'], $data['department_id'], $data['location_id'], 'RF');

        return response()->json($ref_no);
    }
}
