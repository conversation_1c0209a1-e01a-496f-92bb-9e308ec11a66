<?php

namespace App\Http\Controllers\Fleet;

use App\Http\Controllers\Controller;
use App\Models\Fleet\AnswerQuestion;
use App\Models\Fleet\AnswerQuestionDetail;
use App\Models\Fleet\AnswerQuestionUser;
use App\Models\Fleet\CategoryQuestion;
use App\Models\Fleet\Question;
use App\Models\Fleet\StatusVehicle;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class QuizController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $data = AnswerQuestionUser::where('user_id', Auth::user()->id)->with('answerQuestionDetail', 'statusVehicle')->where('slug', 'quiz')->orderBy('id', 'desc')->get();
        foreach ($data as $key => $res) {
            $sv = $res->statusVehicle->start ?? 0;
            if ($res['total_point'] <= $sv) {
                $data[$key]['ready'] = 0;
            }else{
                $data[$key]['ready'] = 1;
            }
        }
        return response()->json(['data' => $data]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create(Request $request)
    {
        $route = $request->slug;
        if ($route) {
            $data = CategoryQuestion::where('slug', 'quiz')->orderBy('id', 'asc')->with('question.answer')->get();
            // $data['success'] = true;
            $status = array('name' => 'gray', 'color' => '#808080');
            return response()->json(['color_name' => $status, 'quiz_question' => $data]);
        }else{
            return response()->json(['success' => false, 'messages' => 'not found!']);
        }
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        try{
            $data = $request->all();
            // return $data;
            $answerQuestionUser['status'] = 0;
            $answerQuestionUser['user_id'] = Auth::user()->id;
            $answerQuestionUser['slug'] = 'quiz';
            $answerUser = AnswerQuestionUser::create($answerQuestionUser);
            
            $total_poin = 0;
            $point = 0;
            $CategoryQuestion = array();
            foreach ($data['answer'] as $key => $res) {
                $question = Question::findOrFail($res['question_id']);
                $answer = AnswerQuestion::findOrFail($res['answer_id']);

                $poin_tertinggi = AnswerQuestion::where('question_id',$res['question_id'])->orderBy('point','desc')->first();
                if ($poin_tertinggi) {
                    $total_poin += $poin_tertinggi->point;
                }

                $res['question'] = $question->question;
                $res['answer'] = $answer->answer;
                $res['point'] = $answer->point;
                $res['answer_question_user_id'] = $answerUser->id;
                $res['answer_question_id'] = $res['answer_id'];
                
                $CategoryQuestion[$key] = AnswerQuestionDetail::create($res);
                $point += $answer->point;
            }

            if ($point > 0) {
                $total_persen = ($point/$total_poin)*100;
            }else{
                $total_persen = 0;
            }

            // dd($point);
            if ($total_persen > 99) {
                $color = StatusVehicle::where('name','green')->first()->id;
            }else{
                $color = StatusVehicle::where('name','red')->first()->id;
            }
            
            $qPoint['status_vehicle_id'] = $color;
            $qPoint['total_point'] = $point;
            $qPoint['input_date'] = now();
            $qPoint['status'] = 1;
            AnswerQuestionUser::findOrFail($answerUser->id)->update($qPoint);

            return response()->json(['success' => true, 'message' => 'Selamat anda mendapatkan '.$point.' point']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        // 
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $data = AnswerQuestionUser::with('statusVehicle')->findOrFail($id);
        if ($data['total_point'] <= $data->statusVehicle->start) {
            $data['ready'] = 0;
        }else{
            $data['ready'] = 1;
        }
        return response()->json($data);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        try{
            $data = $request->all();
            $CategoryQuestion = AnswerQuestionUser::findOrFail($id)->update($data);
            
            return response()->json(['success' => true, 'message' => 'Berhasil edit data']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $data = AnswerQuestionUser::findOrFail($id);
        $data->delete();
        $response = array(
            'success' => true,
            'message' => 'Berhasil hapus data'
        );

        return response()->json($response);
    }
}
