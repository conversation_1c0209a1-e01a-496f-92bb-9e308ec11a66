<?php

namespace App\Http\Controllers\Fleet;

use App\Exports\ExportDriver;
use App\Http\Controllers\Controller;
use App\Models\Fleet\Driver;
use App\Models\Fleet\Company;
use App\Models\Fleet\Department;
use App\Models\Fleet\Location;
use App\Models\Fleet\TypeSim;
use App\Models\Fleet\MasterLocation;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;
use App\Models\User;
use Carbon\Carbon;
use Maatwebsite\Excel\Facades\Excel;

class DriverController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        // return $request->all();
        $company = [];
        if ($request->has('company')) {
            $company_get = $request->input('company');
            if (is_array($company_get)) {
                for ($i=0; $i < count($company_get); $i++) { 
                    $company_id = Company::findOrFail($company_get[$i])->code ?? null;
                    $company[] = $company_id;
                    // $company[] = $company_get[$i];
                }
            }
        }

        $departement = [];
        if ($request->has('departement')) {
            $departement_get = $request->input('departement');
            if (is_array($departement_get)) {
                for ($i=0; $i < count($departement_get); $i++) { 
                    $departement[] = $departement_get[$i];
                }
            }
        }

        $sim_type = [];
        if ($request->has('sim_type')) {
            $sim_type_get = $request->input('sim_type');
            if (is_array($sim_type_get)) {
                for ($i=0; $i < count($sim_type_get); $i++) { 
                    $sim_type[] = $sim_type_get[$i];
                }
            }
        }

        $location = [];
        if ($request->has('location')) {
            $location_get = $request->input('location');
            if (is_array($location_get)) {
                for ($i=0; $i < count($location_get); $i++) { 
                    $location[] = $location_get[$i];
                }
            }
        }

        set_time_limit(0);

        // $companies = $this->getCompany();
        $companies = Company::get();
        $departments = [];
        $sim_types = TypeSim::select('name','id')->get();
        $locations = [];

        $filter = User::with(['company','department','branch','section','subSection','driver.vehicle','driver.type_sim', 'location'])
        ->where('role', 'Driver')
        ->orderBy('id','desc')
        ->get();

        $fil_arr = [];
        foreach ($filter as $key => $fil) {
            if (is_array($sim_type) && count($sim_type)) {
                if (in_array($fil->driver->type_sim_id, $sim_type)) {
                    $fil_arr[] = $fil->id;
                }
            }

            // if (is_array($location) && count($location)) {
            //     if (in_array($fil->driver->master_location_id, $location)) {
            //         $fil_arr[] = $fil->id;
            //     }
            // }
        }
        // return $location;
        // $data = Driver::orderBy('id', 'desc')->with('user', 'vehicle.category')->get();
        $data = User::with(['company','location','department','branch','section','subSection','driver.vehicle','driver.type_sim', 'location'])
        ->where(function($q) use($company,$location,$departement,$fil_arr){
            if (is_array($company) && count($company)) {
                $q->whereIn('company_id',$company);
            }
            if (is_array($location) && count($location)) {
                $q->whereIn('location_id',$location);
            }
            if (is_array($departement) && count($departement)) {
                $q->whereIn('department_id',$departement);
            }
            if (is_array($fil_arr) && count($fil_arr)) {
                $q->whereIn('id',$fil_arr);
            }
        })
        ->where('role', 'ilike' ,'%driver%')
        ->orderBy('id','desc')
        ->get();
        

        foreach ($data as $key => $datas) {
            $datas['driving_license'] = $datas->driver->driving_license;
            if (isset($datas->driver->sim_validity_period)) {
                $start_time = \Carbon\Carbon::now();
                $finish_time = \Carbon\Carbon::parse($datas->driver->sim_validity_period);

                $result = $start_time->diffInDays($finish_time, false);
                $datas->driver->expired_day = $result;
            }else{
                $datas->driver->expired_day = 0;
            }
        }

        return response()->json(['data' => $data, 'companies' => $companies, 'departments' => $departments, 'sim_types' => $sim_types, 'locations' => $locations]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $data = Driver::orderBy('id', 'asc')->get();

        return response()->json($data);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        try{
            $data = $request->all();
            $Question = Driver::create($data);

            return response()->json(['success' => true, 'message' => 'Berhasil input data']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        // 
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $data = Driver::findOrFail($id);
        return response()->json($data);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        try{
            $data = $request->all();
            $Question = Driver::findOrFail($id)->update($data);

            return response()->json(['success' => true, 'message' => 'Berhasil edit data']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $data = Driver::findOrFail($id);
        $data->delete();
        $response = array(
            'success' => true,
            'message' => 'Berhasil hapus data'
        );

        return response()->json($response);
    }

    public function sync()
    {
        $data = Http::get('http://lestarijayaraya.co.id:8009/api/karyawan/api_tampil?api_key=Y4BoXmabMlK7BQx&employee_status=1&employe_status=1&jabatan=driver')->json();
        // return $data;
        $user_login_code = array();

        try {
            foreach ($data as $key => $user_login) {
                if ($user_login['user_id']) {
                    $userLjr = User::where('code', $user_login['user_id'])->first()->id ?? null;
                    if ($userLjr) {
                        $dataUser = User::where('code', $user_login['user_id'])->first();
                        $dataUser->role = 'Driver';
                        $dataUser->save();

                        array_push($user_login_code, $userLjr);

                        $driver = Driver::where('user_id', $userLjr)->first();
                        $dataDriver['user_id'] = $userLjr;
                        $dataDriver['join_date'] = now();
                        $dataDriver['status'] = 1;
                        $dataDriver['available'] = 1;
                        $dataDriver['emergency_contact'] = $user_login['emergency_number'];
                        $dataDriver['emergency_name'] = $user_login['full_name'];
                        if ($user_login['born_date'] == "0000-00-00") {
                            $user_login['born_date'] = null;
                        }
                        $dataDriver['birth'] = $user_login['born_date'];
                        $dataDriver['master_location_id'] = MasterLocation::where('code', $user_login['location_id'])->first()->id ?? null;
                        if ($user_login['validity_driving_license'] == "0000-00-00") {
                            $user_login['validity_driving_license'] = null;
                        }
                        $dataDriver['sim_validity_period'] = $user_login['validity_driving_license'];
                        $dataDriver['driving_license'] = $user_login['driving_license_number'] ?? null;
                        $dataDriver['type_sim_id'] = TypeSim::where('name', $user_login['type_driving_license'])->first()->id ?? null;
                        if (!$driver) {
                            Driver::create($dataDriver);
                        }else{
                            $driver->update($dataDriver);
                        }
                    }
                }
            }

            $delUser = Driver::whereNotIn('user_id', $user_login_code)->delete();

            return response(['status' => true, 'message' => 'berhasil singkronkan data'], 200);
        } catch (\Throwable $th) {
            return response(['status' => false, 'message' => $th->getMessage()], 200);
        }
    }

    public function getLocation(Request $request){
        $company = $request->input('params');
        $departement = [];
        $departement_id = [];
        $departement_no = 0;
        $location = [];
        $location_id = [];
        $location_no = 0;
        if (is_array($company)) {
            for ($i=0; $i < count($company); $i++) { 
                $name_company = $company[$i]['name'];
                $id_company = $company[$i]['code'];

                // $data = Http::get('http://lestarijayaraya.co.id:8009/api/karyawan/api_tampil?api_key=Y4BoXmabMlK7BQx&employee_status=1&employe_status=1&company='.$name_company)->json();
                $data = User::where('company_id', $id_company)->get();
                // if (is_array($data)) {
                    // return $data;
                    foreach ($data as $key => $datas) {
                        $id_departement = $datas['department_id'];        
                        $name_departement = $datas['department_name'];
                        if (!isset($departement_id[$id_departement])) {
                            $departement_id[$id_departement] = $id_departement;
                            $departement[$departement_no]['id'] = $id_departement;        
                            $departement[$departement_no]['name'] = $name_departement;
                            $departement_no++;
                        }
                        
                        $id_location = $datas['location_id'];        
                        $name_location = $datas['location_name'];
                        if (!isset($location_id[$id_location])) {
                            $location_id[$id_location] = $id_location;
                            $location[$location_no]['id'] = $id_location;        
                            $location[$location_no]['name'] = $name_location;
                            $location_no++;
                        }
                    }
                // }
            }
        }
        // $company = Company::findOrFail($company_get[$i])->name;

        return response()->json(['departement' => $departement, 'location' => $location]);
    }

    public function getCompany(){
        $company = [];
        $company_id = [];
        $company_no = 0;
        
        $data = Http::get('http://lestarijayaraya.co.id:8009/api/karyawan/api_tampil?api_key=Y4BoXmabMlK7BQx&employee_status=1&employe_status=1')->json();

        if (is_array($data)) {
            foreach ($data as $key => $datas) {
                $id_company = $datas['company_id'];        
                $name_company = $datas['company_name'];
                if (!isset($company_id[$id_company])) {
                    $company_id[$id_company] = $id_company;
                    $company[$company_no]['id'] = $id_company;        
                    $company[$company_no]['name'] = $name_company;
                    $company_no++;
                }
            }
        }

        return $company;
    }

    public function export(Request $request)
    {
        $company = [];
        if ($request->has('company')) {
            $company_get = $request->input('company');
            if (is_array($company_get)) {
                for ($i=0; $i < count($company_get); $i++) { 
                    $company_id = Company::findOrFail($company_get[$i])->code ?? null;
                    $company[] = $company_id;
                    // $company[] = $company_get[$i];
                }
            }
        }

        $departement = [];
        if ($request->has('departement')) {
            $departement_get = $request->input('departement');
            if (is_array($departement_get)) {
                for ($i=0; $i < count($departement_get); $i++) { 
                    $departement[] = $departement_get[$i];
                }
            }
        }

        $sim_type = [];
        if ($request->has('sim_type')) {
            $sim_type_get = $request->input('sim_type');
            if (is_array($sim_type_get)) {
                for ($i=0; $i < count($sim_type_get); $i++) { 
                    $sim_type[] = $sim_type_get[$i];
                }
            }
        }

        $location = [];
        if ($request->has('location')) {
            $location_get = $request->input('location');
            if (is_array($location_get)) {
                for ($i=0; $i < count($location_get); $i++) { 
                    $location[] = $location_get[$i];
                }
            }
        }

        $filter = User::with(['company','department','branch','section','subSection','driver.vehicle','driver.type_sim', 'location'])
        ->where('role', 'Driver')
        ->orderBy('id','desc')
        ->get();
        
        $fil_arr = [];
        foreach ($filter as $key => $fil) {
            if (is_array($sim_type) && count($sim_type)) {
                if (in_array($fil->driver->type_sim_id, $sim_type)) {
                    $fil_arr[] = $fil->id;
                }
            }

            // if (is_array($location) && count($location)) {
            //     if (in_array($fil->driver->master_location_id, $location)) {
            //         $fil_arr[] = $fil->id;
            //     }
            // }
        }

        $data = User::with(['company','location','department','branch','section','subSection','driver.vehicle','driver.type_sim', 'location'])
        ->where(function($q) use($company,$location,$departement,$fil_arr){
            if (is_array($company) && count($company)) {
                $q->whereIn('company_id',$company);
            }
            if (is_array($location) && count($location)) {
                $q->whereIn('location_id',$location);
            }
            if (is_array($departement) && count($departement)) {
                $q->whereIn('department_id',$departement);
            }
            if (is_array($fil_arr) && count($fil_arr)) {
                $q->whereIn('id',$fil_arr);
            }
        })
        ->where('role', 'ilike' ,'%driver%')
        ->orderBy('id','desc')
        ->get();
        

        foreach ($data as $key => $datas) {
            $datas['driving_license'] = $datas->driver->driving_license;
            if (isset($datas->driver->sim_validity_period)) {
                $start_time = \Carbon\Carbon::now();
                $finish_time = \Carbon\Carbon::parse($datas->driver->sim_validity_period);

                $result = $start_time->diffInDays($finish_time, false);
                $datas->driver->expired_day = $result;
            }else{
                $datas->driver->expired_day = 0;
            }
        }

        return Excel::download(new ExportDriver($data), 'List Data Driver.xlsx');
    }
}
