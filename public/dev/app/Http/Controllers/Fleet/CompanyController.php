<?php

namespace App\Http\Controllers\Fleet;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Fleet\Company;
use App\Models\Fleet\Currency;
use App\Models\Fleet\ChartMaster;
use App\Models\User;
use DB;

class CompanyController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $data = Company::with('currency')->orderBy('id','desc')->get();

        return response()->json($data);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $currency = Currency::pluck('currabrev','id');
        $account = ChartMaster::whereHas('account', function($q){
            $q->where('pandl', 0);
        })
        ->with('account')
        ->select("accountcode as code", DB::raw("CONCAT(chart_masters.accountcode,' - ',chart_masters.accountname) as text"))
        ->orderBy('code','asc')
        ->pluck('text','code');

        $account2 = ChartMaster::whereHas('account', function($q){
            $q->where('pandl', 1);
        })
        ->with('account')
        ->select("accountcode as code", DB::raw("CONCAT(chart_masters.accountcode,' - ',chart_masters.accountname) as text"))
        ->orderBy('code','asc')
        ->pluck('text','code');

        return response()->json(['currency' => $currency, 'account' => $account, 'account2' => $account2]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        try{
            $company = User::get()->groupBy('company_id');
            $code = [];
            foreach ($company as $key => $value) {
                $com = Company::where('code', $value->first()->company_id)->first();
                if ($com) {
                    $com->name = $value->first()->company_name;
                    $com->save();
                    array_push($code, $value->first()->company_id);
                }else {
                    $data['code'] = $value->first()->company_id;
                    $data['name'] = $value->first()->company_name;
                    $create = Company::create($data);
                    array_push($code, $value->first()->company_id);
                }
            }
            
            $delete = Company::whereNotIn('code', $code)->delete();
            // $data = $request->all();
            // $company = Company::create($data);

            return response()->json(['success' => true, 'message' => 'Berhasil Singkronkan data']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $data = Company::with('department')->find($id);

        return response()->json($data);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $data = Company::find($id);
        $currency = Currency::pluck('currabrev','id');
        $account = ChartMaster::whereHas('account', function($q){
            $q->where('pandl', 0);
        })
        ->with('account')
        ->select("accountcode as code", DB::raw("CONCAT(chart_masters.accountcode,' - ',chart_masters.accountname) as text"))
        ->orderBy('code','asc')
        ->pluck('text','code');

        $account2 = ChartMaster::whereHas('account', function($q){
            $q->where('pandl', 1);
        })
        ->with('account')
        ->select("accountcode as code", DB::raw("CONCAT(chart_masters.accountcode,' - ',chart_masters.accountname) as text"))
        ->orderBy('code','asc')
        ->pluck('text','code');

        return response()->json(['data' => $data, 'currency' => $currency, 'account' => $account, 'account2' => $account2]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        try{
            $data = $request->all();
            $company = Company::find($id)->update($data);

            return response()->json(['success' => true, 'message' => 'Berhasil update data']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        try {
            $data = Company::destroy($id);

            return response()->json(['success' => true, 'message' => 'Berhasil delete data']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }
}
