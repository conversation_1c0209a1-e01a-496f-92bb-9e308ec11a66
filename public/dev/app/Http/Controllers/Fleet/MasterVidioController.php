<?php

namespace App\Http\Controllers\Fleet;

use App\Http\Controllers\Controller;
use App\Models\Fleet\HistoryView;
use App\Models\Fleet\MasterVidio;
use App\Models\Fleet\MasterVidioHistory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class MasterVidioController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $status = $request->get('status');

        if ($status != null) {
            $data = MasterVidio::orderBy('id', 'desc')->where('status', $status)->get();
        }else{
            $data = MasterVidio::orderBy('id', 'desc')->get();
        }

        foreach ($data as $key => $value) {
            $data[$key]['url_vidio'] = "http://".$request->getHost()."/".$value->vidio;
            if (Auth::check()) {
                $cek_play = MasterVidioHistory::where('user_id',Auth::user()->id)->whereDate('created_at',Carbon::today())->where('vidio_id',$value->id)->count();
                if ($cek_play) {
                    $data[$key]['played_today'] = true;
                }else{
                    $data[$key]['played_today'] = false;
                }
            }else{
                $data[$key]['played_today'] = false;
            }
        }
        return response()->json($data);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        try{
            $data = $request->all();
            if ($request->vidio) {
                $image_parts = explode(";base64,", $request->vidio);
                if ($image_parts) {
                    $image_type_aux = explode("video/", $image_parts[0]);
                    $image_type = $image_type_aux[1];
                    if ($image_type == 'mp4') {
                        $image_base64 = base64_decode($image_parts[1]);
                        $folderPath = 'storage/master-vidio/';
                        $imageName = uniqid();
                        $imageFullPath = $folderPath.$imageName.".".$image_type;
                        file_put_contents($imageFullPath, $image_base64);
                        $data['vidio'] = $imageFullPath;
                    }else{
                        return "Format gambar tidak sesuai, Tipe vidio hanya mp4";
                    }
                }
            }
            $masterVidio = MasterVidio::create($data);
            
            return response()->json(['success' => true, 'message' => 'Berhasil input data']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show(Request $request, $id)
    {
        $data = MasterVidio::findOrFail($id);
        foreach ($data as $key => $value) {
            $data[$key]['url_vidio'] = "http://".$request->getHost()."/".$value->vidio;
        }
        return response()->json($data);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit(Request $request, $id)
    {
        $data = MasterVidio::findOrFail($id);
        $data['url_vidio'] = "http://".$request->getHost()."/".$data->vidio;
        return response()->json($data);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        try{
            $data = $request->all();
            if (isset($request->vidio)) {
                try {
                    $image_parts = explode(";base64,", $request->vidio);
                    if ($image_parts) {
                        $image_type_aux = explode("video/", $image_parts[0]);
                        $image_type = $image_type_aux[1];
                        if ($image_type == 'mp4') {
                            $image_base64 = base64_decode($image_parts[1]);
                            $folderPath = 'storage/master-vidio/';
                            $imageName = uniqid();
                            $imageFullPath = $folderPath.$imageName.".".$image_type;
                            file_put_contents($imageFullPath, $image_base64);
                            $data['vidio'] = $imageFullPath;
                        }else{
                            return "Format gambar tidak sesuai, Tipe vidio hanya mp4";
                        }
                    }
                } catch (\Throwable $th) {
                    unset($request->vidio);
                }
            }
            $masterVidio = MasterVidio::findOrFail($id)->update($data);
            
            return response()->json(['success' => true, 'message' => 'Berhasil edit data']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $data = MasterVidio::findOrFail($id);
        $data->delete();
        $response = array(
            'success' => true,
            'message' => 'Berhasil hapus data'
        );

        return response()->json($response);
    }

    public function historyView(Request $request)
    {
        try{
            $data = $request->all();
            $data['user_id'] = Auth::user()->id;
            $historyView = HistoryView::create($data);
            
            return response()->json(['success' => true, 'message' => 'Berhasil input data']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    public function history(Request $request)
    {
        if (Auth::check()) {
            $history = new MasterVidioHistory;
            $history->vidio_id = $request->input('vidio_id');
            $history->user_id = Auth::user()->id;
            $history->save();

            return response()->json(['success' => true, 'message' => 'Berhasil input data']);
        }else{
            return response()->json(['success' => false, 'message' => 'error authentication'], 500);
        }
    }
}
