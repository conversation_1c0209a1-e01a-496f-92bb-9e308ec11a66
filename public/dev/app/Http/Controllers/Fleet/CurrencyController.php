<?php

namespace App\Http\Controllers\Fleet;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Fleet\Currency;
use App\Models\Fleet\Country;
use Illuminate\Support\Facades\Validator;
use DB;

class CurrencyController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $data = Currency::orderBy('id','desc')->get();
        return response()->json($data);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $country = Country::select("code as value", DB::raw("CONCAT(countries.code,' - ',countries.name) as text"))->orderBy('value','asc')->pluck('text', 'value');

        return response()->json($country);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'currency_data' => 'required',
                'country' => 'required',
                'hundreds_name' => 'required',
                'decimal_places' => 'required',
                'rate' => 'required',
                'web_cart' => 'required',
            ]);
    
            if ($validator->fails()) {
                return response()->json($validator->errors(), 500);
            }

            $data = $request->except(['currency_data']);
            $data['currency'] = explode('-', $request->input('currency_data'))[0];
            $data['currabrev'] = explode('-', $request->input('currency_data'))[1];
            
            $divisi = Currency::create($data);

            return response()->json(['success' => true, 'message' => 'Berhasil input data']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $data = Currency::where('id',$id)->select("currencies.*", DB::raw("CONCAT(currencies.currency,'-',currencies.currabrev) as currency_data"))->first();

        return response()->json($data);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        try {
            $validator = Validator::make($request->all(), [
                'currency_data' => 'required',
                'country' => 'required',
                'hundreds_name' => 'required',
                'decimal_places' => 'required',
                'rate' => 'required',
                'web_cart' => 'required',
            ]);
    
            if ($validator->fails()) {
                return response()->json($validator->errors(), 500);
            }

            $data = $request->except(['currency_data']);
            $data['currency'] = explode('-', $request->input('currency_data'))[0];
            $data['currabrev'] = explode('-', $request->input('currency_data'))[1];
            
            $divisi = Currency::find($id)->update($data);

            return response()->json(['success' => true, 'message' => 'Berhasil update data']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        try {
            $data = Currency::destroy($id);

            return response()->json(['success' => true, 'message' => 'Berhasil delete data']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }
}
