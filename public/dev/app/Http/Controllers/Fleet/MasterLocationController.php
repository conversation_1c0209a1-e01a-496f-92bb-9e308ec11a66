<?php

namespace App\Http\Controllers\Fleet;

use App\Http\Controllers\Controller;
use App\Models\Fleet\MasterLocation;
use App\Models\User;
use Illuminate\Http\Request;

class MasterLocationController extends Controller
{
    public function index()
    {
        $data = MasterLocation::orderBy('id', 'desc')->get();
        return response()->json($data);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        try{
            $lokasi = User::get()->groupBy('location_id');
            $code = [];
            foreach ($lokasi as $key => $value) {
                $com = MasterLocation::where('code', $value->first()->location_id)->first();
                if ($com) {
                    $com->name = $value->first()->location_name;
                    $com->save();
                    array_push($code, $value->first()->location_id);
                }else {
                    $data['code'] = $value->first()->location_id;
                    $data['name'] = $value->first()->location_name;
                    $create = MasterLocation::create($data);
                    array_push($code, $value->first()->location_id);
                }
            }
            $delete = MasterLocation::whereNotIn('code', $code)->delete();
            // $data = $request->all();
            // $masterLocation = MasterLocation::create($data);
            
            return response()->json(['success' => true, 'message' => 'Berhasil Singkronkan data']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $data = MasterLocation::findOrFail($id);
        return response()->json($data);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        try{
            $data = $request->all();
            $masterLocation = MasterLocation::findOrFail($id)->update($data);
            
            return response()->json(['success' => true, 'message' => 'Berhasil edit data']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $data = MasterLocation::findOrFail($id);
        $data->delete();
        $response = array(
            'success' => true,
            'message' => 'Berhasil hapus data'
        );

        return response()->json($response);
    }
}
