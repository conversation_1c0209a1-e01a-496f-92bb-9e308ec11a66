<?php

namespace App\Http\Controllers\Fleet;

use App\Exports\ExportUser;
use App\Http\Controllers\Controller;
use App\Models\User;
use Spatie\Permission\Models\Role;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use App\Models\Fleet\Company;
use App\Models\Fleet\Department;
use DB;
use Faker\Calculator\Ean;
use Maatwebsite\Excel\Facades\Excel;

class UserController extends Controller
{
    public function index(Request $request)
    {
        $columns = ['full_name', 'email', 'nip', 'company_name', 'department_name', 'section_name', 'sub_section_name', 'role'];
        $keyword = $request->get('keywoard');
        $user = User::where(function($result) use ($keyword,$columns){
                            foreach($columns as $column)
                            {
                                if($keyword != ''){
                                    $result->orWhere($column,'LIKE','%'.$keyword.'%');
                                }
                            }
                        })
                        ->where(function($query) use($request){
                            if ($request->get('company_id')) {
                                $id_company = Company::where('id', $request->get('company_id'))->pluck('code');
                                $query->whereIn('company_id', $id_company);
                            }
                            if ($request->get('department_id')) {
                                $id_company = Department::where('id', $request->get('department_id'))->pluck('code');
                                $query->whereIn('department_id', $id_company);
                            }
                        })
                        ->orderBy('id', 'desc')->get();
        
        return $user;
    }

    public function create()
    {
        $role = Role::whereNotIn('name',['Driver'])->pluck('name','name'); 
        $company = Company::all();
        $department = department::all();

        return response()->json(['role' => $role, 'company' => $company, 'department' => $department]);
    }

    public function delete($id)
    {
        $user = User::destroy($id);

        return "Delete User Success";
    }

    public function store(Request $request)
    {
        $user = new User();
        $user->first_name = $request->get('first_name');
        $user->last_name = $request->get('last_name');
        $user->email = $request->get('email');
        $user->company_id = $request->get('company_id');
        $user->branch_id = $request->get('branch_id');
        $user->birth = $request->get('birth');
        $user->department_id = $request->get('department_id');
        $user->section_id = $request->get('section_id');
        $user->sub_section_id = $request->get('sub_section_id');
        $user->role = $request->get('role');
        $password = $request->get('password');
        if ($password) {
            $user->password = Hash::make($password);
        }
        
        $user->save();

        $user->assignRole($request->input('role'));
        
        return "Update User Successfully!";
    }

    public function edit($id)
    {
        $user = User::findOrFail($id);

        return $user;
    }

    public function update(Request $request, $id)
    {
        $user = User::findOrFail($id);
        $user->first_name = $request->get('first_name');
        $user->last_name = $request->get('last_name');
        $user->email = $request->get('email');
        $user->department_id = $request->get('department_id');
        $user->section_id = $request->get('section_id');
        $user->sub_section_id = $request->get('sub_section_id');
        if ($request->has('role')) {
            $user->role = $request->get('role');
        }
        $password = $request->get('password');
        if ($password) {
            $user->password = Hash::make($password);
        }
        
        $user->save();
        
        if ($request->has('role')) {
            DB::table('model_has_roles')->where('model_id',$id)->delete();
            $user->assignRole($request->input('role'));
        }
        
        return "Update User Successfully!";
    }

    public function forgotPassword(Request $request)
    {
        $data = $request->all();
        $user = User::findOrFail(Auth::user()->id);

        if ($user->email == null) {
            return response([
                'message' => 'Masukkan email terlebih dahulu.',
                'success' => false
            ], 200);
        }

        if (Hash::check($user->password, $data['oldPassword'])) {
            $user->password = $data['newPassword'];
            $user->save();
            return response()->json(['success' => true, 'message' => 'Password berhasil diubah']);
        }else{
            return response()->json(['success' => true, 'message' => 'Password tidak sesuai']);
        }
    }

    public function photoProfile(Request $request)
    {
        try {
            if ($request->image) {
                $image_parts = explode(";base64,", $request->image);
                if ($image_parts) {
                    $image_type_aux = explode("image/", $image_parts[0]);
                    $image_type = $image_type_aux[1];
                    if ($image_type == 'jpg' || $image_type == 'jpeg' || $image_type == 'png') {
                        $image_base64 = base64_decode($image_parts[1]);
                        $folderPath = 'storage/profile/';
                        $imageName = uniqid();
                        $imageFullPath = $folderPath.$imageName.".".$image_type;
                        file_put_contents($imageFullPath, $image_base64);
                        $users['photo'] = $imageFullPath;
                    }else{
                        return "Format gambar tidak sesuai, Tipe gambar hanya jpg, jpeg, png";
                    }
                }
            }
            $user = User::findOrFail(Auth::user()->id)->update($users);
            return response()->json(['success' => true, 'message' => 'foto profile berhasil diubah']);
        } catch (\Throwable $th) {
            return response()->json(['success' => false, 'message' => $th]);
        }
    }

    public function export()
    {
        // $data = User::get();

        // return view('export.user', ['data' => $data]);
        return Excel::download(new ExportUser(), 'List Data User.xlsx');
    }

    public function updateEmail(Request $request)
    {
        $user = User::findOrFail(Auth::user()->id);
        $user->email = $request->newEmail;
        $user->save();

        return response()->json(['success' => true, 'message' => 'email berhasil diubah']);
    }
}
