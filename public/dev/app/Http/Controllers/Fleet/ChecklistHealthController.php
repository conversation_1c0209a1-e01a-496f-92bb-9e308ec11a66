<?php

namespace App\Http\Controllers\Fleet;

use App\Http\Controllers\Controller;
use App\Models\Fleet\AnswerQuestion;
use App\Models\Fleet\AnswerQuestionDetail;
use App\Models\Fleet\AnswerQuestionUser;
use App\Models\Fleet\CategoryQuestion;
use App\Models\Fleet\Driver;
use App\Models\Fleet\Question;
use App\Models\Fleet\QuestionMark;
use App\Models\Fleet\StatusVehicle;
use App\Models\Fleet\LocationChecklist;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ChecklistHealthController extends Controller
{
    public function index()
    {
        $auth = Auth::user()->role;
        $data = AnswerQuestionUser::where('user_id', Auth::user()->id)->with('answerQuestionDetail', 'statusVehicle')->where('slug', 'checklist-health')->orderBy('id', 'desc')->get();
        foreach ($data as $key => $res) {
            $sv = $res->statusVehicle->start ?? 0;
            if ($res['total_point'] <= $sv) {
                $data[$key]['ready'] = 0;
            }else{
                $data[$key]['ready'] = 1;
            }
        }

        return response()->json(['data' => $data, 'user' => $auth]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create(Request $request)
    {
        // $route = $request->slug;
        // if ($route) {
            $age = now()->diffInYears(Auth::user()->birth);
            $data = CategoryQuestion::where('slug', 'checklist-health')->orderBy('id', 'asc')->get();
            // $data = CategoryQuestion::where('slug', 'checklist-health')->orderBy('id', 'asc')->with('question.answer')->get();
            $no = 0;
            $question_fix = [];
            foreach ($data as $key => $value) {
                foreach ($value->question as $keys => $quest) {
                    if (!is_null($quest->age_above) && $age >= $quest->age_above) {
                        $question_fix[$no] = $quest->where('id',$quest->id)->with('answer')->first();
                        $no++;
                    }elseif (!is_null($quest->age_under) && $age < $quest->age_under) {
                        $question_fix[$no] = $quest->where('id',$quest->id)->with('answer')->first();
                        $no++;
                    }elseif (is_null($quest->age_under) && is_null($quest->age_above)){
                        $question_fix[$no] = $quest->where('id',$quest->id)->with('answer')->first();
                        $no++;
                    }
                }
                unset($value->question);
                $value->question = $question_fix;
            }
            return response()->json($data);
        // }else{
        //     return response()->json(['success' => false, 'messages' => 'not found!']);
        // }
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        try{
            $data = $request->all();
            // return $data;
            $answerQuestionUser['status'] = 0;
            $answerQuestionUser['user_id'] = Auth::user()->id;
            $answerQuestionUser['slug'] = 'checklist-health';
            $answerQuestionUser['location_checklist_id'] = $data[0]['location_checklist_id'];
            $answerUser = AnswerQuestionUser::create($answerQuestionUser);
            
            $accompanied_foto = false;
            $question_accompanied_foto = array();
            foreach ($data as $key => $value) {
                $question = Question::findOrFail($value['question_id']);
                if ($question->accompanied_photo == 1 && !(isset($value['image']))) {
                    $accompanied_foto = true;
                    $question_accompanied_foto[$key] = $value['question_id'];
                }
            }

            $total_poin = 0;
            $point_result = 0;
            $danger = (bool)false;
            $ket_danger = [];
            if ($accompanied_foto == false) {
                foreach ($data as $key => $res) {
                    $question = Question::findOrFail($res['question_id']);
                    $answer = AnswerQuestion::findOrFail($res['answer_id']);
                    $poin_tertinggi = AnswerQuestion::where('question_id',$res['question_id'])->orderBy('point','desc')->first();
                    if ($poin_tertinggi) {
                        $total_poin += $poin_tertinggi->point;
                    }
                    
                    $res['question'] = $question->question;
                    $res['answer'] = $answer->answer;
                    $res['point'] = $answer->point;
                    $res['note_checklist'] = $answer->note_checklist;

                    if ($answer->danger == 1) {
                        $danger = (bool)true;
                        $ket_danger[] = $question->question;
                    }

                    if (isset($res['image'])) {
                        $image_parts = explode(";base64,", $res['image']);
                        if ($image_parts) {
                            $image_type_aux = explode("image/", $image_parts[0]);
                            $image_type = $image_type_aux[1];
                            if ($image_type == 'jpg' || $image_type == 'jpeg' || $image_type == 'png') {
                                $image_base64 = base64_decode($image_parts[1]);
                                $folderPath = 'storage/cheklist/';
                                $imageName = uniqid();
                                $imageFullPath = $folderPath.$imageName.".".$image_type;
                                file_put_contents($imageFullPath, $image_base64);
                                $res['image'] = $imageFullPath;
                            }else{
                                return "Format gambar tidak sesuai, Tipe gambar hanya jpg, jpeg, png";
                            }
                        }
                    }
                    $res['answer_question_user_id'] = $answerUser->id;
                    $res['answer_question_id'] = $res['answer_id'];
                    // dd($res);
                    // $CategoryQuestion[$key] = AnswerQuestionUser::create($res);
        
                    AnswerQuestionDetail::create($res);
                    $point_result += $answer->point;
                }

                if ($point_result > 0) {
                    $total_persen = ($point_result/$total_poin)*100;
                }else{
                    $total_persen = 0;
                }
                
                // foreach ($CategoryQuestion as $answer) {
                //     // dd($answer->point);
                //     $point += (int)$answer->point;
                // }
        
                // // dd($point);
                // try {
                //     $color = StatusVehicle::where('start', '<=', $point)->where('to', '>=', $point)->first()->id;
                // } catch (\Throwable $th) {
                //     $color = StatusVehicle::orderBy('to', 'desc')->first()->id;
                // }
        
                // $color = StatusVehicle::where('start', '<=', $point)->where('to', '>=', $point)->first()->id;
                $message = "";
                if ($danger) {
                    $color = StatusVehicle::where('name','red')->first()->id;
                    $status_color = 'red';
                    $message = 'Anda tidak lulus check kesehatan karena kriteria berikut bermasalah:\n';
                    foreach ($ket_danger as $key_danger => $val_danger) {
                        $message .= '- '.$val_danger.'\n';
                    }
                }elseif ($total_persen == 100) {
                    $color = StatusVehicle::where('name','green')->first()->id;
                    $status_color = 'green';
                    $message = 'Anda lulus check kesehatan silahkan lanjut check kendaraan';
                }elseif ($total_persen < 90) {
                    $color = StatusVehicle::where('name','red')->first()->id;
                    $status_color = 'red';
                    $message = 'Anda tidak lulus check kesehatan karena hasil nilai kesehatan anda rendah';
                }elseif ($total_persen >= 90 && $total_persen <= 99) {
                    $color = StatusVehicle::where('name','yellow')->first()->id;
                    $status_color = 'yellow';
                    $message = 'Anda lulus check kesehatan silahkan lanjut check kendaraan';
                }else{
                    $color = StatusVehicle::where('name','green')->first()->id;
                    $status_color = 'green';
                    $message = 'Anda lulus check kesehatan silahkan lanjut check kendaraan';
                }
                $qPoint['status_vehicle_id'] = $color;
                $qPoint['total_point'] = $point_result;
                $qPoint['input_date'] = now();
                $qPoint['status'] = 1;
                AnswerQuestionUser::findOrFail($answerUser->id)->update($qPoint);
                
                return response()->json(['success' => true, 'status' => $status_color, 'poin' => round($total_persen,0), 'message' => $message, 'question' => '']);
            }else {
                return response()->json(['success' => false, 'status' => '', 'poin' => 0, 'message' => 'jawaban harus menyertakan gambar', 'question' => $question_accompanied_foto]);
            }

        } catch (\Exception $e) {
            return response()->json(['success' => false, 'status' => '', 'poin' => 0, 'message' => $e->getMessage(), 'question' => ''], 500);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        try {
            $data = AnswerQuestionDetail::where('answer_question_user_id', $id)->with('answerQuestion', 'answerUser', 'questions.categoryQuestion')->get();
            foreach ($data as $key => $value) {
                $answer_question = AnswerQuestion::where('question_id', $value->question_id)->get();
                $data[$key]['questions']['list_answer_question'] = $answer_question;
            }
            $status = AnswerQuestionUser::with('statusVehicle')->findOrFail($id);
            
            return response()->json(['data' => $data, 'status' => $status]);
        } catch (\Throwable $th) {
            return response()->json(['success' => false, 'message' => $th], 500);
        }
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $data = AnswerQuestionUser::findOrFail($id);
        if ($data['total_point'] <= $data->statusVehicle->start) {
            $data['ready'] = 0;
        }else{
            $data['ready'] = 1;
        }

        return response()->json($data);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        try{
            $data = $request->all();
            $CategoryQuestion = AnswerQuestionUser::findOrFail($id)->update($data);
            
            return response()->json(['success' => true, 'message' => 'Berhasil edit data']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $data = QuestionMark::findOrFail($id);
        $data->delete();
        $response = array(
            'success' => true,
            'message' => 'Berhasil hapus data'
        );

        return response()->json($response);
    }

    public function detailCheck($id){
        $data = AnswerQuestionUser::find($id);
        if (!$data) {
            return response()->json(['success' => false, 'message' => 'terjadi kesalahan']);
        }

        $output['waktu_checklist'] = $data->input_date;
        $output['lokasi_kode_qr'] = null;
        $output['lokasi_name'] = null;
        if ($data->location_checklist_id) {
            $location = LocationChecklist::find($data->location_checklist_id);
            if ($location) {
                $output['lokasi_kode_qr'] = $location->code;
                $output['lokasi_name'] = $location->name;
            }
        }
        if (isset($data->statusVehicle->name)) {
            $output['status'] = $data->statusVehicle->name;
        }else{
            $output['status'] = null;
        }
        $output['note'] = $data->note;
        $output['poin'] = $data->total_point;

        return response()->json(['success' => true, 'data' => $output]);
    }
}
