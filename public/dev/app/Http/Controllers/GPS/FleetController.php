<?php

namespace App\Http\Controllers\GPS;

use App\Http\Controllers\Controller;
use App\FleetMaster\Vehicle;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use App\Models\Fleet\ObjectGetLocation;
use App\Models\Fleet\ObjectGetLocationDetail;

class FleetController extends Controller
{
    public function userGetObject()
    {
        try {
            $vehicle = Vehicle::whereNotNull('gps_tokens')->get();

            foreach ($vehicle as $key => $data) {
                $cmd = 'OBJECT_GET_LOCATIONS,'.$data->gps_tokens;
                $response = Http::get('http://p-adv.net/api/api.php', [
                    'api' => 'user',
                    'ver' => 1.7,
                    'key' => env('LJR_KEY'),
                    'cmd' => $cmd,
                ]);

                if ($response->successful()) {
                    $json = $response->json();
                    if (isset($json[$data->gps_tokens])) {
                        $object_head = $json[$data->gps_tokens];
                        $object_head['imei'] = $data->gps_tokens;
                        $object_param = $object_head['params'];
                        unset($object_head['params']);

                        $object = ObjectGetLocation::create($object_head);

                        $object_param['object_id'] = $object->id;
                        $detail = ObjectGetLocationDetail::create($object_param);

                        $update = Vehicle::find($data->id);
                        $update->lat = $object_head['lat'];
                        $update->lng = $object_head['lng'];
                        $update->altitude = $object_head['altitude'];
                        $update->angle = $object_head['angle'];
                        $update->speed = $object_head['speed'];
                        $update->loc_valid = $object_head['loc_valid'];
                        $update->save();
                    }
                }
            }
            
            return response()->json(['success' => true, 'message' => 'Berhasil Input Data.']);
        } catch (\Throwable $th) {
            return response()->json(['success' => false, 'message' => 'Gagal Input Data.']);
        }
    }
}
