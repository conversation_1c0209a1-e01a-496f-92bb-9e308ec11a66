<template>
  <div>
    <div class="card shadow mb-4">
      <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary float-left">
          Finance Check
        </h6>
      </div>
      <div class="card-body pb-5">
        <div class="row">
          <div class="col-md-6">
            <div class="form-group">
              <label class="form-label">
                Cari nomor polisi
              </label>
              <ValidationObserver v-slot="{ handleSubmit }">
                <form @submit.prevent="handleSubmit(formSearchNopol)">
                  <ValidationProvider
                    name="nopol"
                    rules="required|min:3"
                    v-slot="{ errors, classes }"
                  >
                    <div class="d-flex" style="gap: 5px;">
                      <input
                        type="text"
                        class="form-control"
                        name="admin"
                        placeholder="Masukkan nomor polisi"
                        v-model="dataSearch.nopol"
                      />
                      <button class="btn btn-primary">
                        <i class="fas fa-search"></i>
                      </button>
                    </div>
                    <span class="text-error mt-2">{{ errors[0] }}</span>
                  </ValidationProvider>
                </form>
              </ValidationObserver>
            </div>
          </div>
        </div>
        <div class="mt-2" v-if="data && data.id">
          <div class="col-md-6">
            <div class="card">
              <div class="card-body p-3">
                <div class="mb-3">
                  <div style="font-weight: 500; color: grey;">
                    Nomor Polisi
                  </div>
                  <div style="font-weight: 600;">{{ data.license_no }}</div>
                </div>
                <div class="mb-3">
                  <div style="font-weight: 500; color: grey;">
                    Driver Terakhir
                  </div>
                  <div class="">
                    <div>
                      Nama Lengkap :
                      <span style="font-weight: 600;">{{
                        data.driver_name
                      }}</span>
                    </div>
                    <div>
                      Username :
                      <span style="font-weight: 600;">{{
                        data.user.username
                      }}</span>
                    </div>
                    <div>
                      Email :
                      <span style="font-weight: 600;">{{
                        data.user.email
                      }}</span>
                    </div>
                  </div>
                </div>
                <div>
                  <div style="font-weight: 500; color: grey;">
                    KM Terakhir
                  </div>
                  <div style="font-weight: 600;">{{ data.km_actual }}</div>
                </div>
              </div>
            </div>
          </div>
          <div class="">
            <button
              class="btn btn-primary"
              @click="() => handleResetKendaraan()"
              :disabled="
                data.answer_question_user.length > 0
                  ? data.answer_question_user[0].type == 2
                    ? true
                    : false
                  : false
              "
            >
              <i class="fas fa-sync-alt"></i> Reset Kendaraan
            </button>
            <button
              class="btn btn-success"
              @click="() => handleResetKendaraan('km')"
              :disabled="data.km_actual < 1 ? true : false"
            >
              <i class="fas fa-sync-alt"></i> Reset KM
            </button>
            <button class="btn btn-danger ms-2" @click="handleResetSearch">
              <i class="fas fa-times"></i> Batal
            </button>
            <div style="font-size: 12px; font-weight: 500;">
              <span style="color: red;">*</span> Reset kendaraan hanya dapat
              dilakukan jika status kendaraan berangkat
            </div>
          </div>
          <div class="mt-3">
            <h6 class="mb-2">History Checklist Kendaraan</h6>
            <table class="table">
              <thead>
                <tr>
                  <th style="width: 5%; background: rgb(197, 197, 197);">No</th>
                  <th style="width: 15%; background: rgb(197, 197, 197);">
                    Tipe
                  </th>
                  <th style="width: 20%; background: rgb(197, 197, 197);">
                    Dibuat Pada
                  </th>
                  <th style="width: 15%; background: rgb(197, 197, 197);">
                    Status
                  </th>
                  <th style="width: 40%; background: rgb(197, 197, 197);">
                    Keputusan Kordi
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="(item, index) in data.answer_question_user">
                  <td>{{ index + 1 }}</td>
                  <td>{{ item.type == 1 ? "Berangkat" : "Pulang" }}</td>
                  <td>{{ new Date(item.created_at).toLocaleString() }}</td>
                  <td class="text-nowrap">
                    <div
                      v-if="
                        item.vehicle_next_danger != null ||
                        item.vehicle_repair != null
                      "
                      :style="{
                        border: '0px',
                        width: '80px',
                        backgroundColor: '#FF0000',
                        padding: '25px',
                        fontWeight: '900',
                        fontSize: '25px',
                        cursor: 'pointer',
                      }"
                    >
                      <span style="color: black;">{{
                        Math.floor(item.total_point)
                      }}</span>
                    </div>
                    <div
                      v-else-if="item.maintenance_odometer == 0"
                      :style="{
                        border: '0px',
                        width: '80px',
                        backgroundColor: item.status_vehicle
                          ? item.status_vehicle.color
                          : '',
                        padding: '25px',
                        fontWeight: '900',
                        fontSize: '25px',
                        cursor: 'pointer',
                      }"
                    >
                      <span style="color: black;">{{
                        Math.floor(item.total_point)
                      }}</span>
                    </div>
                    <div
                      v-else-if="
                        item.maintenance_odometer == 1 &&
                        item.vehicle_next_danger == 2
                      "
                      :style="{
                        border: '0px',
                        width: '80px',
                        backgroundColor: '#FFFF00',
                        padding: '25px',
                        fontWeight: '900',
                        fontSize: '25px',
                        cursor: 'pointer',
                      }"
                    >
                      <span style="color: black;">{{
                        Math.floor(item.total_point)
                      }}</span>
                    </div>
                  </td>
                  <td>
                    <div>
                      <ul style="list-style-type: none;">
                        <li
                          v-for="riwayat in item.riwayat_tindakan"
                          :key="riwayat.id"
                        >
                          - {{ riwayat.note }} ({{
                            new Date(riwayat.created_at).toLocaleString()
                          }}
                          -
                          {{
                            riwayat.approve_by
                              ? riwayat.approve_by.first_name +
                                " " +
                                riwayat.approve_by.last_name
                              : ""
                          }})
                        </li>
                      </ul>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import "datatables.net-dt/js/dataTables.dataTables";
import "datatables.net-dt/css/jquery.dataTables.min.css";
import FlashMessage from "@smartweb/vue-flash-message";
import * as notify from "../../../utils/notify.js";
import Swal from "sweetalert2";
import axios from "axios";
import {
  ValidationProvider,
  ValidationObserver,
} from "vee-validate/dist/vee-validate.full.esm";

Vue.use(FlashMessage);

Vue.config.productionTip = false;

export default {
  name: "Tables",
  mounted() {},
  components: {
    ValidationProvider,
    ValidationObserver,
  },

  data: () => ({
    dataSearch: {
      nopol: "",
    },
    data: {
      id: null,
      answer_question_user: [],
    },
  }),

  created() {},
  methods: {
    async formSearchNopol() {
      let loader = this.$loading.show({ canCancel: false });
      const res = await axios.get(
        "/checklist-vehicle/search-history-checklist",
        {
          params: {
            keyword: this.dataSearch.nopol,
          },
        }
      );
      this.data = res?.data?.data;
      loader.hide();
    },
    handleResetSearch() {
      this.data = {
        id: null,
        answer_question_user: [],
      };
      this.dataSearch = {
        nopol: "",
      };
    },
    async handleResetKendaraan(type) {
      if ((this.data?.answer_question_user[0]?.type ?? null) == 2 && !type)
        return;
      if (type == "km" && this.data?.km_actual < 1) return;
      Swal.fire({
        html: `
            <div class="pt-4 text-white" style="font-size: 17px">
                <strong>
                    Apakah anda yakin ingin mereset kendaraan ini?
                </strong>
            </div>
            <h4 class="pt-3 text-white">
                <strong>
                    <i class="fas fa-exclamation-triangle"></i>
                </strong>
            </h4>
            `,
        background: "#6A99D0",
        allowOutsideClick: false,
        showCancelButton: true,
        focusConfirm: false,
        borderRadius: "100px",
        confirmButtonColor: "#4F71BE",
        cancelButtonColor: "#d33",
        confirmButtonText: "Ya, Reset Kendaraan",
        cancelButtonText: "Batal",
      }).then(async (result) => {
        if (result.isConfirmed) {
          let res;
          if (type == "km") {
            console.log("masuk", this.data.id);
            res = await axios.post(`/reset_nopol`, {
              asset_id: this.data.id,
            });
          } else {
            res = await axios.post(
              `/checklist-vehicle/search-history-checklist/${this.data.id}`,
              {
                nopol: this.data.license_no,
              }
            );
          }
          if (res.data?.success) {
            this.$bvToast.toast(res?.data?.message, {
              title: "Success",
              variant: "success",
              solid: true,
            });
            this.formSearchNopol();
          } else {
            this.$bvToast.toast("Gagal mereset kendaraan, silahkan coba lagi", {
              title: "Error",
              variant: "danger",
              solid: true,
            });
          }
        }
      });
    },
  },
};
</script>
